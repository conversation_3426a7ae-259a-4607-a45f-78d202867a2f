'use client'

import Link from "next/link"
import { useState } from 'react'

export default function MobileMenu() {
  const [activeKeys, setActiveKeys] = useState<number[]>([])

  const toggleMenu = (key: number) => {
    setActiveKeys((prevKeys) =>
      prevKeys.includes(key)
        ? prevKeys.filter((k) => k !== key)
        : [...prevKeys, key]
    )
  }

  const isActive = (key: number) => activeKeys.includes(key)

  return (
    <ul className="navigation clearfix">
      <li>
        <Link href="/">Accueil</Link>
      </li>

      <li>
        <Link href="/page-about">À propos</Link>
      </li>

      <li>
        <Link href="/services">Services</Link>
      </li>

      {/* <li className="dropdown">
        <Link href="/services">Services</Link>
        <ul style={{ display: isActive(1) ? "block" : "none" }}>
          <li><Link href="/service1">Service 1</Link></li>
          <li><Link href="/service2">Service 2</Link></li>
        </ul>
        <div className="dropdown-btn" onClick={() => toggleMenu(1)}>
          <i className="fa fa-angle-down" />
        </div>
      </li> */}

      <li>
        <Link href="/gallery">Galerie</Link>
      </li>

      <li>
        <Link href="/blog">Blog</Link>
      </li>

      <li>
        <Link href="/contact">Contact</Link>
      </li>
    </ul>
  )
}
