'use client'
import { useState, useEffect } from 'react'

declare global {
  interface Window {
    google: any;
    googleTranslateElementInit: () => void;
  }
}

export default function LanguageSwitcher() {
  const [currentLang, setCurrentLang] = useState<'fr' | 'en'>('fr')
  const [isTranslating, setIsTranslating] = useState(false)

  useEffect(() => {
    // Check if already loaded
    if (document.querySelector('script[src*="translate.google.com"]')) {
      return
    }

    // Load Google Translate script
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = '//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit'
    script.async = true
    document.head.appendChild(script)

    // Initialize Google Translate
    window.googleTranslateElementInit = () => {
      new window.google.translate.TranslateElement({
        pageLanguage: 'fr',
        includedLanguages: 'fr,en',
        layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
        autoDisplay: false
      }, 'google_translate_element')

      // Hide Google's UI elements after a short delay
      setTimeout(() => {
        hideGoogleElements()
      }, 1000)
    }

    // Monitor for language changes via URL hash or cookie
    const checkLanguage = () => {
      const hash = window.location.hash
      const cookie = document.cookie.split(';').find(c => c.trim().startsWith('googtrans='))
      
      if (hash.includes('#googtrans(fr|en)') || hash.includes('#googtrans/fr/en') || cookie?.includes('/fr/en')) {
        setCurrentLang('en')
      } else if (hash.includes('#googtrans(en|fr)') || hash.includes('#googtrans/en/fr') || cookie?.includes('/en/fr')) {
        setCurrentLang('fr')
      }
    }

    // Check language on hash change
    window.addEventListener('hashchange', checkLanguage)
    checkLanguage()

    return () => {
      window.removeEventListener('hashchange', checkLanguage)
    }
  }, [])

  const hideGoogleElements = () => {
    // Hide the banner
    const banner = document.querySelector('.goog-te-banner-frame')
    if (banner) {
      (banner as HTMLElement).style.display = 'none'
    }

    // Reset body styles
    document.body.style.top = '0'
    document.body.style.marginTop = '0'

    // Hide tooltips
    const tooltips = document.querySelectorAll('#goog-gt-tt, .goog-tooltip')
    tooltips.forEach(el => {
      (el as HTMLElement).style.display = 'none'
    })

    // Hide the original dropdown
    const dropdown = document.querySelector('.goog-te-combo')
    if (dropdown) {
      (dropdown as HTMLElement).style.visibility = 'hidden';
      (dropdown as HTMLElement).style.position = 'absolute';
      (dropdown as HTMLElement).style.left = '-9999px';
    }
  }

  const translateTo = (targetLang: 'fr' | 'en') => {
    if (isTranslating) return

    setIsTranslating(true)

    // Method 1: Try using the dropdown
    const dropdown = document.querySelector('.goog-te-combo') as HTMLSelectElement
    if (dropdown) {
      dropdown.value = targetLang
      dropdown.dispatchEvent(new Event('change'))
    }

    // Method 2: Use hash-based approach (more reliable)
    const sourceLang = currentLang
    window.location.hash = `#googtrans(${sourceLang}|${targetLang})`

    // Method 3: Set cookie (fallback)
    document.cookie = `googtrans=/auto/${targetLang}; path=/`

    setCurrentLang(targetLang)

    // Auto reload page after translation
    setTimeout(() => {
      window.location.reload()
    }, 1000)
  }

  const toggleLanguage = () => {
    const newLang = currentLang === 'fr' ? 'en' : 'fr'
    translateTo(newLang)
  }

  return (
    <>
      {/* Hidden Google Translate Element */}
      <div id="google_translate_element" style={{ display: 'none' }}></div>
      
      {/* Custom Language Toggle */}
      <div className="language-switcher">
        <button 
          className={`lang-toggle ${isTranslating ? 'translating' : ''}`}
          onClick={toggleLanguage}
          disabled={isTranslating}
          type="button"
          aria-label={`Changer vers ${currentLang === 'fr' ? 'Anglais' : 'Français'}`}
        >
          <span className={`lang-option ${currentLang === 'fr' ? 'active' : ''}`}>
            FR
          </span>
          <span className="toggle-divider">|</span>
          <span className={`lang-option ${currentLang === 'en' ? 'active' : ''}`}>
            EN
          </span>
          {isTranslating && (
            <div className="loading-spinner">
              <div className="spinner"></div>
            </div>
          )}
        </button>

        <style jsx>{`
          .language-switcher {
            position: relative;
            display: inline-block;
          }

          .lang-toggle {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 8px 16px;
            color: #ffffff;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 70px;
            justify-content: center;
            position: relative;
            outline: none;
          }

          .lang-toggle:hover:not(:disabled) {
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .lang-toggle:active {
            transform: translateY(0);
          }

          .lang-toggle:disabled {
            opacity: 0.8;
            cursor: not-allowed;
            transform: none !important;
          }

          .lang-option {
            transition: all 0.3s ease;
            opacity: 0.6;
            font-weight: 400;
            font-size: 12px;
          }

          .lang-option.active {
            opacity: 1;
            font-weight: 700;
            color: #fff;
            text-shadow: 0 0 6px rgba(255, 255, 255, 0.4);
          }

          .toggle-divider {
            opacity: 0.4;
            font-size: 10px;
            margin: 0 2px;
          }

          .loading-spinner {
            position: absolute;
            top: 50%;
            right: 6px;
            transform: translateY(-50%);
          }

          .spinner {
            width: 10px;
            height: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-top: 1px solid #fff;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          /* Mobile Styles */
          @media (max-width: 768px) {
            .lang-toggle {
              padding: 10px 18px;
              font-size: 13px;
              min-width: 75px;
              border-radius: 22px;
            }

            .lang-option {
              font-size: 13px;
            }

            .toggle-divider {
              font-size: 11px;
            }
          }

          /* Mobile Menu Dark Theme */
          @media (max-width: 768px) {
            .mobile-menu .lang-toggle {
              border-color: rgba(0, 0, 0, 0.2);
              color: #333;
              background: rgba(0, 0, 0, 0.05);
            }

            .mobile-menu .lang-toggle:hover:not(:disabled) {
              border-color: rgba(0, 0, 0, 0.4);
              background: rgba(0, 0, 0, 0.1);
            }

            .mobile-menu .lang-option.active {
              color: #333;
              text-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
            }

            .mobile-menu .spinner {
              border-color: rgba(0, 0, 0, 0.3);
              border-top-color: #333;
            }
          }

          /* Hide Google Translate UI */
          :global(.goog-te-banner-frame) {
            display: none !important;
          }

          :global(.goog-te-menu-frame) {
            display: none !important;
          }

          :global(body) {
            top: 0px !important;
            margin-top: 0px !important;
          }

          :global(#goog-gt-tt) {
            display: none !important;
          }

          :global(.goog-tooltip) {
            display: none !important;
          }

          :global(.goog-te-combo) {
            visibility: hidden !important;
            position: absolute !important;
            left: -9999px !important;
          }

          :global(.skiptranslate) {
            display: none !important;
          }
        `}</style>
      </div>
    </>
  )
}
