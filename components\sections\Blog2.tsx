import Link from "next/link";
import Image from "next/image";

interface Blog2Props {
  addClass: string;
}

const blogData = [
  {
    image: "/images/resource/blog2-1.jpg",
    category: "Tendance",
    date: "20 Août 2025",
    title: "Le printemps arrive, profitez de nos offres spa exceptionnelles",
    link: "/news-details",
  },
  {
    image: "/images/resource/blog2-2.jpg",
    category: "Tendance",
    date: "20 Août 2025",
    title: "Découvrez nos massages VIP et soins personnalisés",
    link: "/news-details",
  },
  {
    image: "/images/resource/blog2-3.jpg",
    category: "Tendance",
    date: "20 Août 2025",
    title: "Pourquoi prendre soin de soi est essentiel au quotidien",
    link: "/news-details",
  },
];

const Blog2 = ({ addClass }: Blog2Props) => {
  return (
    <section className={`${addClass}`}>
      <div className="auto-container">
        <div className="sec-title text-center">
          <figure className="image">
            <Image src="/images/icons/icon1.png" alt="Icône" width={60} height={60} />
          </figure>
          <span className="sub-title">Nos Articles</span>
          <h2 className="words-slide-up text-split">Actualités & Conseils</h2>
        </div>

        <div className="row">
          {blogData.map((post, index) => (
            <div key={index} className="blog-block col-lg-4 col-md-6 mb-0">
              <div className="inner-box">
                <div className="image-bo">
                  <figure className="image" style={{ position: "relative", width: "370px", height: "250px" }}>
                    <Link href={post.link}>
                      <Image
                        src={post.image}
                        alt={post.title}
                        fill
                        style={{ objectFit: "cover" }}
                      />
                    </Link>
                  </figure>
                </div>
                <div className="content-box">
                  <ul className="post-meta">
                    <li className="categories"><Link href={post.link}>{post.category}</Link></li>
                    <li className="date">{post.date}</li>
                  </ul>
                  <h4 className="title">
                    <Link href={post.link}>{post.title}</Link>
                  </h4>
                  <Link className="read-more" href={post.link}>
                    Lire plus <i className="icon fa-regular fa-angle-right"></i>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Blog2;