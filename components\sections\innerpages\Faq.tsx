"use client";

import { useState } from 'react';
import Image from 'next/image';

const faqData = [
  {
    question: "Quels types de massages proposez-vous ?",
    answer: "Chez <PERSON><PERSON>pe, nous proposons une variété de massages incluant le massage relaxant, le massage suédois, le massage thaï et bien d'autres pour répondre à vos besoins de bien-être.",
  },
  {
    question: "Faut-il prendre rendez-vous à l'avance ?",
    answer: "Oui, il est recommandé de réserver à l'avance pour garantir la disponibilité de votre créneau préféré. Vous pouvez réserver directement via notre site ou nous appeler.",
  },
  {
    question: "Quels produits utilisez-vous durant les soins ?",
    answer: "Nous utilisons des produits naturels et haut de gamme, adaptés à tous types de peaux afin de garantir des résultats visibles tout en respectant votre peau.",
  },
  {
    question: "Proposez-vous des soins du visage personnalisés ?",
    answer: "Absolument ! Chaque soin du visage est personnalisé selon votre type de peau et vos besoins spécifiques, après un diagnostic réalisé par nos esthéticiennes.",
  },
];

const Faq = () => {
  const [isActive, setIsActive] = useState<any>({ status: false, key: 1 });

  const handleClick = (key: number) => {
    if (isActive.key === key) {
      setIsActive({ status: false });
    } else {
      setIsActive({ status: true, key });
    }
  };

  return (
    <section className="faq-section py-5">
      <div className="container">
        <div className="sec-title text-center mb-4">
          <figure className="image mx-auto mb-3">
            <Image src="/images/icons/icon1.png" alt="FAQ Lavish Shape" width={60} height={60} />
          </figure>
          <span className="sub-title">FAQ</span>
          <h2 className="words-slide-up text-split">Questions Fréquentes</h2>
          <p>Voici les réponses aux questions que nos clientes nous posent le plus souvent.</p>
        </div>

        <ul className="accordion-box wow fadeInRight">
          {faqData.map((item, index) => (
            <li key={index} className={`accordion block ${isActive.key === index ? "active-block" : ""}`}>
              <div
                className={`acc-btn ${isActive.key === index ? "active" : ""}`}
                onClick={() => handleClick(index)}
              >
                {item.question}
                <div className="icon fa fa-plus"></div>
              </div>
              <div className={`acc-content ${isActive.key === index ? "current" : ""}`}>
                <div className="content">
                  <div className="text">{item.answer}</div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
};

export default Faq;
