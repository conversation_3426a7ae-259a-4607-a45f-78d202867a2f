import Link from "next/link";
import Image from "next/image";

const About8 = () => {
  return (
    <section className="about-section-five">
      <div className="about8-2"></div>
      <div className="about8-1"></div>
      <div className="auto-container">
        <div className="outer-box">
          <div className="row">
            <div className="content-column col-xl-4 col-lg-6 col-md-6">
              <div className="inner-column">
                <div className="sec-title mb-0">
                  <figure className="image">
                    <Image src="/images/icons/icon2.png" alt="Image" width={40} height={40} />
                  </figure>
                  <span className="sub-title">Faites connaissance avec nous</span>
                  <h2 className="words-slide-up">
                    Lavish Shape & Glow Med Spa
                  </h2>
                  <div className="text">
                    Proin efficitur, mauris vel condimentum pulvinar, velit orci consectetur ligula, eget egestas magna mi ut arcu. Phasellus nec odio orci.
                  </div>
                </div>
                <ul className="list-style">
                  <li>
                    <figure className="icon">
                      <Image src="/images/icons/theme-icon4.png" alt="Image" width={20} height={20} />
                    </figure>
                    Velit orci consectetur ligula, eget egestas magn
                  </li>
                  <li>
                    <figure className="icon">
                      <Image src="/images/icons/theme-icon4.png" alt="Image" width={20} height={20} />
                    </figure>
                    Pelit orci consectetur ligula
                  </li>
                  <li>
                    <figure className="icon">
                      <Image src="/images/icons/theme-icon4.png" alt="Image" width={20} height={20} />
                    </figure>
                    Eget egestas magn
                  </li>
                </ul>
                <div className="author-box">
                  <div className="inner d-block d-sm-flex">
                    <Link href="/booking" className="theme-btn btn-style-two btn pricing-btn mb-4 mb-sm-0">
                      <span className="btn-title">Réservez</span>
                    </Link>
                    <figure className="thumb">
                      <Image src="/images/boss.jpg" alt="Image" width={60} height={60} style={{ borderRadius: "100%" }} />
                    </figure>
                    <div className="info">
                      {/* <div className="sign">
                        <Image src="/images/resource/about-sign1.png" alt="Signature" width={100} height={40} />
                      </div> */}
                      <div className="name">
                        Mme - <span className="designation">Fondatrice</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="image-column col-xl-4 col-lg-6 wow fadeInLeft">
              <div className="inner-column">
                <div className="image-box">
                  <figure className="image overlay-anim rounded" style={{ overflow: "hidden", borderRadius: "12px" }}>
                    <Image src="/images/about.jpg" alt="Image" width={400} height={500} style={{ objectFit: "cover", borderRadius: "16px" }} />
                  </figure>
                </div>
              </div>
            </div>

            <div className="timetable-block col-xl-4 col-lg-6 col-md-6 wow fadeInRight">
              <div className="inner">
                <div className="content-top">
                  <figure className="icon">
                    <Image src="/images/icons/clock1.png" alt="Image" width={30} height={30} />
                  </figure>
                  <h4 className="title">Heures d'ouverture</h4>
                </div>
                <div className="content">
                  <div className="time-box">
                    <h4 className="title"><i className="icon fa-sharp fa-thin fa-clock"></i> Horaires :</h4>
                    <div className="opening-days">Lun au Ven : 7h30 - 01h00</div>
                    <div className="opening-days">Samedi : 7h30 - 01h00</div>
                    <div className="opening-days">Dimanche : 7h30 - 01h00</div>
                  </div>
                  <div className="time-box">
                    <h4 className="title"><i className="icon fa-solid fa-location-dot"></i> Adresse :</h4>
                    <div className="time-box-address">
                      Boulevard de la Liberté,<br />
                      Douala Bonapriso<br />
                      Cameroun
                    </div>
                  </div>
                  <div className="bg bg-image" style={{ backgroundImage: 'url(/images/resource/about8-4.png)' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About8;
