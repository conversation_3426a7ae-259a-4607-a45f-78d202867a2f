"use client";
import Cal, { getCalA<PERSON> } from "@calcom/embed-react";
import { useEffect, useState } from "react";

const BookingPage = () => {
  const [mounted, setMounted] = useState(false);
  // const { theme, systemTheme } = useTheme();

  // Determine the current theme
  // const currentTheme = theme === "system" ? systemTheme : theme;
  // const isDark = currentTheme === "dark";

  useEffect(() => {
    setMounted(true);
    (async function () {
      const cal = await getCalApi({ namespace: "30min" });
      cal("ui", {
        cssVarsPerTheme: {
          dark: { "cal-brand": "#D0AC3B" },
          light: { "cal-brand": "#D0AC3B" },
        },
        hideEventTypeDetails: false,
        layout: "month_view",
      });
    })();
  }, []); // Re-run when theme changes

  return (
    <section className="pb-14 md:pb-16 lg:pb-[88px] xl:pb-[100px]">
      <div className="container">
        <div className="sec-title text-center">
          <span className="sub-title">Réservez en ligne</span>
          <h2>Choisissez le créneau qui vous convient</h2>
        </div>

        <div className="mx-auto max-w-[1100px] rounded-2xl p-0 md:p-4">
          {!mounted ? (
            <div className="h-[600px] w-full animate-pulse" />
          ) : (
            <div className="w-full  md:h-[700px] overflow-y-auto rounded-md bg-transparent dark:bg-transparent">
              <Cal
                namespace="30min"
                calLink="briandave"
                style={{
                  width: "100%",
                  height: "100%",
                  background: "transparent",
                }}
                config={{
                  layout: "month_view",
                  theme: "light", // Dynamic theme
                }}
              />
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default BookingPage;