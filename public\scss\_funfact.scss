/*** 

====================================================================
    Fun Fact Section
====================================================================

***/

.fun-fact-section {
    position: relative;
    padding: 90px 0 120px;
    .bg-image-two {
        width: 100%;
        height: 69px;
        bottom: 0;
        left: 0;
        top: auto;
    }
    .feature1-2 {
        background-image: url(../images/resource/feature1-2.png);
        position: absolute;
        width: 285px;
        height: 344px;
        bottom: 37px;
        right: 0;
        @include media-breakpoint-down(md){
            display: none;
        }
    }
}

//Counter BLock
.counter-block {
    position: relative;
    @include media-breakpoint-down(lg){
        margin-bottom: 40px;
    }
    .inner {
        position: relative;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        &:hover {
            .icon {
                img {
                    transform: scale(-1) rotate(180deg);
                }
            }
            .counter-title {
                opacity: 1;
            }
        }
    }
    .icon {
        background-color: var(--theme-color1);
        border-radius: 50%;
        position: relative;
        height: 100px;
        width: 100px;
        line-height: 96px;
        margin-bottom: 35px;
        img {
            position: relative;
            @include transition(all 300ms ease);
        }
    }
    .count-box {
        position: relative;
        display: block;
        font-size: 60px;
        font-weight: 600;
        .count-text {
            color: var(--headings-color);
            font-size: 80px;
            font-weight: 500;
            font-family: var(--title-font);
        }
    }
    .counter-title {
        display: block;
        font-size: 22px;
        color: var(--headings-color);
        font-weight: 500;
        margin-bottom: 0;
        margin-top: 43px;
    }
}

/*** 

====================================================================
    Fun Fact Section Two
====================================================================

***/

.fun-fact-section-two {
    background-color: var(--theme-color5);
    position: relative;
    padding: 90px 0 120px;
    &.pull-down {
        margin-bottom: -96px;
    }
    .outer-box {
        background-color: #F9F3F0;
        border: 6px solid var(--theme-color-light);
        padding: 60px 60px 50px;
        position: relative;
        margin-bottom: -100px;
        z-index: 1;
        @include media-breakpoint-down(lg){
            padding: 60px 60px 10px;
        }
        @include media-breakpoint-down(sm){
            padding: 60px 20px 10px;
        }
    }
}

//Counter BLock
.counter-block-two {
    position: relative;
    @include media-breakpoint-down(lg){
        margin-bottom: 40px;
    }
    .inner {
        position: relative;
        display: flex;
        align-items: center;
        &:hover {
            .icon {
                img {
                    transform: scale(-1) rotate(180deg);
                }
            }
            .counter-title {
                opacity: 1;
            }
        }
    }
    .icon {
        font-size: 64px;
        position: relative;
        line-height: 1;
        margin-bottom: 35px;
        text-align: center;
        min-width: 60px;
        @include media-breakpoint-down(sm){
            max-width: 60px;
        }
        img {
            position: relative;
            @include transition(all 300ms ease);
        }
    }
    .inner-content {
        padding-left: 17px;
    }
    .count-box {
        position: relative;
        display: block;
        color: var(--headings-color);
        font-size: 64px;
        font-weight: 400;
        font-family: var(--text-font);
        @include media-breakpoint-down(xl){
            font-size: 42px;
        }
        .count-text {
            color: var(--headings-color);
            font-size: 64px;
            font-weight: 600;
            font-family: var(--text-font);
            @include media-breakpoint-down(xl){
                font-size: 42px;
            }
        }
    }
    .counter-title {
        display: block;
        font-size: 22px;
        color: var(--headings-color);
        font-weight: 500;
        margin-bottom: 0;
        margin-top: 25px;
    }
}
