/* Purerelax | Spa & Beauty HTML Template

Template Name: Purerelax
Version: 1.0.0
License: copyright commercial

/************ TABLE OF CONTENTS ***************
1.  Fonts
2.  Reset
3.  Global Settings
4.  Main Header / TWo / Three
5.  <PERSON><PERSON> Header 
6.  Mobile Menu
7.  Hidden Sidebar style
8.  Section Title
9.  Banner Section / Two / Three
10. Project Section / Two / Three
11. Features Section / Two / Three
12. About Section / Two / Three
13. Services Section / Two / Three
14. Video Section / Two
15. Contact Section
16. FAQ's Section / Two
17. Fun Fact Section / Two
18. Testimonial Section / Two
19. Process Section / Two
20. Team Section
21. Why Choose Us / Two / Three
22. News Section
23. Main Footer
**********************************************/
@import url("https://fonts.googleapis.com/css2?family=Alex+Brush&family=Cormorant:ital,wght@0,300;0,400;0,500;0,600;0,700;1,600&family=Plus+Jakarta+Sans:ital,wght@0,400;0,500;0,600;0,700;0,800;1,400;1,500;1,600;1,700&display=swap");
@import url("swiper.min.css");
@import url("linear.css");
@import url("select2.min.css");
@import url("fontawesome.css");
@import url("flaticon-set-logistics.css");
@import url("tm-bs-mp.css");
@import url("tm-utility-classes.css");
:root {
  --theme-color1: #D0AC3B;
  --theme-color2: #6DA8AE;
  --theme-color3: #666666;
  --theme-color4: #6DA8AE;
  --theme-color5: #f9f6f1;
  --bg-theme-color1: var(--theme-color1);
  --bg-theme-color2: var(--theme-color2);
  --bg-theme-color3: var(--theme-color3);
  --bg-theme-color4: var(--theme-color4);
  --border-theme-color1: var(--theme-color1);
  --border-theme-color2: var(--theme-color2);
  --theme-color-light: #FFFFFF;
  --theme-color-dark: #000000;
  --theme-color-lighter: #F8F8F8;
  --review-color: #FFAA18;
  --theme-color1-dark: #B69D49;
  --theme-color1-rgb: 194, 167, 78;
  --theme-color2-rgb: 1, 20, 37;
  --theme-color3-rgb: 102, 102, 102;
  --theme-color4-rgb: 24, 52, 37;
  --theme-color5-rgb: 249, 246, 241;
  --headings-color-rgb: 28, 26, 29;
  --theme-color-dark-rgb: 0, 0, 0;
  --theme-color-light-rgb: 255, 255, 255;
  --text-color: #707070;
  --headings-color: #1C1A1D;
  --link-color: var(--theme-color-dark);
  --link-hover-color: var(--theme-color-dark);
  --text-font: "Plus Jakarta Sans", sans-serif;
  --title-font: "Cormorant";
  --style-font: "Alex Brush", cursive;
  --body-font-size: 16px;
  --body-line-height: 29px;
  --body-font-weight: 400;
  --line-height-heading-h1: 1.2em;
  --line-height-heading: 1.211em;
  --line-height-heading-small: 1.4em;
  --h1-font-size: 80px;
  --h2-font-size: 60px;
  --h3-font-size: 40px;
  --h4-font-size: 30px;
  --h5-font-size: 20px;
  --h6-font-size: 18px;
  --h1-font-weight: 700;
  --h2-font-weight: 700;
  --h3-font-weight: 700;
  --h4-font-weight: 700;
  --h5-font-weight: 700;
  --h6-font-weight: 700;
  --sec-title-subtitle-color: var(--theme-color1);
  --sec-title-subtitle-font-size: 25px;
  --sec-title-subtitle-font-family: var(--style-font);
  --sec-title-subtitle-font-weight: 400;
  --sec-title-subtitle-line-height: 31.25px;
  --sec-title-color: var(--theme-color-dark);
  --sec-title-font-size: var(--h2-font-size);
  --sec-title-font-family: var(--title-font);
  --sec-title-font-weight: 600;
  --sec-title-title-line-height: 1.172em;
  --theme-light-background: #f8f6f1;
  --theme-light-background-text-color: var(--headings-color);
  --theme-black: #000000;
  --container-width: 1320px;
  --small-container-width: 1000px;
  --large-container-width: 1326px;
}

/*
 * typography.scss
 * -----------------------------------------------
*/
::-moz-selection {
  background: var(--theme-color-dark);
  color: #fff;
  text-shadow: none;
}
::selection {
  background: var(--theme-color-dark);
  color: #fff;
  text-shadow: none;
}

:active,
:focus {
  outline: none !important;
}

::-webkit-input-placeholder {
  color: #000;
}

::-moz-input-placeholder {
  color: #000;
}

::-ms-input-placeholder {
  color: #000;
}

body {
  background-color: #fff;
  background-attachment: fixed;
  -ms-word-wrap: break-word;
  word-wrap: break-word;
  counter-reset: my-sec-counter;
  -webkit-font-smoothing: antialiased;
  -moz-font-smoothing: antialiased;
  color: var(--text-color);
  font-size: var(--body-font-size);
  font-family: var(--text-font);
  font-weight: var(--body-font-weight);
  line-height: var(--body-line-height);
  letter-spacing: -0.01em;
}

p, .text {
  color: var(--text-color);
  font-size: var(--body-font-size);
  font-family: var(--text-font);
  font-weight: var(--body-font-weight);
  line-height: var(--body-line-height);
  letter-spacing: -0.01em;
}

/* -------- Headings ---------- */
h1, h2, h3, h4, h5, h6 {
  color: var(--headings-color);
  font-family: var(--title-font);
  position: relative;
  line-height: var(--line-height-heading-);
}
h1 small,
h1 .small, h2 small,
h2 .small, h3 small,
h3 .small, h4 small,
h4 .small, h5 small,
h5 .small, h6 small,
h6 .small {
  font-weight: normal;
  line-height: 1;
  color: var(--headings-color);
}
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a {
  color: inherit;
  font-weight: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 1rem;
}

h1 {
  font-size: var(--h1-font-size);
  font-weight: var(--h1-font-weight);
  line-height: var(--line-height-heading-h1);
}

h2 {
  font-size: var(--h2-font-size);
  font-weight: var(--h2-font-weight);
  line-height: var(--line-height-heading);
}

h3 {
  font-size: var(--h3-font-size);
  font-weight: var(--h3-font-weight);
  line-height: var(--line-height-heading);
}

h4 {
  font-size: var(--h4-font-size);
  font-weight: var(--h4-font-weight);
  line-height: var(--line-height-heading);
}

h5 {
  font-size: var(--h5-font-size);
  font-weight: var(--h5-font-weight);
  line-height: var(--line-height-heading);
}

h6 {
  font-size: var(--h6-font-size);
  font-weight: var(--h6-font-weight);
  line-height: var(--line-height-heading-small);
}

/* -------- Body Text ---------- */
table p {
  margin-bottom: 0;
}

p {
  margin-bottom: 20px;
}
p a:not(.button):not(.btn):hover, p a:not(.button):not(.btn):focus {
  text-decoration: underline;
}

/* -------- other ---------- */
a {
  color: var(--link-color);
  text-decoration: none;
  font-weight: var(--body-font-weight);
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
a:hover, a:focus {
  color: inherit;
  text-decoration: none;
  outline: none;
}
a b, a strong {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
a img {
  border: none;
}

pre,
ul,
ol,
dl,
dd,
blockquote,
address,
table,
fieldset {
  margin-bottom: 10px;
}

ol, ul {
  list-style-position: inside;
  margin: 0;
  padding: 0;
}

b, strong {
  color: #333;
  font-weight: var(--body-font-weight-bold);
}

iframe {
  border: none !important;
}

/*
 * container.scss
 * -----------------------------------------------
*/
.container .container {
  width: 100%;
}

.container .container,
.container .container-fluid,
.container-fluid .container,
.container-fluid .container-fluid {
  padding-left: 0;
  padding-right: 0;
}

section > .container,
section > .container-fluid {
  padding-top: var(--container-pt);
  padding-bottom: var(--container-pt);
}

@media (min-width: 1400px) {
  .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
    max-width: var(--container-width);
  }
}
/*=== Default Form ===*/
.form-control, .input-text {
  height: calc(2.25rem + 27px);
  padding: 14px 30px;
  outline: 0;
  background-color: #f4f5f8;
  border: 1px solid #f4f5f8;
  color: #fff;
  font-size: 0.9rem;
  width: 100%;
}
.form-control::-webkit-input-placeholder, .input-text::-webkit-input-placeholder {
  color: #000;
  opacity: 1;
}
.form-control::-moz-placeholder, .input-text::-moz-placeholder {
  color: #000;
  opacity: 1;
}
.form-control:-ms-input-placeholder, .input-text:-ms-input-placeholder {
  color: #000;
  opacity: 1;
}
.form-control::-ms-input-placeholder, .input-text::-ms-input-placeholder {
  color: #000;
  opacity: 1;
}
.form-control::placeholder, .input-text::placeholder {
  color: #000;
  opacity: 1;
}
.form-control:-ms-input-placeholder, .input-text:-ms-input-placeholder {
  color: #000;
}
.form-control::-ms-input-placeholder, .input-text::-ms-input-placeholder {
  color: #000;
}

textarea.form-control {
  height: auto;
  padding-top: 15px;
  padding-bottom: 15px;
}

/* -------- Dark Theme Styling ---------- */
.dark-layout {
  background-color: var(--theme-color-dark) !important;
  --sec-title-color: var(--theme-color-light);
}
.dark-layout .sticky-header .main-menu .navigation > li > a,
.dark-layout h1, .dark-layout h2, .dark-layout h3, .dark-layout h4, .dark-layout h5, .dark-layout h6 {
  color: var(--theme-color-light);
}
.dark-layout .preloader {
  background-color: var(--theme-color-dark);
}
.dark-layout .preloader:after {
  background-image: url(../images/logo.png);
}
.dark-layout .sticky-header {
  background-color: var(--theme-color-dark);
}
.dark-layout .hidden-bar .upper-box {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dark-layout .hidden-bar .social-links {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.dark-layout .hidden-bar .social-links li {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

/*** 

====================================================================
Reset
====================================================================

***/
* {
  margin: 0px;
  padding: 0px;
  border: none;
  outline: none;
  font-size: 100%;
}

/*** 

====================================================================
Global Settings
====================================================================

***/
textarea {
  overflow: hidden;
  resize: none;
}

button {
  outline: none !important;
  cursor: pointer;
}

img {
  display: inline-block;
  max-width: 100%;
  height: auto;
}

ul,
li {
  list-style: none;
  padding: 0px;
  margin: 0px;
}

.title a {
  color: inherit;
}

.color1 {
  color: var(--theme-color1);
}

.color2 {
  color: var(--theme-color2);
}

.color3 {
  color: var(--theme-color3);
}

.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
  z-index: 99;
  background-color: #ffffff;
}

.large-container {
  position: static;
  max-width: var(--large-container-width);
  padding: 0px 15px;
  margin: 0 auto;
  width: 100%;
}

.auto-container {
  position: static;
  max-width: var(--large-container-width);
  padding: 0px 15px;
  margin: 0 auto;
  width: 100%;
}

.small-container {
  position: static;
  max-width: var(--small-container-width);
  padding: 0px 15px;
  margin: 0 auto;
  width: 100%;
}

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.dropdown-toggle::after {
  display: none;
}

/*=======================
    Preloader
=======================*/
.preloader {
  position: fixed;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  z-index: 999999;
  background-color: #ffffff;
}
.preloader:after {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 110px;
  margin-left: -55px;
  margin-top: -70px;
  height: 110px;
  background-position: center center;
  background-repeat: no-repeat;
  -webkit-animation: pulse 1s infinite linear;
  animation: pulse 1s infinite linear;
  background-image: url(../images/icons/preloader.png);
  content: "";
}
.preloader:before {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  width: 100%;
  text-align: center;
  margin: 0 auto;
  margin-top: 20px;
  color: var(--theme-color-dark);
  font-weight: 600;
  font-size: 14px;
  font-family: var(--title-font);
  letter-spacing: 2px;
  text-transform: uppercase;
  content: "Chargement";
  -webkit-transition: none;
  transition: none;
}

/*=======================
Scroll To Top style
=======================*/
.scroll-to-top {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 40px;
  font-size: 16px;
  line-height: 40px;
  color: var(--theme-color-light);
  text-align: center;
  cursor: pointer;
  background-color: var(--theme-color1);
  z-index: 100;
  display: none;
  border-radius: 50%;
  margin: 0 auto;
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 1399.98px) {
  .scroll-to-top {
    bottom: 70px;
  }
}

.scroll-to-top:hover {
  background: var(--theme-color-dark);
  color: #ffffff;
}

.link-style-one {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 12px;
  line-height: 20px;
  font-weight: 600;
  overflow: hidden;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  font-family: var(--title-font);
  color: var(--theme-color-dark);
}
.link-style-one:before {
  position: absolute;
  left: 0;
  right: 18px;
  bottom: 2px;
  height: 1px;
  background-color: var(--theme-color1);
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.link-style-one i {
  position: relative;
  top: 1px;
  display: block;
  font-size: 14px;
  margin-left: 5px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.link-style-one:hover {
  color: var(--theme-color1);
}
.link-style-one:hover:before {
  right: 100%;
}

/*=== List Style One ===*/
.list-style-one {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 0;
}
.list-style-one li {
  position: relative;
  font-size: 12px;
  line-height: 24px;
  font-weight: 400;
  color: var(--theme-color-dark);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-right: 30px;
}
.list-style-one li:last-child {
  margin-right: 0 !important;
}
.list-style-one li i {
  margin-right: 10px;
  color: var(--theme-color-dark);
  font-size: 16px;
  line-height: 27px;
}
.list-style-one li a {
  display: block;
  color: var(--theme-color-dark);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-one li a:hover {
  color: var(--theme-color1);
}
.list-style-one.light li {
  color: var(--theme-color-light);
}
.list-style-one.light li a {
  color: var(--theme-color-light);
}
.list-style-one.light li a:hover {
  color: var(--theme-color1);
}
.list-style-one.light i {
  color: var(--theme-color-light);
}

/*=== List Style Two ===*/
.list-style-two {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.list-style-two li {
  position: relative;
  font-size: 18px;
  line-height: 24px;
  padding-left: 25px;
  font-weight: 600;
  color: var(--theme-color-dark);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  margin-bottom: 15px;
}
.list-style-two li i {
  position: absolute;
  left: 0px;
  top: 0px;
  color: var(--theme-color1);
  font-size: 18px;
  line-height: 24px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-two li a {
  display: inline-block;
  font-weight: inherit;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-two li a:hover {
  color: var(--theme-color1);
}
.list-style-two.two-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}
.list-style-two.two-column li {
  width: 50%;
}
@media (max-width: 767.98px) {
  .list-style-two.two-column li {
    width: 100%;
  }
}

/*Social Icon One*/
.social-icon-one {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}
.social-icon-one li {
  position: relative;
  margin-left: 50px;
}
.social-icon-one li:first-child {
  margin-left: 0;
}
.social-icon-one li a {
  position: relative;
  display: block;
  line-height: 27px;
  text-align: center;
  font-size: 14px;
  color: #bdbdbd;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-one li a:hover {
  color: var(--theme-color1);
}
.social-icon-one.light li a {
  color: var(--theme-color-light);
}
.social-icon-one.light li a:hover {
  color: var(--theme-color1);
}

/*Social Icon Two*/
.social-icon-two {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.social-icon-two li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 10px;
}
.social-icon-two li:first-child {
  margin-left: 0;
}
.social-icon-two li a {
  position: relative;
  display: block;
  height: 36px;
  width: 36px;
  line-height: 36px;
  border-radius: 50%;
  text-align: center;
  font-size: 14px;
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.social-icon-two li a i {
  position: relative;
}
.social-icon-two li a::before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  -webkit-transform: scale(0);
          transform: scale(0);
  background-color: var(--theme-color1);
  content: "";
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  border-radius: 50px;
}
.social-icon-two li a:hover {
  color: #fff;
}
.social-icon-two li a:hover:before {
  -webkit-transform: scale(1);
          transform: scale(1);
}

.bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.devider {
  position: absolute;
  padding: 0;
  margin: 0;
  width: 80%;
  left: 0;
  right: 0;
  top: 0;
  margin: 0 auto;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
@media (max-width: 1199.98px) {
  .devider {
    width: 100%;
  }
}

/*======================
    Tabs Box
======================*/
.tabs-box {
  position: relative;
}

.tabs-box .tab {
  display: none;
}

.tabs-box .active-tab {
  display: block;
}

.play-btn {
  position: relative;
}
.play-btn:hover .icon {
  background-color: var(--theme-color1);
  color: var(--theme-color-dark);
}
.play-btn .icon {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 90px;
  width: 90px;
  border-radius: 50%;
  font-size: 24px;
  color: var(--theme-color1);
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-btn .circle-text img {
  position: relative;
  display: inline-block;
  -webkit-animation: fa-spin 60s infinite linear;
          animation: fa-spin 60s infinite linear;
}

.play-btn-two {
  position: relative;
}
.play-btn-two:hover .icon {
  background-color: var(--theme-color1);
  color: var(--theme-color-dark);
}
.play-btn-two .icon {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 122px;
  width: 122px;
  border-radius: 50%;
  font-size: 24px;
  color: var(--theme-color1);
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-btn-two .circle-text img {
  position: relative;
  display: inline-block;
  -webkit-animation: fa-spin 60s infinite linear;
          animation: fa-spin 60s infinite linear;
}

/*======================
    Media Play Button 
======================*/
.play-now {
  position: relative;
  display: block;
  z-index: 9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-now:hover .icon {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.play-now .icon {
  position: relative;
  display: inline-block;
  height: 100px;
  width: 100px;
  text-align: center;
  line-height: 100px;
  background-color: #ffffff;
  color: var(--theme-color-dark);
  z-index: 1;
  font-size: 22px;
  display: block;
  border-radius: 50%;
  -webkit-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
          box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-now .ripple,
.play-now .ripple:before,
.play-now .ripple:after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(191, 168, 136, 0.6);
          box-shadow: 0 0 0 0 rgba(191, 168, 136, 0.6);
  -webkit-animation: ripple 3s infinite;
          animation: ripple 3s infinite;
}
/* .play-now .ripple.light,
.play-now .ripple:before.light,
.play-now .ripple:after.light {
  -webkit-box-shadow: 0 0 0 0 rgb(255, 255, 255);
          box-shadow: 0 0 0 0 rgb(255, 255, 255);
}
.play-now .ripple.light:before, .play-now .ripple.light:after,
.play-now .ripple:before.light:before,
.play-now .ripple:before.light:after,
.play-now .ripple:after.light:before,
.play-now .ripple:after.light:after {
  -webkit-box-shadow: 0 0 0 0 rgb(255, 255, 255);
          box-shadow: 0 0 0 0 rgb(255, 255, 255);
} */
.play-now .ripple:before {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
}
.play-now .ripple:after {
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
}

@-webkit-keyframes ripple {
  70% {
    -webkit-box-shadow: 0 0 0 50px rgba(191, 168, 136, 0);
    box-shadow: 0 0 0 50px rgba(191, 168, 136, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(191, 168, 136, 0);
    box-shadow: 0 0 0 0 rgba(191, 168, 136, 0);
  }
}

@keyframes ripple {
  70% {
    -webkit-box-shadow: 0 0 0 50px rgba(191, 168, 136, 0);
    box-shadow: 0 0 0 50px rgba(191, 168, 136, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(191, 168, 136, 0);
    box-shadow: 0 0 0 0 rgba(191, 168, 136, 0);
  }
}
.play-now-two {
  height: 150px;
  width: 150px;
  background-color: rgba(21, 21, 21, 0.5);
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 34px;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  -webkit-animation: zoom-one 3s infinite linear;
          animation: zoom-one 3s infinite linear;
}
.play-now-two:hover {
  color: #ff9205;
  background-color: #ffffff;
}

/*======================
    Contact Info Button 
======================*/
.info-btn {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  font-size: 15px;
  padding-left: 70px;
  text-align: left;
  font-weight: 700;
  line-height: 20px;
  min-height: 62px;
  font-family: var(--title-font);
}
@media (max-width: 991.98px) {
  .info-btn {
    padding-left: 70px;
  }
}
.info-btn:hover .icon {
  -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
}
.info-btn small {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: var(--theme-color-dark);
  margin-bottom: 8px;
}
@media (max-width: 991.98px) {
  .info-btn small {
    font-size: 14px;
    margin-bottom: 15px;
  }
}
.info-btn strong {
  font-size: 24px;
  font-weight: 700;
  color: var(--theme-color1);
}
@media (max-width: 991.98px) {
  .info-btn strong {
    font-size: 26px;
  }
}
@media (max-width: 575.98px) {
  .info-btn strong {
    font-size: 20px;
  }
}
.info-btn .icon {
  position: absolute;
  left: 0;
  top: 5px;
  width: 52px;
  height: 52px;
  line-height: 52px;
  text-align: center;
  border-radius: 50%;
  font-size: 24px;
  color: var(--theme-color-light);
  background-color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 991.98px) {
  .info-btn .icon {
    width: 60px;
    height: 60px;
    line-height: 60px;
  }
}
.info-btn:hover {
  color: var(--theme-color1);
}

.info-btn-two {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  font-size: 15px;
  text-align: left;
  font-weight: 700;
  line-height: 20px;
  min-height: 74px;
  padding: 0 42px;
  padding-left: 90px;
  border-radius: 50px;
  background-color: var(--theme-color-light);
  font-family: var(--title-font);
}
@media (max-width: 991.98px) {
  .info-btn-two {
    padding-left: 70px;
  }
}
.info-btn-two small {
  display: block;
  font-size: 14px;
  font-weight: 400;
  color: var(--theme-color-dark);
  margin-bottom: 0;
}
.info-btn-two strong {
  font-size: 16px;
  font-weight: 700;
  color: var(--theme-color-dark);
}
.info-btn-two .icon {
  position: absolute;
  left: 8px;
  top: 10px;
  width: 52px;
  height: 52px;
  line-height: 52px;
  text-align: center;
  border-radius: 50%;
  font-size: 24px;
  color: var(--theme-color-light);
  background-color: var(--theme-color1);
}
.info-btn-two:hover {
  color: var(--theme-color1);
}

.styled-pagination {
  position: relative;
  max-width: 356px;
  margin: 12px auto;
  width: 100%;
  margin-top: 60px;
  background-color: transparent;
}
.styled-pagination .swiper-pagination-progressbar-fill {
  background: -webkit-gradient(linear, right top, left top, color-stop(-1.4%, rgba(217, 217, 217, 0)), to(#D0AC3B));
  background: linear-gradient(270deg, rgba(217, 217, 217, 0) -1.4%, #D0AC3B 100%);
}
.styled-pagination .swiper-pagination-progressbar-fill:before {
  position: absolute;
  top: -7px;
  height: 15px;
  width: 15px;
  background-color: var(--theme-color1);
  border-radius: 50%;
  content: "";
}

.swiper-horizontal > .styled-scrollbar {
  border-radius: 2px;
  height: 3px;
  width: 356px;
  bottom: 11px;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: -webkit-gradient(linear, right top, left top, color-stop(-1.4%, rgba(217, 217, 217, 0)), to(#D0AC3B));
  background: linear-gradient(270deg, rgba(217, 217, 217, 0) -1.4%, #D0AC3B 100%);
}

.swiper-horizontal > .styled-scrollbar > .swiper-scrollbar-drag {
  background-color: var(--theme-color1);
  border-radius: 50%;
  height: 15px;
  max-width: 15px;
  top: -6px;
  cursor: pointer;
}

/*================================
    Progress Bar
=================================*/
.skills {
  position: relative;
}
.skills .skill-item {
  position: relative;
  margin-bottom: 35px;
}
.skills .skill-item:last-child {
  margin-bottom: 0px;
}
.skills .skill-item .skill-header {
  position: relative;
  margin-bottom: 6px;
  margin-top: 31px;
}
.skills .skill-item .skill-header .skill-title {
  font-size: 14px;
  color: var(--theme-color-dark);
  font-weight: 600;
  font-family: var(--text-font);
}
.skills .skill-item .skill-bar {
  position: relative;
  width: 100%;
  height: 6px;
  border-radius: 6px;
}
.skills .skill-item .skill-bar .bar-inner {
  position: relative;
  width: 100%;
  height: 6px;
}
.skills .skill-item .skill-bar .bar-inner .bar {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 5px;
  -webkit-transition: all 3000ms ease;
  transition: all 3000ms ease;
  background-color: var(--theme-color2);
}
.skills .skill-item .skill-bar .bar-inner .bar:before {
  border: 1px solid #d7d7d7;
  content: "";
  top: -4px;
  left: -3px;
  position: absolute;
  width: calc(100% + 6px);
  height: calc(100% + 6px);
}
.skills .skill-item .skill-bar .bar-inner .skill-percentage {
  position: absolute;
  right: 8px;
  bottom: 100%;
  margin-bottom: 15px;
  font-size: 14px;
  color: var(--theme-color2);
  -webkit-transform: translateX(50%);
  transform: translateX(50%);
  height: 26px;
  text-align: center;
}

/*========================
  Select2 Dropdown Plugin
========================*/
.select2-dropdown {
  border: 1px solid #eee;
}

.select2-results__option {
  padding: 0 10px;
  color: #7c858c;
  border: 0;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #7c858c;
  padding-left: 0;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--theme-color1);
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  height: 30px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  bottom: 1px;
  height: auto;
  width: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 12px;
  color: #7c858c;
}

.select2-container--default .select2-selection--single .select2-selection__arrow:before {
  position: relative;
  content: "\f107";
  font-family: "Font Awesome 6 Pro";
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  display: none;
}

.select2-container--default .select2-selection--single {
  border-radius: 0;
}

.default-navs .owl-next,
.default-navs .owl-prev {
  display: block;
  margin: 0px 5px;
  height: 65px;
  width: 65px;
  color: var(--theme-color1);
  border-radius: 50%;
  -webkit-box-shadow: 0 0px 30px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #ffffff;
  font-size: 16px;
  line-height: 63px;
  font-weight: 700;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.default-navs .owl-next span,
.default-navs .owl-prev span {
  display: inline-block;
  background: var(--gradient-1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
.default-navs .owl-next:hover,
.default-navs .owl-prev:hover {
  -webkit-box-shadow: 0;
          box-shadow: 0;
  border: 1px solid var(--theme-color2);
}

.custom-navs .owl-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.custom-navs .owl-nav .owl-prev,
.custom-navs .owl-nav .owl-next {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 50px;
  width: 87px;
  padding-left: 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.custom-navs .owl-nav .owl-prev:before,
.custom-navs .owl-nav .owl-next:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  background-color: var(--theme-color1);
  opacity: 0.1;
  content: "";
  border-radius: 50px;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.custom-navs .owl-nav .owl-prev:hover,
.custom-navs .owl-nav .owl-next:hover {
  padding: 0 17px;
}
.custom-navs .owl-nav .owl-prev:hover:before,
.custom-navs .owl-nav .owl-next:hover:before {
  width: 100%;
}
.custom-navs .owl-nav .owl-next {
  padding-left: 0;
  padding-right: 20px;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.custom-navs .owl-nav .owl-next:before {
  left: auto;
  right: 0;
}

.custom-navs-two .owl-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: absolute;
  left: 0;
  right: 0;
  top: -55px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.custom-navs-two .owl-nav:before {
  border-top: 1px solid #000;
  content: "";
  position: absolute;
  left: calc(50% + 32px);
  width: 44.6%;
  opacity: 10%;
}
.custom-navs-two .owl-nav:after {
  border-top: 1px solid #000;
  content: "";
  position: absolute;
  right: calc(50% + 32px);
  width: 44.6%;
  opacity: 10%;
}
.custom-navs-two .owl-nav .owl-prev,
.custom-navs-two .owl-nav .owl-next {
  background-color: #F9F6F1;
  color: var(--theme-color-dark);
  font-size: 8px;
  position: relative;
  line-height: 16px;
  height: 16px;
  width: 16px;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  opacity: 1;
}
.custom-navs-two .owl-nav .owl-prev:hover,
.custom-navs-two .owl-nav .owl-next:hover {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.custom-navs-two .owl-nav .owl-next {
  margin-left: 4px;
  padding-left: 0;
  padding-right: 0;
}

.disable-navs .owl-nav {
  display: none;
}

/*==========================
	Nav Style One
===========================*/
.nav-style-one {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 9;
}
.nav-style-one .next,
.nav-style-one .prev {
  margin-right: 38px;
  padding: 18px 25px;
  padding-left: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.nav-style-one .next .icon,
.nav-style-one .prev .icon {
  position: relative;
  display: inline-block;
}
.nav-style-one .next .icon:before,
.nav-style-one .prev .icon:before {
  position: absolute;
  top: 0;
  margin-top: -20px;
  right: -25px;
  height: 52px;
  width: 52px;
  border: 1px dotted #fff;
  border-radius: 100px;
  content: "";
  z-index: -1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.nav-style-one .next:hover .icon:before,
.nav-style-one .prev:hover .icon:before {
  background-color: rgba(255, 255, 255, 0.15);
  width: 125px;
}
.nav-style-one .prev {
  margin-right: 0;
  padding-left: 25px;
  padding-right: 0;
}
.nav-style-one .prev .icon:before {
  left: -25px;
  right: auto;
}
.nav-style-one .swiper-button-disabled {
  opacity: 0.5;
  pointer-events: none;
}
.nav-style-one.dark .next .icon:before,
.nav-style-one.dark .prev .icon:before {
  border: 1px solid #797979;
}

/*==========================
	Default Tabs
===========================*/
.default-tabs {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}

.default-tabs .tab-buttons {
  position: relative;
  margin-bottom: 30px;
}

.default-tabs .tab-buttons li {
  position: relative;
  float: left;
  font-weight: 600;
  font-size: 18px;
  padding: 15px 35px;
  color: var(--theme-color1);
  line-height: 20px;
  border-radius: 5px;
  background-color: #ffffff;
  cursor: pointer;
  margin-right: 20px;
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.default-tabs .tab-buttons li:last-child {
  margin-right: 0;
}

.default-tabs .tab-buttons li.active-btn {
  background: var(--gradient-1);
  color: #ffffff;
}

.default-tabs .tabs-content {
  position: relative;
  width: 100%;
}

.blockquote-style-one {
  position: relative;
  font-size: 14px;
  line-height: 24px;
  color: var(--theme-color-dark);
  padding: 0 0 0 25px;
  background-color: #fff;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-family: var(--title-font);
  font-weight: 600;
  margin-bottom: 30px;
}
.blockquote-style-one:before {
  position: absolute;
  left: 0;
  top: 3px;
  bottom: 3px;
  width: 3px;
  z-index: 2;
  background-color: var(--theme-color1);
  content: "";
}

.box-shadow-none,
.box_shadow_none {
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
}

.packages-section .swiper-pagination .swiper-pagination-bullet,
.testimonial-carousel-three .swiper-pagination .swiper-pagination-bullet,
.testimonial-carousel .swiper-pagination .swiper-pagination-bullet {
  background-color: transparent;
  border: 1px solid #707070;
  border-radius: 50%;
  height: 12px;
  margin: 0 8.5px;
  opacity: 1;
  width: 12px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.packages-section .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.testimonial-carousel-three .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active,
.testimonial-carousel .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--theme-color1);
  border-color: var(--theme-color1);
  height: 15px;
  width: 15px;
}

.owl-carousel .swiper-wrapper {
  padding-bottom: 70px;
}

.theme-btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  white-space: nowrap;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  font-family: var(--title-font);
}
.theme-btn .btn-title {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

/*Btn Style One*/
.btn-style-one {
  position: relative;
  font-size: 12px;
  line-height: 15px;
  padding: 23px 53px;
  font-family: var(--text-font);
  font-weight: 600;
  overflow: hidden;
  color: var(--theme-color-light);
  background: var(--theme-color1);
  border-radius: 3px;
  border: none;
  letter-spacing: 1px;
  text-transform: uppercase;
  z-index: 0;
}
.btn-style-one:before {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  content: "";
  background-color: var(--theme-color-dark);
  border-radius: 0;
  -webkit-transform: scale(0, 1);
          transform: scale(0, 1);
  -webkit-transform-origin: top right;
          transform-origin: top right;
  transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1), -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
          transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  z-index: -1;
}
.btn-style-one:hover:before {
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}
.btn-style-one:hover {
  color: var(--theme-color-light);
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}
.btn-style-one.dark-bg {
  background-color: var(--theme-color-dark);
}
.btn-style-one.dark-bg:hover {
  color: var(--theme-color-light);
}
.btn-style-one.dark-bg::before {
  background-color: var(--theme-color1);
}
.btn-style-one.light-bg {
  background-color: var(--theme-color-light);
  color: var(--theme-color-dark);
}
.btn-style-one.light-bg:hover {
  color: var(--theme-color-light);
}
.btn-style-one.light-bg::before {
  background-color: var(--theme-color-dark);
}
.btn-style-one.hover-light:hover {
  color: var(--theme-color2);
}
.btn-style-one.hover-light:before {
  background-color: var(--theme-color-light);
}
.btn-style-one.hover-dark:hover {
  color: var(--theme-color-light);
}
.btn-style-one.hover-dark:before {
  background-color: var(--theme-color-dark);
}

/*Btn Style Two*/
.btn-style-two {
  background: var(--theme-color1);
  border-radius: 3px;
  border: none;
  color: var(--theme-color-light);
  font-family: var(--text-font);
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: 15.12px;
  overflow: hidden;
  padding: 22px 37px 22px 36px;
  position: relative;
  text-transform: uppercase;
  z-index: 0;
}
.btn-style-two:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  content: "";
  background-color: var(--theme-color-dark);
  border-radius: 0;
  -webkit-transform: scale(0, 1);
          transform: scale(0, 1);
  -webkit-transform-origin: top right;
          transform-origin: top right;
  transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1), -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
          transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  z-index: -1;
}
.btn-style-two:hover:before {
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}
.btn-style-two:hover {
  color: var(--theme-color-light);
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}
.btn-style-two.dark-bg {
  background-color: var(--theme-color-dark);
}
.btn-style-two.dark-bg:hover {
  color: var(--theme-color-light);
}
.btn-style-two.dark-bg::before {
  background-color: var(--theme-color1);
}
.btn-style-two.light-bg {
  background-color: var(--theme-color-light);
  border: 1px solid var(--theme-color-light);
  color: var(--theme-color1);
}
.btn-style-two.light-bg:hover {
  color: var(--theme-color-light);
}
.btn-style-two.light-bg::before {
  background-color: var(--theme-color-dark);
}
.btn-style-two.transparent-bg {
  background-color: transparent;
  border: 1px solid var(--theme-color-light);
  color: var(--theme-color-light);
}
.btn-style-two.transparent-bg:hover {
  color: var(--theme-color-light);
}
.btn-style-two.transparent-bg::before {
  background-color: var(--theme-color1);
}
.btn-style-two.hover-light:hover {
  color: var(--theme-color2);
}
.btn-style-two.hover-light:before {
  background-color: var(--theme-color-light);
}
.btn-style-two.hover-dark:hover {
  color: var(--theme-color-light);
}
.btn-style-two.hover-dark:before {
  background-color: var(--theme-color-dark);
}

/*Btn Style One*/
.btn-style-transparent {
  background: transparent;
  border-radius: 3px;
  border: 1px solid var(--theme-color1);
  color: var(--theme-color1);
  font-family: var(--text-font);
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  line-height: 15px;
  overflow: hidden;
  padding: 23px 53px;
  position: relative;
  text-transform: uppercase;
  z-index: 0;
}
.btn-style-transparent:before {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  content: "";
  background-color: var(--theme-color-dark);
  border-radius: 0;
  -webkit-transform: scale(0, 1);
          transform: scale(0, 1);
  -webkit-transform-origin: top right;
          transform-origin: top right;
  transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1), -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
          transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  z-index: -1;
}
.btn-style-transparent:hover:before {
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}
.btn-style-transparent:hover {
  color: var(--theme-color-light);
  -webkit-box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/*** 

====================================================================
  Anim Icons
====================================================================

***/
.reveal {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  visibility: hidden;
  overflow: hidden;
}
.reveal img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -webkit-transform-origin: left;
          transform-origin: left;
}

.anim-icons {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 100%;
  width: 100%;
  max-width: calc(var(--container-width) - 30px);
  margin: 0 auto;
}

[text-split] {
  opacity: 0;
}

.word {
  overflow: hidden;
  padding-bottom: 0;
  margin-bottom: -0.1em;
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
}

.anim-icons.full-width {
  max-width: 100%;
}

.anim-icons .icon {
  position: absolute;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
}

.icon-wheel-1 {
  width: 144px;
  height: 163px;
}

.icon-wheel-2 {
  width: 144px;
  height: 163px;
}

.icon-wheel-3 {
  width: 149px;
  height: 147px;
}

.icon-wheel-5 {
  width: 144px;
  height: 163px;
}

.icon-wheel-compass-1 {
  width: 171px;
  height: 176px;
}

.icon-wheel-compass-2 {
  width: 97px;
  height: 99px;
}

.icon-wheel-compass-3 {
  width: 103px;
  height: 106px;
}

.icon-anchor-1 {
  width: 138px;
  height: 136px;
}

.icon-sailboat-line-1 {
  width: 431px;
  height: 705px;
}

.icon-sailboat-line-3 {
  width: 242px;
  height: 250px;
}

.icon-sailboat-line-4 {
  width: 369px;
  height: 728px;
}

.icon-big-boat-1 {
  width: 800px;
  height: 464px;
}

.icon-big-boat-2 {
  width: 229px;
  height: 739px;
}

.icon-big-boat-3 {
  width: 373px;
  height: 126px;
}

.icon-big-boat-5 {
  width: 229px;
  height: 739px;
}

.icon-pattern1 {
  width: 494px;
  height: 465px;
}

.icon-pattern2 {
  width: 247px;
  height: 477px;
}

.icon-plane-1 {
  width: 350px;
  height: 349px;
}

.icon-plane-2 {
  width: 106px;
  height: 640px;
}

.icon-plane-3 {
  width: 455px;
  height: 420px;
}

.icon-plane-4 {
  width: 301px;
  height: 336px;
}

.icon-plane-5 {
  width: 313px;
  height: 253px;
}

.icon-plane-6 {
  width: 188px;
  height: 110px;
}

.icon-plane-7 {
  width: 256px;
  height: 255px;
}

.icon-plane-8 {
  width: 374px;
  height: 194px;
}

.icon-plane-9 {
  width: 313px;
  height: 253px;
}

.icon-big-arrow {
  width: 1279px;
  height: 250px;
}

.bounce-y {
  -webkit-animation: bounce-y 10s infinite linear;
          animation: bounce-y 10s infinite linear;
}

.bounce-x {
  -webkit-animation: bounce-x 10s infinite linear;
          animation: bounce-x 10s infinite linear;
}

.zoom-one {
  -webkit-animation: zoom-one 10s infinite linear;
          animation: zoom-one 10s infinite linear;
}

.zoom-two {
  -webkit-animation: zoom-two 5s infinite linear;
          animation: zoom-two 5s infinite linear;
}

.zoom-three {
  -webkit-animation: zoom-three 5s infinite linear;
          animation: zoom-three 5s infinite linear;
}

@-webkit-keyframes float {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}

@keyframes float {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-10px);
            transform: translateY(-10px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes bounce-y {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@keyframes bounce-y {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-30px);
            transform: translateY(-30px);
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
  }
}
@-webkit-keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
            transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@keyframes bounce-x {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  50% {
    -webkit-transform: translateX(30px);
            transform: translateX(30px);
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
}
@-webkit-keyframes zoom-one {
  0% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
}
@keyframes zoom-one {
  0% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
  }
  100% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
}
@-webkit-keyframes zoom-two {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@keyframes zoom-two {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
  50% {
    -webkit-transform: scale(0.95);
            transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
            transform: scale(1);
  }
}
@-webkit-keyframes round-shape-anim {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(194, 167, 78, 0.08), 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08);
            box-shadow: 0 0 0 0 rgba(194, 167, 78, 0.08), 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08);
  }
  100% {
    -webkit-box-shadow: 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08), 0 0 0 70px rgba(194, 167, 78, 0);
            box-shadow: 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08), 0 0 0 70px rgba(194, 167, 78, 0);
  }
}
@keyframes round-shape-anim {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(194, 167, 78, 0.08), 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08);
            box-shadow: 0 0 0 0 rgba(194, 167, 78, 0.08), 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08);
  }
  100% {
    -webkit-box-shadow: 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08), 0 0 0 70px rgba(194, 167, 78, 0);
            box-shadow: 0 0 0 30px rgba(194, 167, 78, 0.08), 0 0 0 50px rgba(194, 167, 78, 0.08), 0 0 0 70px rgba(194, 167, 78, 0);
  }
}
.overlay-anim {
  position: relative;
}
.overlay-anim:after {
  background: rgba(255, 255, 255, 0.3);
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 0;
  opacity: 1;
  z-index: 9;
  pointer-events: none;
}
.overlay-anim:hover:after {
  height: 100%;
  opacity: 0;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}

.overlay-anim-two {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}
.overlay-anim-two:hover:after {
  left: 0;
  right: 0;
  opacity: 0;
  -webkit-transition: all 400ms linear;
  transition: all 400ms linear;
}
.overlay-anim-two:after {
  background: rgba(255, 255, 255, 0.3);
  bottom: 0;
  content: "";
  left: 50%;
  position: absolute;
  right: 51%;
  top: 0;
  opacity: 1;
  pointer-events: none;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
}

.circle {
  position: fixed;
  width: 10px;
  height: 10px;
  left: -10px;
  top: -10px;
  border-radius: 100%;
  z-index: 1;
  pointer-events: none;
  z-index: 10000;
  -webkit-transform: scale(1);
          transform: scale(1);
}
@supports (mix-blend-mode: difference) {
  .circle {
    background-color: white;
    mix-blend-mode: difference;
  }
}
@media only screen and (max-width: 1023px) {
  .circle {
    display: none !important;
  }
}

.circle-follow {
  position: fixed;
  mix-blend-mode: difference;
  width: 30px;
  height: 30px;
  left: -21px;
  top: -21px;
  border-radius: 100%;
  z-index: 1;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  pointer-events: none;
  z-index: 10000;
  -webkit-transform: scale(1);
          transform: scale(1);
}
@supports (mix-blend-mode: difference) {
  .circle-follow {
    border: 1px solid #fff;
    mix-blend-mode: difference;
  }
}
@media only screen and (max-width: 1023px) {
  .circle-follow {
    display: none !important;
  }
}

/* Animate 1 */
.owl-carousel .animate-7,
.owl-carousel .animate-6,
.owl-carousel .animate-5,
.owl-carousel .animate-4,
.owl-carousel .animate-3,
.owl-carousel .animate-2,
.owl-carousel .animate-1 {
  opacity: 0;
  -webkit-transform: translateY(100px);
          transform: translateY(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .animate-x {
  opacity: 0;
  -webkit-transform: translateX(100px);
          transform: translateX(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .active .animate-7,
.owl-carousel .active .animate-6,
.owl-carousel .active .animate-5,
.owl-carousel .active .animate-4,
.owl-carousel .active .animate-3,
.owl-carousel .active .animate-2,
.owl-carousel .active .animate-1 {
  opacity: 1;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.owl-carousel .active .animate-2 {
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}
.owl-carousel .active .animate-3 {
  -webkit-transition-delay: 600ms;
          transition-delay: 600ms;
}
.owl-carousel .active .animate-4 {
  -webkit-transition-delay: 900ms;
          transition-delay: 900ms;
}
.owl-carousel .active .animate-5 {
  -webkit-transition-delay: 1200ms;
          transition-delay: 1200ms;
}
.owl-carousel .active .animate-6 {
  -webkit-transition-delay: 1500ms;
          transition-delay: 1500ms;
}
.owl-carousel .active .animate-7 {
  -webkit-transition-delay: 1800ms;
          transition-delay: 1800ms;
}

/*** 

====================================================================
  Search Popup
====================================================================

***/
.search-popup {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 100%;
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  overflow: hidden;
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.search-popup .search-back-drop {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: var(--theme-color-dark);
  opacity: 0.95;
}
.search-popup .close-search {
  position: absolute;
  top: 30px;
  right: 30px;
  font-size: 26px;
  color: var(--theme-color-light);
  z-index: 3;
  border-radius: 50%;
  background-color: transparent;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.search-popup .search-inner {
  position: relative;
  display: block;
  top: 40%;
  height: auto;
  z-index: 1;
  width: calc(100% - 60px);
  max-width: 800px;
  margin: auto;
  opacity: 0;
  -webkit-transform: translateY(-50px);
          transform: translateY(-50px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.search-popup .form-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.search-popup .form-group input[type=search],
.search-popup .form-group input[type=text] {
  position: relative;
  display: block;
  line-height: 20px;
  font-size: 16px;
  width: 100%;
  height: 50px;
  border: 1px solid #e1e6dc;
  padding: 15px 20px;
  color: #707070;
  background: #ffffff;
  border-radius: 5px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.search-popup .form-group input[type=search]:focus,
.search-popup .form-group input[type=text]:focus {
  border-color: var(--border-theme-color2);
}
.search-popup .form-group button {
  position: absolute;
  right: 5px;
  top: 5px;
  height: 40px;
  width: 40px;
  display: block;
  font-size: 18px;
  color: var(--theme-color-dark);
  line-height: 40px;
  border-radius: 5px;
  font-weight: normal;
  background: #ffffff;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.search-popup .form-group button:hover {
  color: var(--theme-color-dark);
}
.search-popup textarea::-webkit-input-placeholder, .search-popup input::-webkit-input-placeholder {
  color: inherit;
}
.search-popup textarea::-moz-placeholder, .search-popup input::-moz-placeholder {
  color: inherit;
}
.search-popup textarea:-ms-input-placeholder, .search-popup input:-ms-input-placeholder {
  color: inherit;
}
.search-popup textarea::-ms-input-placeholder, .search-popup input::-ms-input-placeholder {
  color: inherit;
}
.search-popup textarea::placeholder,
.search-popup input::placeholder {
  color: inherit;
}

.moblie-search-active .search-popup {
  opacity: 1;
  visibility: visible;
  -webkit-transform: scale(1);
          transform: scale(1);
  border-radius: 0%;
}
.moblie-search-active .search-popup .search-inner {
  opacity: 1;
  -webkit-transform: translateY(0);
          transform: translateY(0);
  -webkit-transition-delay: 500ms;
          transition-delay: 500ms;
}

/*** 

====================================================================
Main Header
====================================================================

***/
.main-header {
  position: relative;
  width: 100%;
  z-index: 999;
}

.header-top {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.header-top .inner-container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}
.header-top .top-left {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 1399.98px) {
  .header-top .top-center {
    display: none;
  }
}
.header-top .top-right {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.header-top .useful-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 10px 0;
}
.header-top .useful-links li {
  position: relative;
  margin-right: 30px;
  font-size: 12px;
  font-weight: 500;
  color: #bdbdbd;
  line-height: 20px;
  font-family: var(--title-font);
}
.header-top .useful-links li a {
  color: #bdbdbd;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.header-top .useful-links li a:hover {
  color: var(--theme-color1);
}

.main-header .logo {
  position: relative;
  display: block;
}
.main-header .logo img {
  max-width: 100%;
  height: auto;
}
.main-header .main-box {
  position: relative;
  left: 0px;
  top: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 1023.98px) {
  .main-header .main-box {
    padding: 20px 0;
  }
}
.main-header .main-box .nav-outer {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  width: 100%;
}
@media (max-width: 1023.98px) {
  .main-header .main-box .nav-outer {
    -webkit-box-pack: end;
        -ms-flex-pack: end;
            justify-content: end;
  }
}

.main-menu {
  position: relative;
}
@media only screen and (max-width: 1023px) {
  .main-menu {
    display: none;
  }
}

.main-menu .navbar-header {
  display: none;
}

.main-menu .navbar-collapse {
  padding: 0px;
}

.main-menu .navigation {
  position: relative;
  margin: 0 0 0 150px;
  top: 0;
}
@media (max-width: 1649.98px) {
  .main-menu .navigation {
    margin: 0 0 0 100px;
  }
}
@media (max-width: 1399.98px) {
  .main-menu .navigation {
    margin: 0 0 0 70px;
  }
}
@media (max-width: 1182.98px) {
  .main-menu .navigation {
    margin: 0 0 0 44px;
  }
}

.main-menu .navigation > li {
  position: relative;
  float: left;
  padding: 42.5px 0;
  margin-right: 60px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 1649.98px) {
  .main-menu .navigation > li {
    margin-right: 70px;
  }
}
@media (max-width: 1399.98px) {
  .main-menu .navigation > li {
    margin-right: 40px;
  }
}
@media (max-width: 1084.98px) {
  .main-menu .navigation > li {
    margin-right: 32px;
  }
}
.main-menu .navigation > li:hover:before, .main-menu .navigation > li.current:before {
  color: var(--theme-color1);
  left: 0;
  width: 100%;
}
.main-menu .navigation > li:last-child {
  margin-right: 0;
}
.main-menu .navigation > li:last-child:after {
  display: none;
}
.main-menu .navigation > li > a {
  position: relative;
  display: block;
  text-align: center;
  opacity: 1;
  color: var(--theme-color-dark);
  font-size: 18px;
  line-height: 22.68px;
  font-weight: 500;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-menu .navigation > li > a:hover {
  color: var(--theme-color1);
}
.main-menu .navigation > li > a .icon {
  position: relative;
  font-size: 20px;
  line-height: 24px;
  margin-left: 10px;
}
.main-menu .navigation > li.dropdown > a {
  padding-right: 14px;
  margin-right: -14px;
}
.main-menu .navigation > li.dropdown > a:after {
  content: "\f107";
  display: block;
  font-family: "Font Awesome 6 Pro";
  font-size: 12px;
  font-weight: 900;
  height: 20px;
  line-height: 24px;
  margin-top: -2px;
  position: absolute;
  right: 0;
  top: 50%;
  width: 10px;
  z-index: 5;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.main-menu .navigation > li.dropdown:hover > ul {
  visibility: visible;
  opacity: 1;
  top: 100%;
  margin-top: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-menu .navigation > li > ul {
  position: absolute;
  left: 0px;
  top: 100%;
  width: 220px;
  z-index: 100;
  padding: 10px 0 0;
  background-color: #ffffff;
  margin-top: 30px;
  opacity: 0;
  display: none;
  -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
          box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
}
.main-menu .navigation > li > ul.from-right {
  left: auto;
  right: 0px;
}
.main-menu .navigation > li > ul > li {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #ebf1f5;
}
.main-menu .navigation > li > ul > li:last-child {
  border-bottom: none;
}
.main-menu .navigation > li > ul > li > a {
  position: relative;
  display: block;
  padding: 10px 0px;
  line-height: 29px;
  font-weight: 400;
  font-size: 16px;
  color: var(--theme-color-dark);
  text-align: left;
  margin: 0 30px;
  text-transform: capitalize;
  -webkit-transition: all 200ms ease;
  transition: all 200ms ease;
}
.main-menu .navigation > li > ul > li:hover > a {
  color: var(--theme-color1);
}
.main-menu .navigation > li > ul > li.dropdown > a:after {
  font-family: "Font Awesome 6 Pro";
  content: "\f105";
  position: absolute;
  right: 0;
  top: 11px;
  display: block;
  line-height: 24px;
  font-size: 11px;
  font-weight: 900;
  z-index: 5;
}
.main-menu .navigation > li > ul > li.dropdown:hover > ul {
  visibility: visible;
  opacity: 1;
  top: 0px;
  margin-top: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.main-menu .navigation > li > ul > li > ul {
  position: absolute;
  left: 100%;
  top: 0px;
  width: 220px;
  z-index: 100;
  display: none;
  background-color: var(--theme-color-light);
  opacity: 0;
  padding: 10px 0 0;
  margin-top: 10px;
  -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
  -webkit-box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
          box-shadow: 2px 2px 5px 1px rgba(0, 0, 0, 0.05), -2px 0px 5px 1px rgba(0, 0, 0, 0.05);
}
.main-menu .navigation > li > ul > li > ul > li {
  position: relative;
  border-bottom: 1px solid #ebf1f5;
  width: 100%;
}
.main-menu .navigation > li > ul > li > ul > li:last-child {
  border-bottom: none;
}
.main-menu .navigation > li > ul > li > ul > li > a {
  position: relative;
  display: block;
  padding: 10px 0;
  line-height: 24px;
  font-weight: 400;
  font-size: 16px;
  color: var(--theme-color-dark);
  text-align: left;
  margin: 0 30px;
  text-transform: capitalize;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-menu .navigation > li > ul > li > ul > li > a:hover {
  color: var(--theme-color1);
}

.main-menu .navigation li.dropdown .dropdown-btn {
  position: absolute;
  right: 10px;
  top: 8px;
  width: 34px;
  height: 30px;
  border: 1px solid var(--theme-color-light);
  text-align: center;
  font-size: 16px;
  line-height: 26px;
  color: var(--theme-color-light);
  cursor: pointer;
  z-index: 5;
  display: none;
}

.main-header .outer-box {
  max-width: 1650px;
  margin: 0 auto;
  padding: 0 15px 1px;
}
@media (max-width: 1023.98px) {
  .main-header .outer-box {
    padding: 0;
  }
}

.main-header .ui-btn {
  position: relative;
  display: block;
  line-height: 30px;
  text-align: center;
  background: none;
  font-size: 22px;
  color: var(--theme-color-dark);
  margin-left: 15px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-header .ui-btn:hover {
  color: var(--theme-color1);
}

.main-header .info-btn {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  font-size: 15px;
  padding-left: 70px;
  color: var(--theme-color-dark);
  text-align: left;
  font-weight: 700;
  line-height: 20px;
  min-height: 62px;
  font-family: var(--title-font);
}
.main-header .info-btn small {
  display: block;
  font-size: 12px;
  line-height: 1em;
  font-weight: 400;
  color: #868686;
  margin-bottom: 0px;
}
.main-header .info-btn i {
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -24px;
  line-height: 48px;
  height: 48px;
  width: 48px;
  text-align: center;
  border-radius: 50%;
  font-size: 22px;
  color: #fff;
  background-color: var(--theme-color1);
}
.main-header .info-btn i:after {
  position: absolute;
  top: -7px;
  right: -7px;
  bottom: -7px;
  left: -7px;
  background-color: var(--theme-color1);
  opacity: 0.1;
  border-radius: 50%;
  content: "";
}
.main-header .info-btn:hover {
  color: var(--theme-color1);
}

.contact-list {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.contact-list li {
  position: relative;
  padding-left: 65px;
  margin-left: 35px;
}
.contact-list li .icon {
  position: absolute;
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  background-color: rgba(255, 255, 255, 0.05);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 20px;
  color: var(--theme-color1);
}
.contact-list li .title {
  font-size: 16px;
  line-height: 20px;
  color: #ffffff;
  font-weight: 600;
  font-family: var(--text-font);
  margin-bottom: 2px;
}
.contact-list li .text {
  font-size: 16px;
  line-height: 24px;
  color: #cecece;
}
.contact-list li .text a {
  color: inherit;
}

.main-header .cart-btn {
  position: relative;
  margin: 0;
  color: var(--theme-color-light);
  margin-right: 30px;
  text-align: center;
}
@media (max-width: 1199.98px) {
  .main-header .cart-btn {
    display: none;
  }
}
.main-header .cart-btn:before {
  background: #e5e5e5;
  content: "";
  position: absolute;
  right: -38px;
  width: 1px;
  height: 80%;
  top: 3px;
}
.main-header .cart-btn:hover {
  color: var(--theme-color1);
}
.main-header .cart-btn .count {
  position: absolute;
  top: 1px;
  right: -8px;
  height: 21px;
  width: 21px;
  line-height: 21px;
  font-size: 12px;
  border-radius: 50%;
  color: #ffffff;
  background-color: var(--theme-color1);
  text-align: center;
}

.header-search {
  position: relative;
  width: 160px;
}
.header-search .form-group {
  position: relative;
}
.header-search .form-group .icon {
  position: absolute;
  top: 0px;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  line-height: 20px;
  font-size: 20px;
  padding: 5px 0;
  color: rgba(255, 255, 255, 0.6);
  background: none;
}

.main-header .search-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 72px;
  padding-right: 30px;
  text-align: center;
}
@media (max-width: 1699.98px) {
  .main-header .search-btn {
    padding-right: 0;
  }
}
.main-header .search-btn:hover {
  color: var(--theme-color1);
}
.main-header .search-btn:hover strong {
  color: var(--theme-color1);
}

/*** 

====================================================================
    Header Style
====================================================================

***/
.main-header {
  background-color: #F9F6F1;
  position: relative;
}
.main-header .header-top {
  padding: 6.5px 0;
}
@media (max-width: 1023.98px) {
  .main-header .header-top {
    display: none;
  }
}
.main-header .header-top .inner-container .list-style-one {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.main-header .header-top .inner-container .list-style-one i {
  color: var(--theme-color1);
  font-size: 12px;
}
.main-header .header-top .inner-container .list-style-one.style-two li {
  margin-right: 30px;
  color: var(--theme-color2);
}
.main-header .header-top .inner-container .list-style-one.style-two li:last-child {
  margin-right: 45px !important;
}
.main-header .header-top .inner-container .list-style-one.style-two li a {
  color: var(--theme-color2);
}
.main-header .header-top .inner-container .list-style-one.style-two li a:hover {
  color: var(--theme-color1);
}
.main-header .header-top .inner-container .social-icon-one li {
  position: relative;
  margin: 0 16px 0 0;
}
.main-header .header-top .inner-container .social-icon-one li:last-child {
  margin-right: 0;
}
.main-header .header-top .inner-container .social-icon-one li a {
  font-size: 15px;
  color: var(--theme-color2);
}
.main-header .header-top .inner-container .social-icon-one li a:hover {
  color: var(--theme-color1);
}
.main-header .header-top .inner-container .top-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.main-header .header-top .inner-container .top-right .list-style-two li {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 0;
  padding-left: 0;
  margin-right: 40px;
}
.main-header .header-top .inner-container .top-right .list-style-two li a {
  color: var(--theme-color-dark);
  font-weight: 400;
}
.main-header .header-top .inner-container .top-right .list-style-two li a.active {
  color: var(--theme-color1);
}
.main-header .header-top .inner-container .top-right .list-style-two li a:hover {
  color: var(--theme-color1);
}
.main-header .header-top.light .inner-container .list-style-one li {
  color: var(--theme-color-light);
}
.main-header .header-top.light .inner-container .list-style-one li a {
  color: var(--theme-color-light);
}
.main-header .header-top.light .inner-container .list-style-one li a:hover {
  color: var(--theme-color1);
}
.main-header .header-top.light .inner-container .list-style-one i {
  color: var(--theme-color-light);
}
.main-header .header-top.light .inner-container .list-style-one.style-two li {
  color: var(--theme-color-light);
}
.main-header .header-top.light .inner-container .list-style-one.style-two li a {
  color: var(--theme-color-light);
}
.main-header .header-top.light .inner-container .list-style-one.style-two li a:hover {
  color: var(--theme-color1);
}
.main-header .header-top.light .inner-container .social-icon-one li:before {
  background-color: rgba(255, 255, 255, 0.1);
}
.main-header .header-top.light .inner-container .social-icon-one li:last-child:after {
  background-color: rgba(255, 255, 255, 0.1);
}
.main-header .header-top.light .inner-container .social-icon-one li a {
  color: var(--theme-color-light);
}
.main-header .header-top.light .inner-container .social-icon-one li a:hover {
  color: var(--theme-color1);
}
.main-header .header-lower {
  background-color: var(--theme-color-light);
  border-radius: 10px 10px 0 0;
  padding: 0 40px;
}
@media (max-width: 1399.98px) {
  .main-header .header-lower {
    padding: 0 30px;
  }
}
@media (max-width: 1199.98px) {
  .main-header .header-lower {
    padding: 0 15px;
  }
}
.main-header .header-lower .logo-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  min-width: 194px;
  height: 48px;
}
.main-header .header-lower .outer-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  padding-left: 30px;
  padding-right: 0;
}
@media (max-width: 1399.98px) {
  .main-header .header-lower .outer-box {
    padding-left: 9px;
    padding-right: 0;
  }
}
@media (max-width: 1023.98px) {
  .main-header .header-lower .outer-box {
    padding-left: 0;
  }
}
.main-header .header-lower .outer-box:before {
  content: "";
  position: absolute;
  border: 1px solid #F9F6F1;
  left: 0;
  height: 108px;
}
@media (max-width: 1023.98px) {
  .main-header .header-lower .outer-box:before {
    display: none;
  }
}
@media (max-width: 1399.98px) {
  .main-header .header-lower .outer-box .theme-btn {
    padding: 17px 27px;
  }
}
@media (max-width: 1023.98px) {
  .main-header .header-lower .outer-box .theme-btn {
    display: none;
  }
}
.main-header .header-lower .outer-box .ui-btn {
  width: 30px;
  height: 30px;
  line-height: 30px;
  margin-right: 25px;
  padding: 0;
  color: var(--theme-color-dark);
}
@media (max-width: 1399.98px) {
  .main-header .header-lower .outer-box .ui-btn {
    margin-right: 14px;
  }
}
@media (max-width: 575.98px) {
  .main-header .header-lower .outer-box .ui-btn {
    margin-right: 0;
    margin-left: 0;
  }
}
.main-header .header-lower .outer-box .ui-btn:hover {
  color: var(--theme-color1);
}
.main-header .header-lower .outer-box .ui-btn i {
  font-weight: 500;
}
@media (max-width: 767.98px) {
  .main-header .header-lower .outer-box .btn-box {
    display: none;
  }
}
.main-header .header-lower.light .main-menu .navigation > li > a {
  color: var(--theme-color-light);
}
.main-header .header-lower.light .outer-box .ui-btn {
  color: var(--theme-color-light);
}
.main-header .header-lower.light .outer-box .ui-btn:hover {
  color: var(--theme-color1);
}
.main-header .header-lower.light .outer-box .ui-btn.cart-btn:before {
  background-color: rgba(255, 255, 255, 0.1);
}
.main-header .header-lower.light .outer-box .call-btn a {
  color: var(--theme-color-light);
}
.main-header .header-lower.light .outer-box .call-btn a strong {
  color: var(--theme-color-light);
}
.main-header .header-lower.light .outer-box .call-btn a:hover {
  color: var(--theme-color1);
}
.main-header .header-lower.light .outer-box .call-btn a:hover strong {
  color: var(--theme-color1);
}

.header-style-three {
  background-color: #f4ecdf;
}

.header-style-two {
  padding: 0;
}
.header-style-two .header-lower {
  padding: 0;
}
.header-style-two .header-lower .main-menu .navigation {
  margin: 0 0 0 80px;
}
.header-style-two .header-lower .main-box {
  max-width: 1650px;
  padding: 0 10px;
  margin: 0 auto;
}
.header-style-two .header-lower .main-box .outer-box:before {
  content: "";
  display: none;
}
.header-style-two .header-lower .main-box .outer-box .mobile-nav-toggler {
  display: block;
  background-color: var(--theme-color-dark);
  padding: 43.5px 0;
  width: 100px;
  margin: 0;
  font-size: 39px;
  text-align: center;
}
.header-style-two .header-lower .main-box .outer-box .mobile-nav-toggler:hover span {
  color: var(--theme-color1);
}
.header-style-two .header-lower .main-box .outer-box .mobile-nav-toggler span {
  color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.header-style-two .header-lower .main-box .outer-box .cart-btn {
  background-color: var(--theme-color1);
  padding: 53.999px 0;
  width: 100px;
  line-height: 7px;
  margin: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.header-style-two .header-lower .main-box .outer-box .cart-btn:hover i {
  color: var(--theme-color-dark);
}
.header-style-two .header-lower .main-box .outer-box .cart-btn:hover .count {
  background-color: var(--theme-color-light);
  color: var(--theme-color-dark);
}
.header-style-two .header-lower .main-box .outer-box .cart-btn:before {
  content: "";
  display: none;
}
.header-style-two .header-lower .main-box .outer-box .cart-btn:after {
  content: "";
  display: none;
}
.header-style-two .header-lower .main-box .outer-box .cart-btn i {
  color: var(--theme-color-light);
  font-size: 32px;
  font-weight: 300;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.header-style-two .header-lower .main-box .outer-box .cart-btn .count {
  background-color: var(--theme-color-dark);
  top: 34px;
  right: 23px;
  width: 22px;
  line-height: 22px;
  font-size: 10px;
  font-weight: 500;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.header-style-two .header-lower .main-box .nav-outer .mobile-nav-toggler {
  display: none;
}

/***

====================================================================
    Sticky Header
====================================================================

***/
.sticky-header {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  left: 0px;
  top: 0px;
  width: 100%;
  padding: 0px 0px;
  z-index: 99999;
  background: #ffffff;
  -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.sticky-header.fixed-header {
  opacity: 1;
  z-index: 9999;
  visibility: visible;
}

.sticky-header .logo {
  padding: 19px 0 20px;
}
.sticky-header .logo img {
  max-height: 40px;
}
.sticky-header .nav-outer {
  position: relative;
  background: none;
}
.sticky-header .inner-container {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.sticky-header .main-menu .navigation > li {
  margin: 0;
  margin-left: 60px;
  padding: 20px 0;
}

.sticky-header .main-menu .navigation.onepage-nav > li {
  margin-left: 35px;
}

.header-style-one.header-style-home5 .sticky-header .main-menu .navigation.onepage-nav > li {
  margin-right: 10px;
}

@media (max-width: 1499.98px) {
  .main-menu .navigation.onepage-nav > li,
  .sticky-header .main-menu .navigation.onepage-nav > li {
    margin-right: 28px;
  }
}
.sticky-header .main-menu .navigation > li.current > a,
.sticky-header .main-menu .navigation > li:hover > a {
  color: var(--theme-color-dark);
}

.sticky-header .outer-box,
.sticky-header .navbar-header {
  display: none;
}

.sticky-header .mobile-nav-toggler {
  color: var(--theme-color-dark);
}

@media only screen and (min-width: 768px) {
  .main-menu .navigation > li > ul,
  .main-menu .navigation > li > ul > li > ul {
    display: block !important;
    visibility: hidden;
    opacity: 0;
  }
}
/***

==================================================================
    Instagram Section
==================================================================

***/
.instagram-section {
  padding: 100px 0;
  position: relative;
}
@media (max-width: 767.98px) {
  .instagram-section {
    padding: 80px 0;
  }
}
.instagram-section .icon-instagram1-6 {
  background-image: url(../images/resource/instagram1-6.png);
  position: absolute;
  width: 201px;
  height: 360px;
  right: 0;
  bottom: 51px;
}
@media (max-width: 1399.98px) {
  .instagram-section .icon-instagram1-6 {
    display: none;
  }
}
.instagram-section .icon-instagram1-7 {
  background-image: url(../images/resource/instagram1-7.png);
  position: absolute;
  width: 201px;
  height: 360px;
  left: 0;
  bottom: 51px;
}
@media (max-width: 1399.98px) {
  .instagram-section .icon-instagram1-7 {
    display: none;
  }
}
.instagram-section .sec-title h4 {
  font-weight: 500;
  position: relative;
}
.instagram-section .sec-title h4:before {
  border: 1px solid #1C1A1D;
  content: "";
  position: absolute;
  left: calc(50% + 170px);
  width: 36.84%;
  opacity: 10%;
  top: 18px;
}
.instagram-section .sec-title h4:after {
  border: 1px solid #1C1A1D;
  content: "";
  position: absolute;
  right: calc(50% + 170px);
  width: 36.84%;
  opacity: 10%;
  top: 18px;
}
.instagram-section .instagram-block .inner-box .image-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (max-width: 991.98px) {
  .instagram-section .instagram-block .inner-box .image-box {
    margin-bottom: 25px;
  }
}
.instagram-section .instagram-block .inner-box .image-box:hover .image:before {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.instagram-section .instagram-block .inner-box .image-box:hover .image a img {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.instagram-section .instagram-block .inner-box .image-box:hover .icon {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.instagram-section .instagram-block .inner-box .image-box .image {
  border-radius: 11px;
  margin-bottom: 0;
  overflow: hidden;
  width: 100%;
}
.instagram-section .instagram-block .inner-box .image-box .image:before {
  border-radius: 11px;
  background-color: rgba(194, 167, 78, 0.8);
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  -webkit-transform: scale(0);
          transform: scale(0);
  pointer-events: none;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.instagram-section .instagram-block .inner-box .image-box .image a img {
  width: 100%;
  border-radius: 5px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.instagram-section .instagram-block .inner-box .image-box .icon {
  color: var(--theme-color-light);
  position: absolute;
  pointer-events: none;
  font-size: 68px;
  z-index: 2;
  -webkit-transform: scale(0);
          transform: scale(0);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/*** 

====================================================================
      Mobile Menu
====================================================================

***/
.mobile-nav-toggler {
  position: relative;
  font-size: 28px;
  line-height: 20px;
  cursor: pointer;
  color: var(--theme-color2);
  display: none;
  top: 0;
  margin-left: 20px;
  -webkit-box-ordinal-group: 9;
      -ms-flex-order: 8;
          order: 8;
}
@media only screen and (max-width: 1023px) {
  .mobile-nav-toggler {
    display: block;
  }
}

.mobile-menu {
  position: fixed;
  right: 0;
  top: 0;
  width: 300px;
  max-width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  z-index: 999999;
}
.mobile-menu .menu-backdrop {
  position: fixed;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  visibility: hidden;
}
.mobile-menu .upper-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  padding: 20px 20px;
}
.mobile-menu .close-btn {
  position: relative;
  top: -10px;
  text-align: center;
  font-size: 18px;
  color: #ffffff;
  cursor: pointer;
  z-index: 10;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
  -webkit-transform: translateY(-50px);
          transform: translateY(-50px);
}
.mobile-menu .close-btn:hover {
  opacity: 0.5;
}
.mobile-menu .nav-logo {
  position: relative;
  text-align: left;
  width: 100%;
}
.mobile-menu .nav-logo img {
  max-height: 40px;
}
.mobile-menu .menu-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow-y: auto;
  background: var(--theme-color-dark);
  padding: 0px 0px;
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  border-radius: 0px;
  -webkit-transform: translateX(101%);
          transform: translateX(101%);
}

.mobile-menu-visible {
  overflow: hidden;
}
.mobile-menu-visible .mobile-menu {
  opacity: 1;
  visibility: visible;
}
.mobile-menu-visible .mobile-menu .menu-backdrop {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.mobile-menu-visible .mobile-menu .menu-box {
  opacity: 1;
  visibility: visible;
  -webkit-transition: all 0.4s ease 200ms;
  transition: all 0.4s ease 200ms;
  -webkit-transform: translateX(0%);
          transform: translateX(0%);
}
.mobile-menu-visible .mobile-menu .close-btn {
  -webkit-transform: translateY(0px);
          transform: translateY(0px);
}
.mobile-menu-visible .scroll-to-top {
  opacity: 0;
  visibility: hidden;
}

.mobile-menu .navigation {
  position: relative;
  display: block;
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li {
  position: relative;
  display: block;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li > ul > li:last-child {
  border-bottom: none;
}
.mobile-menu .navigation li > ul > li:first-child {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li > a {
  position: relative;
  display: block;
  line-height: 24px;
  padding: 10px 20px;
  font-size: 16px;
  color: #ffffff;
  font-weight: 400;
  text-transform: capitalize;
}
.mobile-menu .navigation li:hover > a, .mobile-menu .navigation li.current > a {
  color: var(--theme-color1);
}
.mobile-menu .navigation li.dropdown .dropdown-btn {
  position: absolute;
  right: 0px;
  top: 0px;
  width: 44px;
  height: 44px;
  text-align: center;
  font-size: 16px;
  line-height: 44px;
  color: #ffffff;
  cursor: pointer;
  z-index: 5;
}
.mobile-menu .navigation li.dropdown .dropdown-btn:after {
  content: "";
  position: absolute;
  left: 0px;
  top: 10px;
  width: 1px;
  height: 24px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .navigation li.dropdown .dropdown-btn.active i:before {
  content: "\f106";
}

.mobile-menu .navigation li > ul,
.mobile-menu .navigation li > ul > li > ul {
  display: none;
}

.mobile-menu .navigation li > ul > li {
  padding-left: 20px;
}

.contact-list-one {
  position: relative;
  padding: 30px 20px 20px;
}
.contact-list-one li {
  position: relative;
  margin-bottom: 20px;
  position: relative;
  padding-left: 54px;
  font-size: 14px;
  line-height: 24px;
  color: #ffffff;
}
.contact-list-one li:last-child {
  margin-right: 0;
}
.contact-list-one li a {
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-list-one li a:hover {
  color: var(--theme-color1);
}
.contact-list-one li .icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 34px;
  line-height: 50px;
  color: var(--theme-color1);
}
.contact-list-one li .title {
  display: block;
  font-size: 12px;
  color: #b2c1c0;
  font-weight: 400;
  text-transform: uppercase;
}
.contact-list-one li .text {
  color: var(--theme-color-light);
}

.mobile-menu .social-links {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background: var(--theme-color-dark);
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}
.mobile-menu .social-links li {
  position: relative;
  text-align: center;
  width: 100%;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}
.mobile-menu .social-links li a {
  position: relative;
  display: block;
  line-height: 50px;
  height: 50px;
  font-size: 14px;
  text-align: center;
  color: #ffffff;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.mobile-menu .social-links li a:hover {
  color: var(--theme-color1);
}

/***

====================================================================
  Hidden Sidebar style
====================================================================

***/
.toggle-hidden-bar {
  position: relative;
  height: 24px;
  min-width: 38px;
}
.toggle-hidden-bar .line {
  position: absolute;
  left: 0;
  top: 10px;
  height: 3px;
  width: 100%;
  background-color: #ffffff;
}
.toggle-hidden-bar .line:nth-child(1) {
  width: 24px;
  top: 0;
}
.toggle-hidden-bar .line:nth-child(3) {
  width: 24px;
  left: auto;
  right: 0;
  top: 21px;
}

.hidden-bar {
  position: fixed;
  right: 0;
  top: 0px;
  height: 100%;
  overflow-y: auto;
  z-index: 99999;
  max-width: 320px;
  width: 100%;
  opacity: 0;
  -webkit-transform: translateX(100%);
          transform: translateX(100%);
  visibility: hidden;
  background: #ffffff;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.hidden-bar::-webkit-scrollbar {
  width: 2px;
}
.hidden-bar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 2px #cecece;
  box-shadow: inset 0 0 2px #cecece;
  border-radius: 10px;
}
.hidden-bar::-webkit-scrollbar-thumb {
  background: #7a7a7a;
  border-radius: 10px;
}
.hidden-bar::-webkit-scrollbar-thumb:hover {
  background: #7a7a7a;
}
.hidden-bar .inner-box {
  position: relative;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  background-color: var(--theme-color-dark);
  height: 100%;
}
.hidden-bar .upper-box {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
  padding: 30px 30px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}
.hidden-bar .upper-box .close-btn {
  position: relative;
  top: -5px;
  text-align: center;
  font-size: 18px;
  line-height: 30px;
  color: var(--theme-color1);
  cursor: pointer;
  z-index: 10;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
.hidden-bar .upper-box .close-btn:hover {
  opacity: 0.5;
}
.hidden-bar .upper-box .nav-logo {
  position: relative;
  text-align: left;
  width: 100%;
}
.hidden-bar .upper-box .nav-logo img {
  max-height: 40px;
}
.hidden-bar .text-box {
  position: relative;
  padding: 25px 30px 25px;
}

.contact-list-two {
  position: relative;
  padding: 30px 30px 20px;
}
.contact-list-two li {
  position: relative;
  margin-bottom: 20px;
}
.contact-list-two li:last-child {
  margin-right: 0;
}
.contact-list-two li .contact-info-box {
  position: relative;
  padding-left: 54px;
  font-size: 14px;
  line-height: 24px;
  color: var(--theme-color1);
}
.contact-list-two li .contact-info-box a {
  color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-list-two li .contact-info-box .icon {
  position: absolute;
  left: 0;
  top: 0;
  font-size: 34px;
  line-height: 50px;
  color: var(--theme-color-light);
}
.contact-list-two li .contact-info-box .title {
  display: block;
  font-size: 12px;
  color: var(--theme-color-light);
  font-weight: 700;
  text-transform: uppercase;
}

.hidden-bar-back-drop {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.5s ease;
  transition: all 0.5s ease;
}

.active-hidden-bar .hidden-bar {
  -webkit-transform: translateX(0);
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

.active-hidden-bar .hidden-bar-back-drop {
  opacity: 1;
  visibility: visible;
}

.hidden-bar .social-links {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  background: var(--theme-color-dark);
  width: 100%;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: auto;
}
.hidden-bar .social-links li {
  position: relative;
  text-align: center;
  width: 100%;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}
.hidden-bar .social-links li a {
  position: relative;
  display: block;
  line-height: 50px;
  height: 50px;
  font-size: 14px;
  text-align: center;
  color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.hidden-bar .social-links li a:hover {
  color: var(--theme-color1);
}

/***

==================================================================
    Gallery Section
==================================================================

***/
.gallery-section {
  padding: 24px 0 0;
  position: relative;
}

.gallery-block .inner-box .image-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 24px;
}
.gallery-block .inner-box .image-box:hover .image:before {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.gallery-block .inner-box .image-box:hover .image a img {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.gallery-block .inner-box .image-box:hover .icon {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.gallery-block .inner-box .image-box .image {
  margin-bottom: 0;
  overflow: hidden;
  width: 100%;
}
.gallery-block .inner-box .image-box .image:before {
  background-color: #000;
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  -webkit-transform: scale(0);
          transform: scale(0);
  pointer-events: none;
  opacity: 20%;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.gallery-block .inner-box .image-box .image a img {
  width: 100%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.gallery-block .inner-box .image-box .icon {
  background-color: var(--theme-color1);
  border-radius: 50%;
  color: var(--theme-color-light);
  position: absolute;
  font-size: 38px;
  width: 84px;
  height: 84px;
  line-height: 84px;
  -webkit-transform: scale(0);
          transform: scale(0);
  text-align: center;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.gallery-block .inner-box .image-box .icon:hover {
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
}

/*** 

====================================================================
Section Title
====================================================================

***/
.sec-title {
  position: relative;
  margin-bottom: 50px;
}
.sec-title .image {
  margin-bottom: 15px;
}
.sec-title .title-stroke-text {
  position: relative;
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 1px rgba(var(--theme-color-dark-rgb), 0.1);
  font-size: 150px;
  font-weight: 700;
  line-height: 121.1px;
  font-family: var(--title-font);
  letter-spacing: 19.13px;
  margin-bottom: -100px;
  pointer-events: none;
  text-transform: uppercase;
}
@media (max-width: 991.98px) {
  .sec-title .title-stroke-text {
    font-size: 80px;
  }
}
@media (max-width: 767.98px) {
  .sec-title .title-stroke-text {
    font-size: 60px;
  }
}
@media (max-width: 575.98px) {
  .sec-title .title-stroke-text {
    font-size: 36px;
  }
}
.sec-title .title-stroke-text + .image {
  position: relative;
  z-index: 1;
}
.sec-title .title-stroke-text + .image + .sub-title {
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}
.sec-title .sub-title {
  position: relative;
  display: inline-block;
  font-size: var(--sec-title-subtitle-font-size);
  line-height: var(--sec-title-subtitle-line-height);
  font-weight: var(--sec-title-subtitle-font-weight);
  font-family: var(--sec-title-subtitle-font-family);
  color: var(--sec-title-subtitle-color);
  margin-top: 0;
}
.sec-title h1 {
  position: relative;
  font-size: var(--h1-font-size);
  font-weight: 700;
  line-height: 1.211em;
  margin-bottom: 0;
}
.sec-title h2 {
  position: relative;
  font-size: var(--sec-title-font-size);
  color: var(--sec-title-color);
  font-family: var(--sec-title-font-family);
  font-weight: var(--sec-title-font-weight);
  line-height: var(--sec-title-title-line-height);
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .sec-title h2 {
    font-size: 36px;
  }
}
.sec-title .text {
  font-weight: var(--body-font-weight);
  font-size: 16px;
  line-height: 29px;
}
.sec-title.light .sub-title,
.sec-title.light .text,
.sec-title.light h2,
.sec-title.light h1 {
  color: #fff;
}

/*** 

====================================================================
    Banner Section One
====================================================================

***/
.banner-section-one {
  background-color: var(--theme-color5);
  position: relative;
  overflow: hidden;
  padding-bottom: 49px;
}
@media (max-width: 1399.98px) {
  .banner-section-one {
    padding-bottom: 0;
  }
}
.banner-section-one:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  z-index: 8;
  pointer-events: none;
}
.banner-section-one .shape-image-curve {
  background-image: url(../images/main-slider/slide-shape-bottom2.png);
  position: absolute;
  width: 100%;
  height: 153px;
  left: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .banner-section-one .shape-image-curve {
    bottom: -10px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-one .shape-image-curve {
    display: none;
  }
}
.banner-section-one .shape-image-leaf {
  background-image: url(../images/main-slider/slide3-3.png);
  position: absolute;
  width: 141px;
  height: 240px;
  left: 0;
  top: 80px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .banner-section-one .shape-image-leaf {
    display: none;
  }
}
.banner-section-one .slide-item {
  position: relative;
  min-height: 847px;
}
@media (max-width: 1399.98px) {
  .banner-section-one .slide-item {
    min-height: 720px;
    padding: 30px 0 0;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-one .slide-item {
    min-height: 620px;
    padding: 30px 0 0;
  }
}
@media (max-width: 991.98px) {
  .banner-section-one .slide-item {
    padding: 0;
    min-height: auto;
  }
}
.banner-section-one .slide-item .bg-image {
  position: absolute;
  right: 0;
  bottom: 0;
  height: 847px;
  width: 947px;
  background-repeat: no-repeat;
  background-position: bottom right;
  background-size: cover;
  pointer-events: none;
}
.banner-section-one .content-box {
  position: relative;
  padding: 182px 15px 171px 38px;
}
@media (max-width: 1399.98px) {
  .banner-section-one .content-box {
    padding: 100px 15px 40px 38px;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-one .content-box {
    padding: 50px 15px 40px 38px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-one .content-box {
    padding: 90px 55px 100px;
    text-align: center;
  }
}
@media (max-width: 575.98px) {
  .banner-section-one .content-box {
    padding: 70px 15px 80px;
  }
}
.banner-section-one .content-box .image-shape {
  position: absolute;
  right: -93px;
  top: 44px;
  margin-bottom: 0;
}
.banner-section-one .content-box .title-stroke-text {
  -webkit-text-fill-color: transparent;
  -webkit-text-stroke: 1px rgba(var(--theme-color-dark-rgb), 0.1);
  font-size: 140px;
  font-weight: 700;
  line-height: 121.1px;
  font-family: var(--title-font);
  letter-spacing: 30.5px;
  margin-bottom: -100px;
  margin-left: -571px;
  pointer-events: none;
  text-transform: uppercase;
  position: relative;
  right: -283px;
  top: -11px;
}
@media (max-width: 1399.98px) {
  .banner-section-one .content-box .title-stroke-text {
    font-size: 80px;
    top: -30px;
    right: -400px;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-one .content-box .title-stroke-text {
    font-size: 60px;
    letter-spacing: 15px;
    right: auto;
    margin-left: -40px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-one .content-box .title-stroke-text {
    margin-left: 0;
  }
}
@media (max-width: 767.98px) {
  .banner-section-one .content-box .title-stroke-text {
    font-size: 42px;
  }
}
@media (max-width: 575.98px) {
  .banner-section-one .content-box .title-stroke-text {
    font-size: 26px;
  }
}
.banner-section-one .content-box .sub-title {
  position: relative;
  display: inline-block;
  font-size: 20px;
  font-weight: 400;
  font-family: var(--style-font);
  color: var(--headings-color);
  margin-top: 0;
  margin-bottom: 1px;
}
.banner-section-one .content-box .title {
  color: var(--headings-color);
  font-size: 80px;
  font-weight: 700;
  line-height: 97px;
  margin-bottom: 20px;
  margin-right: -70px;
  text-transform: uppercase;
}
@media (max-width: 1399.98px) {
  .banner-section-one .content-box .title {
    font-size: 60px;
    line-height: 1.3;
  }
}
@media (max-width: 991.98px) {
  .banner-section-one .content-box .title {
    margin-right: 0;
  }
}
@media (max-width: 767.98px) {
  .banner-section-one .content-box .title {
    font-size: 40px;
    margin-bottom: 30px;
    margin-right: 0;
  }
}
@media (max-width: 575.98px) {
  .banner-section-one .content-box .title {
    font-size: 25px;
    margin-bottom: 24px;
  }
}
.banner-section-one .content-box .text {
  color: var(--text-color);
  margin-bottom: 44px;
}
@media (max-width: 1399.98px) {
  .banner-section-one .content-box .text br {
    display: none;
  }
}
.banner-section-one .content-box .btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 991.98px) {
  .banner-section-one .content-box .btn-box {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
@media (max-width: 575.98px) {
  .banner-section-one .content-box .btn-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.banner-section-one .content-box .btn-box .theme-btn {
  font-size: 14px;
  margin-right: 20px;
  padding: 22.5px 40px;
}
@media (max-width: 575.98px) {
  .banner-section-one .content-box .btn-box .theme-btn {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-one .image-column {
    display: none;
  }
}
.banner-section-one .image-column .image-box {
  position: relative;
  padding: 96px 0 0;
  margin-left: -191px;
}
@media (max-width: 1399.98px) {
  .banner-section-one .image-column .image-box {
    margin-left: 0;
  }
}
.banner-section-one .image-column .image-box .image {
  margin-bottom: 0;
  position: relative;
}
.banner-section-one .image-column .image-box .image .round-shape {
  background-color: var(--theme-color-light);
  position: absolute;
  width: 741px;
  height: 741px;
  border-radius: 50%;
  top: -22px;
  right: 35px;
  -webkit-animation: round-shape-anim 1s linear infinite;
          animation: round-shape-anim 1s linear infinite;
  -webkit-filter: drop-shadow(-1px 3px 65px rgba(237, 237, 237, 0.35));
  -moz-filter: drop-shadow(-1px 3px 65px rgba(237, 237, 237, 0.35));
  filter: drop-shadow(-1px 3px 65px rgba(237, 237, 237, 0.35));
}
@media (max-width: 1399.98px) {
  .banner-section-one .image-column .image-box .image .round-shape {
    width: 500px;
    height: 500px;
    right: 75px;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-one .image-column .image-box .image .round-shape {
    width: 400px;
    height: 400px;
    right: 35px;
  }
}
.banner-section-one .image-column .image-box .image img {
  width: auto;
}
.banner-section-one .image-box .image .zindex {
  position: relative;
  z-index: 1;
}

/* Animate 1 */
.owl-carousel .animate-7,
.owl-carousel .animate-6,
.owl-carousel .animate-5,
.owl-carousel .animate-4,
.owl-carousel .animate-3,
.owl-carousel .animate-2,
.owl-carousel .animate-1 {
  opacity: 0;
  -webkit-transform: translateY(100px);
          transform: translateY(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .animate-x {
  opacity: 0;
  -webkit-transform: translateX(100px);
          transform: translateX(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .fadeInUpBig {
  opacity: 0;
  -webkit-transform: scale(0.9) translateY(100px);
          transform: scale(0.9) translateY(100px);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.owl-carousel .active .animate-7,
.owl-carousel .active .animate-6,
.owl-carousel .active .animate-5,
.owl-carousel .active .animate-4,
.owl-carousel .active .animate-3,
.owl-carousel .active .animate-2,
.owl-carousel .active .animate-1 {
  opacity: 1;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.owl-carousel .active .animate-2 {
  -webkit-transition-delay: 300ms;
          transition-delay: 300ms;
}
.owl-carousel .active .animate-3 {
  -webkit-transition-delay: 600ms;
          transition-delay: 600ms;
}
.owl-carousel .active .animate-4 {
  -webkit-transition-delay: 900ms;
          transition-delay: 900ms;
}
.owl-carousel .active .animate-5 {
  -webkit-transition-delay: 1200ms;
          transition-delay: 1200ms;
}
.owl-carousel .active .animate-6 {
  -webkit-transition-delay: 1500ms;
          transition-delay: 1500ms;
}
.owl-carousel .active .animate-7 {
  -webkit-transition-delay: 1800ms;
          transition-delay: 1800ms;
}
.owl-carousel .active .fadeInUpBig {
  opacity: 1;
  -webkit-transform: scale(1) translateY(0);
          transform: scale(1) translateY(0);
  -webkit-transition-delay: 500ms;
          transition-delay: 500ms;
}

.banner-carousel .owl-nav {
  position: absolute;
  left: 0;
  right: 0;
  top: 48%;
  max-width: 1714px;
  margin: -25px auto 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 991.98px) {
  .banner-carousel .owl-nav {
    top: 44%;
  }
}
@media (max-width: 767.98px) {
  .banner-carousel .owl-nav {
    display: none;
  }
}
.banner-carousel .owl-nav .owl-next,
.banner-carousel .owl-nav .owl-prev {
  background-color: #fff;
  color: var(--theme-color1);
  display: block;
  height: 55px;
  width: 108px;
  font-size: 20px;
  line-height: 55px;
  font-weight: 700;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
@media (max-width: 991.98px) {
  .banner-carousel .owl-nav .owl-next,
  .banner-carousel .owl-nav .owl-prev {
    height: 46px;
    width: 98px;
    font-size: 16px;
    line-height: 46px;
  }
}
.banner-carousel .owl-nav .owl-next:before,
.banner-carousel .owl-nav .owl-prev:before {
  position: absolute;
  font-size: 16px;
  text-transform: uppercase;
}
.banner-carousel .owl-nav .owl-next:hover,
.banner-carousel .owl-nav .owl-prev:hover {
  color: var(--theme-color2);
}
.banner-carousel .owl-nav .owl-prev {
  padding-right: 48px;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  margin-bottom: 52px;
  border-radius: 6px 0 0 6px;
}
.banner-carousel .owl-nav .owl-prev:after {
  background-color: #eeeeee;
  position: absolute;
  content: "";
  width: 25px;
  height: 1px;
  right: -11px;
  top: 26px;
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.banner-carousel .owl-nav .owl-prev:before {
  content: "Prev";
  padding-left: 22px;
}
.banner-carousel .owl-nav .owl-next {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
  padding-left: 50px;
  border-radius: 0 6px 6px 0;
}
.banner-carousel .owl-nav .owl-next:before {
  content: "Next";
  left: 23px;
}

/*** 

====================================================================
    Banner Section Two
====================================================================

***/
.banner-section-two {
  background-color: var(--theme-color5);
  position: relative;
  overflow: hidden;
  padding-bottom: 60px;
}
@media (max-width: 1399.98px) {
  .banner-section-two {
    padding-bottom: 0;
  }
}
.banner-section-two:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  z-index: 8;
  pointer-events: none;
}
.banner-section-two .shape-image-curve {
  background-image: url(../images/main-slider/slide-shape-bottom3.png);
  position: absolute;
  width: 100%;
  height: 153px;
  left: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .banner-section-two .shape-image-curve {
    display: none;
  }
}
.banner-section-two .shape-image-leaf {
  background-image: url(../images/main-slider/slide3-3.png);
  position: absolute;
  width: 141px;
  height: 240px;
  right: 0;
  top: 80px;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .banner-section-two .shape-image-leaf {
    display: none;
  }
}
.banner-section-two .slide-item {
  position: relative;
  min-height: 836px;
}
@media (max-width: 1399.98px) {
  .banner-section-two .slide-item {
    min-height: 720px;
    padding: 30px 0 0;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-two .slide-item {
    min-height: 620px;
    padding: 30px 0 0;
  }
}
@media (max-width: 991.98px) {
  .banner-section-two .slide-item {
    padding: 0;
    min-height: auto;
  }
}
.banner-section-two .slide-item .bg-image {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 847px;
  width: 947px;
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: cover;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.banner-section-two .content-box {
  position: relative;
  padding: 204px 15px 171px 12px;
}
@media (max-width: 1399.98px) {
  .banner-section-two .content-box {
    padding: 100px 15px 40px 38px;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-two .content-box {
    padding: 70px 15px 40px 38px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-two .content-box {
    padding: 90px 55px 100px;
    text-align: center;
  }
}
@media (max-width: 575.98px) {
  .banner-section-two .content-box {
    padding: 70px 15px 80px;
  }
}
.banner-section-two .content-box .sub-title {
  position: relative;
  display: inline-block;
  font-size: 20px;
  font-weight: 400;
  font-family: var(--style-font);
  color: var(--headings-color);
  margin-top: 0;
  margin-bottom: 1px;
}
.banner-section-two .content-box .title {
  color: var(--headings-color);
  font-size: 80px;
  font-weight: 700;
  line-height: 97px;
  margin-bottom: 20px;
  text-transform: uppercase;
}
@media (max-width: 1399.98px) {
  .banner-section-two .content-box .title {
    font-size: 60px;
    line-height: 1.3;
  }
}
@media (max-width: 991.98px) {
  .banner-section-two .content-box .title {
    margin-right: 0;
  }
}
@media (max-width: 767.98px) {
  .banner-section-two .content-box .title {
    font-size: 40px;
    margin-bottom: 30px;
    margin-right: 0;
  }
}
@media (max-width: 575.98px) {
  .banner-section-two .content-box .title {
    font-size: 25px;
    margin-bottom: 24px;
  }
}
.banner-section-two .content-box .text {
  color: var(--text-color);
  margin-bottom: 44px;
  margin-right: 15px;
}
@media (max-width: 1399.98px) {
  .banner-section-two .content-box .text br {
    display: none;
  }
}
.banner-section-two .content-box .btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 991.98px) {
  .banner-section-two .content-box .btn-box {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
@media (max-width: 575.98px) {
  .banner-section-two .content-box .btn-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.banner-section-two .content-box .btn-box .theme-btn {
  font-size: 14px;
  margin-right: 20px;
  padding: 22.5px 40px;
}
@media (max-width: 575.98px) {
  .banner-section-two .content-box .btn-box .theme-btn {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-two .image-column {
    display: none;
  }
}
.banner-section-two .image-column .image-box {
  position: relative;
  padding: 106px 0 0;
  margin-right: -224px;
}
@media (max-width: 1399.98px) {
  .banner-section-two .image-column .image-box {
    margin-right: 0;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-two .image-column .image-box {
    padding: 90px 0 0;
  }
}
.banner-section-two .image-column .image-box .image {
  margin-bottom: 0;
  position: relative;
}
.banner-section-two .image-column .image-box .image .round-shape {
  background-color: var(--theme-color-light);
  position: absolute;
  width: 720px;
  height: 720px;
  border-radius: 50%;
  top: -22px;
  right: 35px;
  -webkit-animation: round-shape-anim 1s linear infinite;
          animation: round-shape-anim 1s linear infinite;
  -webkit-filter: drop-shadow(-1px 3px 65px rgba(237, 237, 237, 0.35));
  -moz-filter: drop-shadow(-1px 3px 65px rgba(237, 237, 237, 0.35));
  filter: drop-shadow(-1px 3px 65px rgba(237, 237, 237, 0.35));
}
@media (max-width: 1399.98px) {
  .banner-section-two .image-column .image-box .image .round-shape {
    width: 500px;
    height: 500px;
    right: -25px;
  }
}
@media (max-width: 1199.98px) {
  .banner-section-two .image-column .image-box .image .round-shape {
    width: 400px;
    height: 400px;
    right: -5px;
  }
}
.banner-section-two .image-column .image-box .image img {
  width: auto;
  position: relative;
  right: -95px;
  top: 35px;
}
@media (max-width: 1199.98px) {
  .banner-section-two .image-column .image-box .image img {
    top: 0;
    right: -60px;
  }
}

/*** 

====================================================================
    Banner Section Three
====================================================================

***/
.banner-section-three {
  background-color: #f4ecdf;
  position: relative;
  overflow: hidden;
}
.banner-section-three:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  z-index: 8;
  pointer-events: none;
}
.banner-section-three .bg-image {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 836px;
  width: 935px;
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: cover;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
.banner-section-three .shape-image-curve {
  background-image: url(../images/main-slider/slide-shape-bottom4.png);
  position: absolute;
  width: 100%;
  height: 69px;
  left: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .banner-section-three .shape-image-curve {
    display: none;
  }
}
.banner-section-three .shape-image-leaf {
  background-image: url(../images/main-slider/slide8-3.png);
  position: absolute;
  width: 223px;
  height: 367px;
  right: 0;
  top: -150px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .banner-section-three .shape-image-leaf {
    display: none;
  }
}
.banner-section-three .shape-image8-1 {
  background-image: url(../images/main-slider/slide8-4.png);
  position: absolute;
  width: 199px;
  height: 328px;
  right: 0;
  bottom: 144px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .banner-section-three .shape-image8-1 {
    display: none;
  }
}
.banner-section-three .slide-item {
  position: relative;
  min-height: 826px;
}
@media (max-width: 1199.98px) {
  .banner-section-three .slide-item {
    min-height: 685px;
    padding-bottom: 90px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .slide-item {
    padding: 0 100px 90px;
  }
}
@media (max-width: 767.98px) {
  .banner-section-three .slide-item {
    min-height: 540px;
  }
}
@media (max-width: 575.98px) {
  .banner-section-three .slide-item {
    min-height: 460px;
    padding: 0 15px 90px;
  }
}
.banner-section-three .slide-item .float-text {
  color: rgba(var(--theme-color-light-rgb), 0.7);
  font-size: 165px;
  font-weight: 700;
  font-family: var(--title-font);
  line-height: 1;
  letter-spacing: 19.8px;
  text-transform: uppercase;
  position: absolute;
  bottom: 60px;
  z-index: 4;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .banner-section-three .slide-item .float-text {
    font-size: 148px;
    left: 50%;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}
@media (max-width: 1199.98px) {
  .banner-section-three .slide-item .float-text {
    font-size: 130px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .slide-item .float-text {
    display: none;
  }
}
.banner-section-three .slide-item .shape-image8-2 {
  background-image: url(../images/main-slider/slide8-5.png);
  position: absolute;
  width: 259px;
  height: 252px;
  left: calc(50% - 335px);
  bottom: 21px;
  z-index: 4;
  pointer-events: none;
  -webkit-transform: none;
          transform: none;
}
@media (max-width: 1199.98px) {
  .banner-section-three .slide-item .shape-image8-2 {
    bottom: 2px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .slide-item .shape-image8-2 {
    display: none;
  }
}
.banner-section-three .content-box {
  position: relative;
  padding: 206px 15px 171px 12px;
}
@media (max-width: 991.98px) {
  .banner-section-three .content-box {
    padding: 80px 0 140px;
    text-align: center;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .content-box {
    padding: 90px 0 0;
  }
  .banner-section-three .content-box:last-child {
    padding-top: 10px;
  }
}
.banner-section-three .content-box .inner-content {
  padding-top: 166px;
}
@media (max-width: 1199.98px) {
  .banner-section-three .content-box .inner-content {
    padding-top: 118px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .content-box .inner-content {
    padding-top: 38px;
  }
}
.banner-section-three .content-box .sub-title {
  position: relative;
  display: inline-block;
  font-size: 20px;
  font-weight: 400;
  font-family: var(--style-font);
  color: var(--headings-color);
  margin-top: 0;
  margin-bottom: 1px;
}
.banner-section-three .content-box .title {
  color: var(--headings-color);
  font-size: 80px;
  font-weight: 700;
  line-height: 97px;
  margin-bottom: 20px;
  text-transform: uppercase;
}
@media (max-width: 767.98px) {
  .banner-section-three .content-box .title {
    font-size: 40px;
    line-height: 60px;
    margin-bottom: 30px;
    margin-right: 0;
  }
}
@media (max-width: 575.98px) {
  .banner-section-three .content-box .title {
    font-size: 25px;
    line-height: 34px;
    margin-bottom: 24px;
  }
}
.banner-section-three .content-box .text {
  color: var(--text-color);
  margin-bottom: 30px;
  margin-right: 15px;
  letter-spacing: 0;
}
@media (max-width: 1399.98px) {
  .banner-section-three .content-box .text br {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .content-box .text {
    margin-right: 0;
  }
}
.banner-section-three .content-box .btn-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 991.98px) {
  .banner-section-three .content-box .btn-box {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
@media (max-width: 575.98px) {
  .banner-section-three .content-box .btn-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.banner-section-three .content-box .btn-box .theme-btn {
  font-size: 14px;
  margin-right: 20px;
  padding: 22.5px 40px;
}
@media (max-width: 575.98px) {
  .banner-section-three .content-box .btn-box .theme-btn {
    margin-right: 0;
    margin-bottom: 20px;
  }
}
.banner-section-three .image-column .image-box {
  position: relative;
  padding: 108px 0 0;
  z-index: 3;
}
@media (max-width: 1199.98px) {
  .banner-section-three .image-column .image-box {
    margin-right: -85px;
    left: -22px;
    padding: 88px 0 0;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .image-column .image-box {
    max-width: 400px;
    width: 100%;
    left: 0;
    margin: 0 auto;
    padding: 0;
  }
}
.banner-section-three .image-column .image-box .image {
  margin-bottom: 0;
  margin-right: 53px;
  margin-left: -15px;
  position: relative;
  border-radius: 240px;
  border: 1px solid var(--theme-color1);
  padding: 30px;
}
@media (max-width: 1199.98px) {
  .banner-section-three .image-column .image-box .image {
    padding: 12px;
  }
}
@media (max-width: 991.98px) {
  .banner-section-three .image-column .image-box .image {
    margin-left: 0;
    margin-right: 0;
  }
}
.banner-section-three .image-column .image-box .image img {
  border-radius: 214px;
  position: relative;
}
.banner-section-three .image-column .image-box .text-rotate {
  border-radius: 50%;
  position: absolute;
  right: -10px;
  top: 148px;
  height: 180px;
  width: 180px;
  z-index: 2;
  -webkit-transform: none;
          transform: none;
}
@media (max-width: 991.98px) {
  .banner-section-three .image-column .image-box .text-rotate {
    top: auto;
    bottom: 50px;
    left: 50%;
    right: auto;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
  }
}
.banner-section-three .image-column .image-box .text-rotate .icon-text-2 {
  background-image: url(../images/main-slider/slide8-6.png);
  z-index: 2;
  width: 180px;
  height: 180px;
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-animation: fa-spin 40s infinite linear;
          animation: fa-spin 40s infinite linear;
}
.banner-section-three .image-column .image-box .text-rotate .play-btn-two {
  top: 50%;
  left: 50%;
  text-align: center;
  z-index: 2;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 127px;
  height: 127px;
  display: inline-block;
}
.banner-section-three .image-column .image-box .text-rotate .play-btn-two:hover .icon {
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
}
.banner-section-three .image-column .image-box .text-rotate .play-btn-two .icon {
  background-color: var(--theme-color-light);
  -webkit-box-shadow: 0px 4px 30px 0px rgba(var(--theme-color-dark-rgb), 0.05);
          box-shadow: 0px 4px 30px 0px rgba(var(--theme-color-dark-rgb), 0.05);
  color: var(--theme-color1);
  font-size: 30px;
  -webkit-transform: rotate(-35deg);
          transform: rotate(-35deg);
  left: 0;
  top: 0;
  display: block;
  width: 127px;
  height: 127px;
  line-height: 127px;
}

/* Keyframe Animations */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Animation Styles */
.banner-section-three .float-text,
.banner-section-three .shape-image8-2,
.banner-section-three .content-box .text,
.banner-section-three .btn-box,
.banner-section-three .text-rotate,
.banner-section-three .image-box .image {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Specific Delays */
.banner-section-three .float-text {
  animation-delay: 0.2s;
}

.banner-section-three .shape-image8-2 {
  animation-delay: 0.4s;
}

.banner-section-three .content-box .text {
  animation-delay: 0.6s;
}

.banner-section-three .btn-box {
  animation-delay: 0.8s;
}

.banner-section-three .text-rotate {
  animation-delay: 1s;
}

.banner-section-three .image-box .image {
  animation: fadeInRight 1s ease-out forwards;
  animation-delay: 1.2s;
}


/*** 
====================================================================
    Banner Product Section
====================================================================
***/
.banners-section {
  position: relative;
  padding: 120px 0px 90px;
}
@media (min-width: 1400px) {
  .banners-section .container {
    max-width: 1320px;
  }
}

.banner-box-one {
  position: relative;
  margin-bottom: 30px;
  overflow: hidden;
  height: 100%;
}
.banner-box-one .inner-box {
  position: relative;
  background: #F4F1EC;
  padding: 30px 40px 48px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 310px;
}
@media (max-width: 479.98px) {
  .banner-box-one .inner-box {
    padding: 30px 15px 48px;
  }
}
.banner-box-one .inner-box.style-two {
  background-color: #F4ECDF;
}
.banner-box-one .inner-box.style-three {
  background-color: #FAEFEB;
}
.banner-box-one .inner-box:hover .image img {
  -webkit-transform: scale(1.05);
          transform: scale(1.05);
}
.banner-box-one .image {
  position: absolute;
  right: 32px;
  bottom: 40px;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .banner-box-one .image {
    bottom: 20px;
    right: 15px;
  }
}
.banner-box-one .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.banner-box-one .content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.banner-box-one .content .title {
  line-height: 1.2em;
  font-weight: 500;
  margin-bottom: 20px;
}
.banner-box-one .content .title span {
  color: var(--theme-color1);
}
.banner-box-one .content .price {
  color: var(--theme-color1);
  font-size: 28px;
  display: block;
}
.banner-box-one .content .price .price-style {
  color: #B9B8B8;
  font-size: 18px;
  font-weight: 500;
  display: block;
  position: relative;
  text-decoration: line-through;
  margin-bottom: 9px;
}

/*** 
====================================================================
    Banner Product Section Two
====================================================================
***/
.banners-section-two {
  background-color: var(--theme-color5);
  position: relative;
  padding: 30px 0px 90px;
}
.banners-section-two.pull-down {
  padding: 100px 0 0;
}
@media (max-width: 1199.98px) {
  .banners-section-two.pull-down {
    padding: 100px 0;
  }
}
.banners-section-two.pull-down:before {
  background-color: var(--theme-color-light);
  content: "";
  position: absolute;
  height: 174px;
  width: 100%;
  bottom: 0;
  left: 0;
}
@media (max-width: 1199.98px) {
  .banners-section-two.pull-down:before {
    display: none;
  }
}

.banner-box-two {
  position: relative;
  margin-bottom: 30px;
  overflow: hidden;
  height: 100%;
}
.banner-box-two .inner-box {
  position: relative;
  background: #F4F1EC;
  padding: 70px 60px 72px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  min-height: 310px;
}
@media (max-width: 479.98px) {
  .banner-box-two .inner-box {
    padding: 30px 15px 48px;
  }
}
.banner-box-two .inner-box.style-two {
  background-color: #F4ECDF;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
.banner-box-two .inner-box.style-two .content {
  text-align: right;
}
.banner-box-two .inner-box.style-two .content .title,
.banner-box-two .inner-box.style-two .content .text {
  color: var(--theme-color-light);
}
.banner-box-two .image {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.banner-box-two .image img {
  -o-object-fit: cover;
     object-fit: cover;
  width: 100%;
  height: 100%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.banner-box-two .content {
  max-width: 270px;
}
.banner-box-two .content .title {
  line-height: 1.2em;
  font-weight: 500;
  margin-bottom: 17px;
}
@media (max-width: 575.98px) {
  .banner-box-two .content .title {
    font-size: 30px;
  }
}
.banner-box-two .content .title span {
  color: var(--theme-color1);
}
.banner-box-two .content .text {
  position: relative;
  margin-bottom: 24px;
}

/***

====================================================================
    Package Section
====================================================================

***/
.packages-section {
  padding: 93px 0 100px;
  position: relative;
}
@media (max-width: 767.98px) {
  .packages-section {
    padding: 70px 0 100px;
  }
}
.packages-section .sec-title {
  margin-bottom: 86px;
}
@media (max-width: 991.98px) {
  .packages-section .sec-title {
    margin-bottom: 50px;
  }
}
.packages-section .package-pattrn2 {
  background-image: url(../images/resource/package-pattrn2.png);
  position: absolute;
  width: 369px;
  height: 530px;
  right: 0;
  top: 0;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .packages-section .package-pattrn2 {
    display: none;
  }
}
.packages-section .auto-container {
  max-width: 1780px;
}
.packages-section .default-dots .owl-dots {
  left: 0;
  position: absolute;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  bottom: -47px;
}
.packages-section .default-dots .owl-dots .owl-dot {
  height: 12px;
  width: 12px;
  margin: 0 8.5px;
  background-color: transparent;
  border: 1px solid #707070;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.packages-section .default-dots .owl-dots .owl-dot.active {
  width: 15px;
  height: 15px;
  background-color: var(--theme-color1);
  border-color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.package-block .inner-box {
  position: relative;
  margin-bottom: 26px;
}
.package-block .inner-box:hover .image-box .bg-image-two {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.package-block .inner-box .image-box {
  margin-bottom: 56px;
  position: relative;
}
.package-block .inner-box .image-box .image img {
  border-radius: 91% 61% 68% 84%/73% 62% 89% 79%;
}
.package-block .inner-box .image-box .bg-image {
  bottom: auto;
  left: 0;
  top: 24px;
  right: auto;
  width: 100%;
  pointer-events: none;
  z-index: 2;
}
.package-block .inner-box .image-box .bg-image-two {
  bottom: -13px;
  left: 113px;
  top: auto;
  right: auto;
  width: 161px;
  height: 273px;
  -webkit-transform: scale(0);
          transform: scale(0);
  pointer-events: none;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.package-block .content-box .name {
  color: var(--theme-color-dark);
  font-weight: 500;
  margin-bottom: 15px;
}
.package-block .content-box .name:hover {
  color: var(--theme-color1);
}
.package-block .content-box .price {
  color: var(--theme-color1);
  font-size: 30px;
  font-weight: 500;
  font-family: var(--title-font);
}

/***

====================================================================
    Package Section Two
====================================================================

***/
.packages-section-two {
  padding: 120px 0 92px;
  position: relative;
}
@media (max-width: 767.98px) {
  .packages-section-two {
    padding: 70px 0 100px;
  }
}
.packages-section-two .shape-2 {
  background-image: url(../images/icons/shape-2.png);
  position: absolute;
  width: 207px;
  height: 204px;
  bottom: -35px;
  left: 104px;
  z-index: 1;
  pointer-events: none;
  -webkit-animation: fa-spin 40s infinite linear;
          animation: fa-spin 40s infinite linear;
}
@media (max-width: 1399.98px) {
  .packages-section-two .shape-2 {
    display: none;
  }
}
.packages-section-two .info-box {
  padding-top: 48px;
  padding-bottom: 40px;
}
.packages-section-two .info-box .text {
  color: var(--headings-color);
  font-size: 64px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-bottom: 20px;
  display: inline-block;
  line-height: 1;
  margin-bottom: 34px;
  position: relative;
}
.packages-section-two .info-box .text:after {
  background-color: var(--theme-color-dark);
  content: "";
  position: absolute;
  left: calc(100% + 10px);
  height: 2px;
  width: 128px;
  top: 41px;
}
.packages-section-two .info-box .title {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 29px;
}

.package-block-two .inner-box {
  position: relative;
  margin-bottom: 26px;
}
.package-block-two .inner-box:hover .image-box .bg-image-two {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.package-block-two .inner-box:hover .content-box:before {
  height: calc(100% + 1px);
}
.package-block-two .inner-box:hover .content-box .name,
.package-block-two .inner-box:hover .content-box .price {
  color: var(--theme-color-light);
}
.package-block-two .inner-box .image-box {
  margin-bottom: 0;
  position: relative;
}
.package-block-two .inner-box .image-box .image img {
  border-radius: 0;
  width: 100%;
}
.package-block-two .content-box {
  border: 1px solid rgba(var(--theme-color-dark-rgb), 0.1);
  padding: 27px 0 35px;
  position: relative;
  z-index: 1;
}
.package-block-two .content-box:before {
  background-color: var(--theme-color1);
  content: "";
  position: absolute;
  width: 100%;
  height: 0;
  top: -1px;
  left: 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  z-index: -1;
}
.package-block-two .content-box .name {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
}
.package-block-two .content-box .name:hover {
  color: var(--theme-color1);
}
.package-block-two .content-box .price {
  color: var(--theme-color1);
  font-size: 64px;
  font-weight: 600;
  font-family: var(--title-font);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/***

====================================================================
    Package Section Three
====================================================================

***/
.packages-section-three {
  padding: 93px 0 100px;
  position: relative;
}
@media (max-width: 767.98px) {
  .packages-section-three {
    padding: 70px 0 100px;
  }
}
.packages-section-three .outer-box {
  padding: 0 30px;
}
@media (min-width: 1400px) {
  .packages-section-three .outer-box {
    padding: 0 101px;
  }
}
.packages-section-three.home7-style {
  background-color: #F4ECDF;
}

.package-block-three .inner-box {
  background-color: #F4ECDF;
  position: relative;
  margin-bottom: 26px;
  text-align: center;
}
.package-block-three .inner-box.style-two {
  background-color: #F2EFEA;
}
.package-block-three .inner-box:hover .image-box .bg-image-two {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.package-block-three .inner-box:hover .content-box .name {
  color: var(--theme-color-dark);
}
.package-block-three .inner-box:after {
  border: 1px dashed var(--theme-color1);
  content: "";
  position: absolute;
  top: 10px;
  left: 9px;
  right: 9px;
  bottom: 10px;
  z-index: 1;
  pointer-events: none;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.package-block-three .inner-box .image-box {
  margin-bottom: 0;
  position: relative;
}
.package-block-three .inner-box .image-box .image img {
  border-radius: 0;
}
.package-block-three .content-box {
  padding: 70px 50px;
  position: relative;
  z-index: 1;
}
@media (max-width: 1399.98px) {
  .package-block-three .content-box {
    padding: 70px 25px;
  }
}
.package-block-three .content-box:before {
  background-image: url("../images/icons/icon-flower shape.png");
  content: "";
  position: absolute;
  width: 120px;
  height: 120px;
  left: calc(50% - 48px);
  top: 32px;
  z-index: -1;
  pointer-events: none;
}
.package-block-three .content-box .name {
  color: var(--theme-color1);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  position: relative;
}
.package-block-three .content-box .name:hover {
  color: var(--theme-color1);
}
.package-block-three .content-box .price {
  color: var(--headings-color);
  display: block;
  font-size: 64px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-bottom: 40px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.package-block-three .content-box .text {
  margin-bottom: 30px;
}
.package-block-three.home7-style .inner-box {
  background-color: var(--theme-color-light);
}

/*** 
====================================================================
  Pricing Section
====================================================================
***/
.pricing-section {
  padding: 116px 0 128px;
  position: relative;
}
@media (max-width: 767.98px) {
  .pricing-section {
    padding: 90px 0;
  }
}
.pricing-section .leaf1 {
  background-image: url(../images/resource/leaf1.png);
  position: absolute;
  width: 308px;
  height: 176px;
  right: 0;
  bottom: 40px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .pricing-section .leaf1 {
    display: none;
  }
}
.pricing-section .leaf2 {
  background-image: url(../images/resource/leaf2.png);
  position: absolute;
  width: 368px;
  height: 425px;
  left: 0;
  top: -24px;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .pricing-section .leaf2 {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .pricing-section .content-column {
    margin-bottom: 70px;
  }
}
.pricing-section .content-column:last-child {
  margin-bottom: 0;
}
.pricing-section .pricing-block {
  margin-bottom: 88px;
}
.pricing-section .pricing-block:last-child {
  margin-bottom: 0;
}
.pricing-section .pricing-block .inner-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px dashed rgba(112, 112, 112, 0.25);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 24px;
}
.pricing-section .pricing-block .inner-box:hover .image-box .image a img {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.pricing-section .pricing-block .inner-box .image-box {
  margin-right: 21px;
}
.pricing-section .pricing-block .inner-box .image-box .image {
  width: 76px;
  height: 74px;
  overflow: hidden;
  border-radius: 11px;
}
.pricing-section .pricing-block .inner-box .image-box .image a img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-section .pricing-block .inner-box .content-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}
.pricing-section .pricing-block .inner-box .content-box .inner .title {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 0;
}
.pricing-section .pricing-block .inner-box .content-box .inner .title a:hover {
  color: var(--theme-color1);
}
.pricing-section .pricing-block .inner-box .content-box .inner .text {
  color: var(--theme-color1);
  font-size: 22px;
  font-weight: 600;
  font-style: italic;
  font-family: var(--title-font);
}
.pricing-section .pricing-block .inner-box .content-box .price {
  color: var(--theme-color1);
  font-size: 40px;
  font-weight: 700;
  font-family: var(--title-font);
}
@media (max-width: 991.98px) {
  .pricing-section .image-column {
    display: none;
  }
}
.pricing-section .image-column .inner-box {
  position: relative;
}
@media (max-width: 991.98px) {
  .pricing-section .image-column .inner-box {
    text-align: center;
    margin: 90px 0;
  }
}
.pricing-section .image-column .inner-box:before {
  content: "";
  background: #F9F6F1;
  border-radius: 208px;
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: -22px;
}
@media (max-width: 991.98px) {
  .pricing-section .image-column .inner-box:before {
    display: none;
  }
}
.pricing-section .image-column .inner-box .bg-image {
  width: 151px;
  height: 145px;
  top: -30px;
  right: 0;
  left: auto;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .pricing-section .image-column .inner-box .bg-image {
    left: 50%;
  }
}
.pricing-section .image-column .inner-box .image a img {
  border-radius: 208px;
}

/*** 
====================================================================
  Pricing Section Two
====================================================================
***/
.pricing-section-two {
  background-color: #f9f6f1;
  padding: 120px 0 70px;
  position: relative;
}
@media (max-width: 767.98px) {
  .pricing-section-two {
    padding: 90px 0 40px;
  }
}
@media (min-width: 992px) {
  .pricing-section-two .content-column {
    padding-right: 40px;
  }
}
@media (min-width: 992px) {
  .pricing-section-two .content-column + .content-column {
    padding-left: 40px;
    padding-right: 0;
  }
}
.pricing-section-two .content-column:last-child {
  margin-bottom: 0;
}

.pricing-block-two {
  margin-bottom: 40px;
}
.pricing-block-two .inner-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
.pricing-block-two .inner-box .content-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  overflow: hidden;
  width: 100%;
}
.pricing-block-two .inner-box .content-box .inner {
  position: relative;
}
.pricing-block-two .inner-box .content-box .inner .title {
  display: inline-block;
  font-weight: 400;
  margin-bottom: 0;
  position: relative;
  padding-right: 15px;
}
.pricing-block-two .inner-box .content-box .inner .title:after {
  content: "";
  border-bottom: 1px dashed rgba(var(--headings-color-rgb), 0.25);
  width: 100%;
  min-width: 500px;
  left: 100%;
  top: 20px;
  position: absolute;
}
.pricing-block-two .inner-box .content-box .inner .title a:hover {
  color: var(--theme-color1);
}
.pricing-block-two .inner-box .content-box .inner .text {
  color: var(--theme-color1);
  display: block;
  font-weight: 400;
  font-style: italic;
  font-family: var(--title-font);
}
.pricing-block-two .inner-box .content-box .price {
  background-color: #f9f6f1;
  color: var(--theme-color1);
  font-size: 36px;
  font-weight: 400;
  font-family: var(--title-font);
  position: relative;
  padding-left: 12px;
}

/*** 
====================================================================
  Pricing Section Three
====================================================================
***/
.pricing-section-three.pull-up .outer-box {
  border-radius: 10px;
  position: relative;
  margin: -170px 60px 0;
  z-index: 1;
}
@media (max-width: 767.98px) {
  .pricing-section-three.pull-up .outer-box {
    margin: 0;
    border-radius: 0;
  }
}
.pricing-section-three .outer-box {
  background-color: #f2efea;
  background-image: url("../images/icons/shape-bg1.png");
  background-repeat: no-repeat;
  background-position: top center;
  background-size: cover;
  margin: 0 60px;
  padding: 120px 0 96px;
  position: relative;
}
@media (max-width: 1399.98px) {
  .pricing-section-three .outer-box {
    background-image: none;
    margin: 0 24px;
  }
}
@media (max-width: 1199.98px) {
  .pricing-section-three .outer-box {
    margin: 0;
  }
}
@media (max-width: 767.98px) {
  .pricing-section-three .outer-box {
    padding: 90px 0 80px;
  }
}

.pricing-block-three {
  margin-bottom: 24px;
}
.pricing-block-three .inner-box {
  background-color: #FAF1EE;
  border: 1px solid rgba(154, 86, 58, 0.1);
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 20px 30px;
}
@media (max-width: 575.98px) {
  .pricing-block-three .inner-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 40px 15px;
  }
}
.pricing-block-three .inner-box:hover .image-box:after {
  background-color: var(--theme-color-dark);
  color: var(--theme-color-light);
}
.pricing-block-three .inner-box:hover .image-box .image a img {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.pricing-block-three .inner-box .image-box {
  margin-right: 21px;
  position: relative;
}
.pricing-block-three .inner-box .image-box:after {
  background-color: var(--theme-color1);
  border-radius: 50%;
  color: var(--theme-color-light);
  counter-increment: my-sec-counter;
  content: counter(my-sec-counter) " ";
  position: absolute;
  line-height: 1;
  right: 5px;
  top: -7px;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  width: 25px;
  height: 25px;
  line-height: 25px;
  z-index: 1;
}
.pricing-block-three .inner-box .image-box .image {
  width: 76px;
  height: 74px;
  overflow: hidden;
  border-radius: 50%;
}
.pricing-block-three .inner-box .image-box .image a img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-block-three .inner-box .content-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  overflow: hidden;
  width: 100%;
}
@media (max-width: 575.98px) {
  .pricing-block-three .inner-box .content-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    text-align: center;
    padding-top: 20px;
  }
}
.pricing-block-three .inner-box .content-box .inner {
  position: relative;
}
.pricing-block-three .inner-box .content-box .inner .title {
  display: inline-block;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 9px;
  position: relative;
  padding-right: 15px;
}
@media (max-width: 575.98px) {
  .pricing-block-three .inner-box .content-box .inner .title {
    padding-right: 0;
  }
}
.pricing-block-three .inner-box .content-box .inner .title:after {
  content: "";
  border-bottom: 1px dashed var(--headings-color);
  width: 100%;
  min-width: 500px;
  left: 100%;
  top: 20px;
  position: absolute;
}
@media (max-width: 575.98px) {
  .pricing-block-three .inner-box .content-box .inner .title:after {
    border: none;
  }
}
.pricing-block-three .inner-box .content-box .inner .title a:hover {
  color: var(--theme-color1);
}
.pricing-block-three .inner-box .content-box .inner .text {
  display: block;
  font-size: 14px;
  text-transform: uppercase;
}
.pricing-block-three .inner-box .content-box .price {
  background-color: #FAF1EE;
  color: var(--theme-color1);
  font-size: 22px;
  font-weight: 600;
  font-family: var(--title-font);
  position: relative;
  padding-left: 12px;
}
@media (max-width: 575.98px) {
  .pricing-block-three .inner-box .content-box .price {
    padding-left: 0;
  }
}

/*** 
====================================================================
  Pricing Section Four
====================================================================
***/
.pricing-section-four {
  background-color: var(--theme-color5);
  background-repeat: no-repeat;
  background-position: top center;
  background-size: cover;
  padding: 120px 0 244px;
  position: relative;
}
@media (max-width: 1199.98px) {
  .pricing-section-four {
    padding: 90px 0;
  }
}
.pricing-section-four .leaf1 {
  background-image: url(../images/icons/shape-style1.png);
  background-repeat: no-repeat;
  position: absolute;
  width: 225px;
  height: 53px;
  right: 100px;
  top: 336px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .pricing-section-four .leaf1 {
    display: none;
  }
}
.pricing-section-four .leaf2 {
  background-image: url(../images/icons/shape-style1.png);
  background-repeat: no-repeat;
  position: absolute;
  width: 225px;
  height: 53px;
  left: 100px;
  top: 336px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .pricing-section-four .leaf2 {
    display: none;
  }
}
.pricing-section-four .leaf3 {
  background-image: url(../images/icons/shape-style1.png);
  background-repeat: no-repeat;
  position: absolute;
  width: 225px;
  height: 53px;
  right: 100px;
  bottom: 121px;
  pointer-events: none;
  -webkit-transform: rotateX(180deg);
          transform: rotateX(180deg);
}
@media (max-width: 1199.98px) {
  .pricing-section-four .leaf3 {
    display: none;
  }
}
.pricing-section-four .leaf4 {
  background-image: url(../images/icons/shape-style1.png);
  background-repeat: no-repeat;
  position: absolute;
  width: 225px;
  height: 53px;
  left: 100px;
  bottom: 121px;
  pointer-events: none;
  -webkit-transform: rotateX(180deg);
          transform: rotateX(180deg);
}
@media (max-width: 1199.98px) {
  .pricing-section-four .leaf4 {
    display: none;
  }
}
.pricing-section-four .leaf5 {
  background-image: url(../images/icons/shape-style2.png);
  background-repeat: no-repeat;
  position: absolute;
  width: 47px;
  height: 376px;
  right: 100px;
  top: 538px;
  pointer-events: none;
  -webkit-transform: rotateY(180deg);
          transform: rotateY(180deg);
}
@media (max-width: 1599.98px) {
  .pricing-section-four .leaf5 {
    display: none;
  }
}
.pricing-section-four .leaf6 {
  background-image: url(../images/icons/shape-style2.png);
  background-repeat: no-repeat;
  position: absolute;
  width: 47px;
  height: 376px;
  left: 100px;
  top: 538px;
  pointer-events: none;
}
@media (max-width: 1599.98px) {
  .pricing-section-four .leaf6 {
    display: none;
  }
}
@media (max-width: 991.98px) {
  .pricing-section-four .content-column {
    margin-bottom: 70px;
  }
}
.pricing-section-four .content-column:last-child {
  margin-bottom: 0;
}
.pricing-section-four .pricing-block {
  margin-bottom: 60px;
}
.pricing-section-four .pricing-block:last-child {
  margin-bottom: 0;
}
.pricing-section-four .pricing-block .inner-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px dashed rgba(112, 112, 112, 0.25);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 60px;
}
.pricing-section-four .pricing-block .inner-box:hover .image-box .image a img {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.pricing-section-four .pricing-block .inner-box .image-box {
  margin-right: 21px;
}
.pricing-section-four .pricing-block .inner-box .image-box .image {
  width: 76px;
  height: 74px;
  overflow: hidden;
  border-radius: 11px;
}
.pricing-section-four .pricing-block .inner-box .image-box .image a img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.pricing-section-four .pricing-block .inner-box .content-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}
.pricing-section-four .pricing-block .inner-box .content-box .inner .title {
  font-weight: 500;
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .pricing-section-four .pricing-block .inner-box .content-box .inner .title {
    font-size: 24px;
  }
}
.pricing-section-four .pricing-block .inner-box .content-box .inner .title a:hover {
  color: var(--theme-color1);
}
.pricing-section-four .pricing-block .inner-box .content-box .inner .text {
  color: var(--theme-color1);
  font-size: 20px;
  font-weight: 400;
  font-family: var(--title-font);
}
.pricing-section-four .pricing-block .inner-box .content-box .price {
  color: var(--theme-color1);
  font-size: 40px;
  font-family: var(--title-font);
}
@media (max-width: 991.98px) {
  .pricing-section-four .image-column {
    display: none;
  }
}
.pricing-section-four .image-column .inner-box {
  position: relative;
}
@media (max-width: 991.98px) {
  .pricing-section-four .image-column .inner-box {
    text-align: center;
    margin: 90px 0;
  }
}
.pricing-section-four .image-column .inner-box:before {
  content: "";
  background: #F9F6F1;
  border-radius: 208px;
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: -22px;
}
@media (max-width: 991.98px) {
  .pricing-section-four .image-column .inner-box:before {
    display: none;
  }
}
.pricing-section-four .image-column .inner-box .bg-image {
  width: 151px;
  height: 145px;
  top: -30px;
  right: 0;
  left: auto;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .pricing-section-four .image-column .inner-box .bg-image {
    left: 50%;
  }
}
.pricing-section-four .image-column .inner-box .image {
  border-radius: 208px;
  border: 1px solid #DFDFDF;
  padding: 30px;
}
.pricing-section-four .image-column .inner-box .image img {
  border-radius: 208px;
}

/*** 

====================================================================
    Features Section
====================================================================

***/
.features-section {
  padding: 0 0 80px;
}
.features-section .content-column .inner-column {
  position: relative;
  padding-top: 84px;
}
@media (max-width: 1399.98px) {
  .features-section .content-column .inner-column {
    padding-top: 50px;
  }
}
.features-section .content-column .inner-column .sec-title .text {
  margin-top: 11px;
  margin-bottom: 35px;
}
.features-section .content-column .inner-column .bg-image {
  position: absolute;
  width: 948px;
  height: 600px;
  top: 0;
  right: calc(100% + 126px);
}
@media (max-width: 991.98px) {
  .features-section .content-column .inner-column .bg-image {
    display: none;
  }
}

.list-style-two .feature-block .inner-box {
  position: relative;
  margin-bottom: 35px;
}
.list-style-two .feature-block .inner-box:hover .icon {
  -webkit-transform: rotateY(180deg) !important;
          transform: rotateY(180deg) !important;
}
.list-style-two .feature-block .inner-box .icon {
  position: absolute;
  margin-bottom: 0;
  top: -3px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.list-style-two .feature-block .inner-box .title {
  font-size: 22px;
  font-weight: 500;
  margin-bottom: 0;
  margin-left: 35px;
}

/*** 

====================================================================
    Features Section Two
====================================================================

***/
.features-section-two {
  padding: 120px 0;
}
@media (max-width: 767.98px) {
  .features-section-two {
    padding: 90px 0;
  }
}
.features-section-two .sec-title {
  margin-bottom: 125px;
}
@media (max-width: 767.98px) {
  .features-section-two .sec-title {
    text-align: center;
    margin-bottom: 90px;
  }
}
.features-section-two .sec-title .text {
  margin-top: 65px;
}

.feature-block-two .inner-box {
  text-align: center;
  position: relative;
}
@media (max-width: 991.98px) {
  .feature-block-two .inner-box {
    margin-bottom: 30px;
  }
}
.feature-block-two .inner-box:hover .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.feature-block-two .inner-box .icon {
  color: var(--theme-color1);
  font-size: 110px;
  min-height: 164px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.feature-block-two .inner-box .title {
  font-size: 32px;
  font-weight: 400;
  margin-bottom: 21px;
  margin-top: 30px;
}
.feature-block-two .inner-box .title:hover {
  color: var(--theme-color1);
}
.feature-block-two .inner-box .text {
  margin: 0 30px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 1199.98px) {
  .feature-block-two .inner-box .text {
    margin: 0;
  }
}

/*** 

====================================================================
    About Section
====================================================================

***/
.about-section {
  padding: 154px 0 69px;
  position: relative;
}
.about-section .about1-9 {
  background-image: url(../images/resource/about1-9.png);
  position: absolute;
  width: 312px;
  height: 519px;
  right: 0px;
  top: 0;
}
@media (max-width: 1199.98px) {
  .about-section .about1-9 {
    display: none;
  }
}
.about-section .sec-title h2 {
  margin-top: -5px;
}
@media (max-width: 991.98px) {
  .about-section .sec-title h2 {
    margin-top: 0;
  }
}
.about-section .sec-title h2 strong {
  font-weight: 700;
}
.about-section .sec-title h2 strong span {
  position: relative;
  line-height: 1;
  top: -16px;
}
@media (max-width: 1399.98px) {
  .about-section .sec-title h2 strong span {
    top: 0;
  }
}
.about-section .sec-title .text {
  font-style: italic;
  font-weight: 600;
  margin-top: 1px;
  margin-right: 8px;
}
@media (max-width: 991.98px) {
  .about-section .sec-title .text {
    margin-top: 10px;
  }
}
@media (max-width: 575.98px) {
  .about-section .sec-title .text {
    font-size: 14px;
    line-height: 26px;
  }
}
.about-section .image-column .inner-column .image-box {
  position: relative;
}
@media (max-width: 1199.98px) {
  .about-section .image-column .inner-column .image-box {
    margin-bottom: 90px;
  }
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box {
    margin-bottom: 70px;
  }
}
.about-section .image-column .inner-column .image-box .image {
  margin-top: -32px;
}
.about-section .image-column .inner-column .image-box .exp-box {
  display: inline-block;
  position: absolute;
  top: 8px;
  left: 86px;
  text-align: center;
}
.about-section .image-column .inner-column .image-box .exp-box .bg-image {
  width: 170px;
  height: 170px;
  right: 0;
  bottom: -74px;
  left: auto;
  top: auto;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box .exp-box .bg-image {
    right: 0;
    bottom: -48px;
  }
}
.about-section .image-column .inner-column .image-box .exp-box .inner {
  position: relative;
  top: 37px;
  right: 54px;
  z-index: 1;
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box .exp-box .inner {
    top: 5px;
  }
}
.about-section .image-column .inner-column .image-box .exp-box .inner .title {
  color: var(--theme-color-light);
  font-size: 60px;
  font-weight: 500;
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box .exp-box .inner .title {
    font-size: 50px;
  }
}
.about-section .image-column .inner-column .image-box .exp-box .inner .text {
  color: var(--theme-color-light);
  position: relative;
  top: -15px;
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box .exp-box .inner .text {
    margin-top: -10px;
  }
}
.about-section .image-column .inner-column .image-box .bg-image-one {
  bottom: -74px;
  right: -8px;
  left: auto;
  top: auto;
  width: 256px;
  height: 363px;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .about-section .image-column .inner-column .image-box .bg-image-one {
    display: none;
  }
}
.about-section .image-column .inner-column .image-box .bg-image-two {
  bottom: -82px;
  left: -254px;
  top: auto;
  right: auto;
  width: 320px;
  height: 312px;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .about-section .image-column .inner-column .image-box .bg-image-two {
    display: none;
  }
}
.about-section .image-column .inner-column .image-box .bg-image-three {
  bottom: -36px;
  left: 0;
  top: auto;
  right: auto;
  width: 527px;
  height: 517px;
  pointer-events: none;
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box .bg-image-three {
    display: none;
  }
}
.about-section .image-column .inner-column .image-box .bg-image-four {
  bottom: -33px;
  left: 13px;
  top: auto;
  right: auto;
  width: 526px;
  height: 526px;
  z-index: -1;
  pointer-events: none;
}
@media (max-width: 575.98px) {
  .about-section .image-column .inner-column .image-box .bg-image-four {
    display: none;
  }
}
.about-section .content-column .inner-column {
  margin-top: -31px;
}
@media (max-width: 991.98px) {
  .about-section .content-column .inner-column {
    margin-top: 0;
  }
}
@media (max-width: 767.98px) {
  .about-section .content-column .inner-column {
    margin-bottom: 80px;
  }
}
@media (max-width: 575.98px) {
  .about-section .content-column .inner-column {
    margin-bottom: 70px;
  }
}
.about-section .content-column .inner-column .list-style {
  margin-bottom: 38px;
  margin-top: 25px;
}
@media (max-width: 575.98px) {
  .about-section .content-column .inner-column .list-style {
    margin-bottom: 30px;
  }
}
.about-section .content-column .inner-column .list-style li {
  margin-bottom: 10px;
  color: var(--text-color);
  font-size: 16px;
  font-weight: 400;
  position: relative;
  padding-left: 28px;
}
@media (max-width: 575.98px) {
  .about-section .content-column .inner-column .list-style li {
    font-size: 14px;
  }
}
.about-section .content-column .inner-column .list-style li:last-child {
  margin-bottom: 0;
}
.about-section .content-column .inner-column .list-style li:hover .icon {
  -webkit-transform: rotateY(357deg) !important;
          transform: rotateY(357deg) !important;
}
.about-section .content-column .inner-column .list-style li .icon {
  color: var(--theme-color1);
  position: absolute;
  margin-right: 4.5px;
  left: 0;
  top: -2px;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.about-section .content-column .inner-column .author-box .inner {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.about-section .content-column .inner-column .author-box .inner .theme-btn {
  margin-right: 34px;
}
@media (max-width: 991.98px) {
  .about-section .content-column .inner-column .author-box .inner .theme-btn {
    margin-right: 24px;
  }
}
@media (max-width: 575.98px) {
  .about-section .content-column .inner-column .author-box .inner .theme-btn {
    margin-right: 21px;
  }
}
.about-section .content-column .inner-column .author-box .inner .thumb {
  margin-bottom: 0;
  margin-right: 22px;
}
@media (max-width: 991.98px) {
  .about-section .content-column .inner-column .author-box .inner .thumb {
    margin-right: 16px;
  }
}
.about-section .content-column .inner-column .author-box .inner .info {
  margin-top: -4px;
}
@media (max-width: 991.98px) {
  .about-section .content-column .inner-column .author-box .inner .info {
    margin-top: 0;
  }
}
.about-section .content-column .inner-column .author-box .inner .info .sign {
  margin-bottom: 3px;
}
.about-section .content-column .inner-column .author-box .inner .info .name {
  color: #1C1A1D;
  font-size: 14px;
  font-weight: 500;
}
@media (max-width: 991.98px) {
  .about-section .content-column .inner-column .author-box .inner .info .name {
    font-size: 12px;
  }
}
.about-section .timetable-block {
  text-align: center;
}
.about-section .timetable-block .inner .content-top {
  background-color: var(--theme-color1-dark);
  padding: 32px 0 29px;
}
.about-section .timetable-block .inner .content-top i {
  color: var(--theme-color-light);
  font-size: 35px;
}
.about-section .timetable-block .inner .content-top .title {
  color: var(--theme-color-light);
  font-weight: 500;
  margin-top: 6px;
  margin-bottom: 0;
}
.about-section .timetable-block .inner .content {
  background-color: var(--theme-color1);
  padding: 45px 0 114px;
  position: relative;
}
.about-section .timetable-block .inner .content .time-box {
  margin-bottom: 25px;
}
.about-section .timetable-block .inner .content .time-box:last-child {
  margin-bottom: 0;
}
.about-section .timetable-block .inner .content .time-box .opening-days {
  color: var(--theme-color-light);
  font-size: 14px;
  margin-bottom: 5px;
}
.about-section .timetable-block .inner .content .time-box .opening-hours {
  color: var(--theme-color-light);
  font-size: 18px;
  font-weight: 600;
}
.about-section .timetable-block .inner .content .bg-image {
  width: 261px;
  height: 121px;
  top: auto;
  bottom: 0;
}

/*** 

====================================================================
    About Section Two
====================================================================

***/
.about-section-two {
  position: relative;
  padding: 180px 0 40px;
}
@media (max-width: 1199.98px) {
  .about-section-two {
    padding: 80px 0 40px;
  }
}
.about-section-two .about2-pattrn1 {
  background-image: url(../images/resource/about2-7.png);
  position: absolute;
  width: 425px;
  height: 422px;
  left: 0;
  bottom: -45px;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .about-section-two .about2-pattrn1 {
    display: none;
  }
}
.about-section-two .image-column .inner-column .image-box {
  position: relative;
}
.about-section-two .image-column .inner-column .image-box .play-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: absolute;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.about-section-two .image-column .inner-column .image-box .play-box .play-btn-two {
  background-color: var(--theme-color1);
  bottom: 0;
  border-radius: 50%;
  right: 0;
  height: 206px;
  width: 206px;
  line-height: 206px;
  left: -16px;
  z-index: 3;
}
.about-section-two .image-column .inner-column .image-box .play-box .play-btn-two .icon {
  border: 1px dashed var(--theme-color-light);
  font-size: 44px;
  color: var(--theme-color-light);
}
.about-section-two .image-column .inner-column .image-box .play-box .play-btn-two .icon:hover {
  color: var(--theme-color-dark);
}
.about-section-two .image-column .inner-column .image-box .play-box .play-btn-two .icon-text-2 {
  background-image: url(../images/icons/icon-text-2.png);
  bottom: -7px;
  right: -6px;
  z-index: 2;
  width: 196px;
  height: 194px;
  pointer-events: none;
  position: relative;
  -webkit-animation: fa-spin 40s infinite linear;
          animation: fa-spin 40s infinite linear;
}
.about-section-two .image-column .inner-column .image-box .image {
  margin-left: 20px;
}
@media (max-width: 575.98px) {
  .about-section-two .image-column .inner-column .image-box .image {
    display: none;
  }
}
.about-section-two .image-column .inner-column .image-box .image a img {
  width: 100%;
  height: 100%;
}
.about-section-two .image-column .inner-column .image-box .image-one {
  height: 100%;
  margin-left: 0;
}
.about-section-two .image-column .inner-column .image-box .image-one img {
  height: 100%;
}
@media (max-width: 575.98px) {
  .about-section-two .image-column .inner-column .image-box .image-one img {
    width: 100%;
  }
}
.about-section-two .image-column .inner-column .image-box .image-two {
  margin-left: 20px;
  margin-bottom: 20px;
}
@media (max-width: 575.98px) {
  .about-section-two .image-column .inner-column .image-box .image-two {
    display: none;
  }
}
.about-section-two .image-column .inner-column .image-box .image-two img {
  width: 100%;
}
.about-section-two .content-column .inner-column {
  margin-left: 88px;
}
@media (max-width: 1199.98px) {
  .about-section-two .content-column .inner-column {
    margin-left: 0;
  }
}
@media (max-width: 991.98px) {
  .about-section-two .content-column .inner-column {
    margin-top: 80px;
  }
}
@media (max-width: 575.98px) {
  .about-section-two .content-column .inner-column {
    margin-top: 50px;
  }
}
.about-section-two .content-column .inner-column .sec-title .text {
  margin-top: 17px;
}
.about-section-two .content-column .inner-column .list-box .inner .thumb {
  margin-bottom: 23px;
  margin-top: 38px;
}
.about-section-two .content-column .inner-column .list-box .inner .thumb img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.about-section-two .content-column .inner-column .list-box .inner .title {
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .about-section-two .content-column .inner-column .list-box .inner .title {
    font-size: 24px;
  }
}
.about-section-two .content-column .inner-column .list-box .inner:hover .thumb img {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.about-section-two .content-column .inner-column .author-box {
  margin-top: 48px;
}
.about-section-two .content-column .inner-column .author-box .inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 991.98px) {
  .about-section-two .content-column .inner-column .author-box .inner {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
@media (max-width: 575.98px) {
  .about-section-two .content-column .inner-column .author-box .inner {
    display: block;
  }
}
.about-section-two .content-column .inner-column .author-box .inner .contact-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 991.98px) {
  .about-section-two .content-column .inner-column .author-box .inner .contact-info {
    margin-left: 50px;
  }
}
@media (max-width: 575.98px) {
  .about-section-two .content-column .inner-column .author-box .inner .contact-info {
    margin-left: 0;
    margin-top: 40px;
  }
}
.about-section-two .content-column .inner-column .author-box .inner .contact-info .sign {
  margin-left: 32px;
}
.about-section-two .content-column .inner-column .author-box .inner .contact-info .sign .text {
  color: var(--theme-color-dark);
  font-size: 14px;
}
.about-section-two .content-column .inner-column .author-box .inner .contact-info .sign a {
  color: var(--theme-color-dark);
  font-size: 22px;
  font-weight: 700;
  font-family: var(--title-font);
  letter-spacing: 5px;
}
.about-section-two .content-column .inner-column .author-box .inner .contact-info .sign a:hover {
  color: var(--theme-color1);
}

/*** 

====================================================================
    About Section Three
====================================================================

***/
.about-section-three {
  background-color: var(--theme-color5);
  position: relative;
  padding: 10px 0 40px;
}
@media (max-width: 1199.98px) {
  .about-section-three {
    padding: 10px 0 26px;
  }
}
@media (max-width: 991.98px) {
  .about-section-three .image-column .inner-column {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
.about-section-three .image-column .inner-column .image-box {
  position: relative;
  margin-left: 60px;
}
@media (max-width: 991.98px) {
  .about-section-three .image-column .inner-column .image-box {
    margin-left: 0;
    display: inline-block;
  }
}
.about-section-three .image-column .inner-column .image-box .text-rotate {
  background-color: var(--theme-color-light);
  -webkit-box-shadow: 0px 4px 30px 0px rgba(var(--theme-color-dark-rgb), 0.05);
          box-shadow: 0px 4px 30px 0px rgba(var(--theme-color-dark-rgb), 0.05);
  border-radius: 50%;
  position: absolute;
  right: 38px;
  bottom: 78px;
  height: 160px;
  width: 160px;
  z-index: 2;
}
.about-section-three .image-column .inner-column .image-box .text-rotate .icon-text-2 {
  background-image: url(../images/icons/icon-text-3.png);
  z-index: 2;
  width: 123px;
  height: 124px;
  pointer-events: none;
  position: relative;
  top: 18px;
  left: 18px;
  -webkit-animation: fa-spin 40s infinite linear;
          animation: fa-spin 40s infinite linear;
}
.about-section-three .image-column .inner-column .image-box .text-rotate .play-btn-two {
  left: calc(50% - 15px);
  bottom: 60px;
  z-index: 2;
}
.about-section-three .image-column .inner-column .image-box .text-rotate .play-btn-two:hover .icon {
  background-color: transparent;
  color: var(--theme-color1);
}
.about-section-three .image-column .inner-column .image-box .text-rotate .play-btn-two .icon {
  color: var(--theme-color1);
  font-size: 30px;
  -webkit-transform: none;
          transform: none;
  left: 0;
  top: 0;
  display: block;
  width: auto;
  height: auto;
  line-height: 1;
}
.about-section-three .image-column .inner-column .image-box .image-one {
  border: 1px solid #DFDFDF;
  border-radius: 240px;
  margin-bottom: 0;
  padding: 30px;
  display: inline-block;
  z-index: 1;
}
.about-section-three .image-column .inner-column .image-box .image-one img {
  border-radius: 214px;
  height: 100%;
}
@media (max-width: 575.98px) {
  .about-section-three .image-column .inner-column .image-box .image-one img {
    width: 100%;
  }
}
.about-section-three .image-column .inner-column .image-box .image-two {
  margin-bottom: 0;
  position: absolute;
  left: -85px;
  bottom: 16px;
  z-index: 1;
  margin-bottom: 20px;
}
@media (max-width: 575.98px) {
  .about-section-three .image-column .inner-column .image-box .image-two {
    display: none;
  }
}
.about-section-three .image-column .inner-column .image-box .image-two img {
  width: 100%;
}
.about-section-three .content-column .inner-column {
  margin-left: 22px;
  padding-top: 115px;
}
@media (max-width: 1199.98px) {
  .about-section-three .content-column .inner-column {
    margin-left: 0;
    padding-top: 0;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
@media (max-width: 991.98px) {
  .about-section-three .content-column .inner-column {
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    height: auto;
    margin-bottom: 70px;
  }
}
.about-section-three .content-column .inner-column .sec-title {
  position: relative;
}
@media (max-width: 991.98px) {
  .about-section-three .content-column .inner-column .sec-title {
    text-align: center;
  }
}
.about-section-three .content-column .inner-column .sec-title:before {
  background-image: url("../images/icons/shape-flower.png");
  content: "";
  position: absolute;
  width: 200px;
  height: 200px;
  left: -29px;
  top: -44px;
  z-index: 0;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .about-section-three .content-column .inner-column .sec-title:before {
    display: none;
  }
}
.about-section-three .content-column .inner-column .sec-title .title-stroke-text {
  font-size: 150px;
  position: absolute;
  right: -150px;
  margin-left: -341px;
  top: -95px;
  letter-spacing: 17.13px;
}
@media (max-width: 1199.98px) {
  .about-section-three .content-column .inner-column .sec-title .title-stroke-text {
    font-size: 80px;
    letter-spacing: 7.13px;
    right: auto;
    left: -120px;
    margin-left: 0;
  }
}
@media (max-width: 991.98px) {
  .about-section-three .content-column .inner-column .sec-title .title-stroke-text {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .about-section-three .content-column .inner-column .sec-title h2 {
    font-size: 40px;
  }
}
.about-section-three .content-column .inner-column .sec-title .text {
  margin-top: 17px;
}
.about-section-three .content-column .inner-column .info-box {
  margin-top: 48px;
}
.about-section-three .content-column .inner-column .info-box .inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 991.98px) {
  .about-section-three .content-column .inner-column .info-box .inner {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
@media (max-width: 575.98px) {
  .about-section-three .content-column .inner-column .info-box .inner {
    display: block;
  }
}
@media (max-width: 575.98px) {
  .about-section-three .content-column .inner-column .info-box .inner .fact-info {
    text-align: center;
  }
}
@media (max-width: 575.98px) {
  .about-section-three .content-column .inner-column .info-box .inner .fact-info .graph-box .pie-graph .title {
    text-align: left;
  }
}
.about-section-three .content-column .inner-column .info-box .inner .image-box {
  position: relative;
}
@media (max-width: 991.98px) {
  .about-section-three .content-column .inner-column .info-box .inner .image-box {
    margin-left: 30px;
  }
}
@media (max-width: 991.98px) {
  .about-section-three .content-column .inner-column .info-box .inner .image-box {
    margin-left: 0;
    margin-top: 50px;
  }
}
.about-section-three .content-column .inner-column .info-box .inner .image-box .image-one {
  position: relative;
  z-index: 1;
}
.about-section-three .content-column .inner-column .info-box .inner .image-box .image-two {
  position: absolute;
  bottom: 0;
  right: -77px;
}

.graph-box .pie-graph {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  margin-bottom: 42px;
}
.graph-box .pie-graph .graph-outer {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-bottom: 0;
}
.graph-box .pie-graph .graph-outer .inner-text {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 22px;
  color: var(--theme-color2);
  font-weight: 600;
  line-height: 1em;
}
.graph-box .pie-graph .title {
  font-size: 24px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 1.2em;
  margin-bottom: 0;
  margin-left: 25px;
  padding-top: 16px;
}
.graph-box .pie-graph .title:after {
  background-color: var(--theme-color1);
  content: "";
  position: absolute;
  left: 0;
  height: 3px;
  width: 40px;
  top: 0;
}
.graph-box .CircularProgressbar .CircularProgressbar-text {
  font-size: 22px;
}

/*** 

====================================================================
    About Section Four
====================================================================

***/
.about-section-four {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 1199.98px) {
  .about-section-four {
    padding: 80px 0;
  }
}
.about-section-four .about6-shape1 {
  background-image: url(../images/resource/about6-3.png);
  position: absolute;
  width: 259px;
  height: 345px;
  left: 0;
  top: 51px;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .about-section-four .about6-shape1 {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .about-section-four .image-column .inner-column {
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}
.about-section-four .image-column .inner-column .image-box {
  position: relative;
}
.about-section-four .image-column .inner-column .image-box .play-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: absolute;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.about-section-four .image-column .inner-column .image-box .play-box .play-btn-two {
  background-color: var(--theme-color1);
  bottom: 55px;
  border-radius: 50%;
  right: 0;
  left: 42px;
  z-index: 3;
}
@media (max-width: 575.98px) {
  .about-section-four .image-column .inner-column .image-box .play-box .play-btn-two {
    bottom: 0;
    left: 0;
  }
}
.about-section-four .image-column .inner-column .image-box .play-box .play-btn-two .icon {
  background-color: var(--theme-color-light);
  outline: 2px solid var(--theme-color1);
  outline-offset: -7px;
  font-size: 38x;
  height: 110px;
  width: 110px;
  color: var(--theme-color1);
}
.about-section-four .image-column .inner-column .image-box .play-box .play-btn-two .icon:hover {
  color: var(--theme-color-dark);
}
.about-section-four .image-column .inner-column .image-box .play-box .play-btn-two .icon-text-2 {
  background-image: url(../images/icons/icon-text-2.png);
  bottom: -7px;
  right: -6px;
  z-index: 2;
  width: 196px;
  height: 194px;
  pointer-events: none;
  position: relative;
  -webkit-animation: fa-spin 40s infinite linear;
          animation: fa-spin 40s infinite linear;
}
.about-section-four .image-column .inner-column .image-box .image {
  margin-left: 20px;
}
@media (max-width: 575.98px) {
  .about-section-four .image-column .inner-column .image-box .image {
    display: none;
  }
}
.about-section-four .image-column .inner-column .image-box .image a img {
  width: 100%;
  height: 100%;
}
.about-section-four .image-column .inner-column .image-box .image-one {
  height: 100%;
  margin-left: 0;
}
.about-section-four .image-column .inner-column .image-box .image-one img {
  height: 100%;
}
@media (max-width: 575.98px) {
  .about-section-four .image-column .inner-column .image-box .image-one img {
    width: 100%;
  }
}
.about-section-four .image-column .inner-column .image-box .image-two {
  position: absolute;
  bottom: 0;
  left: 0;
  margin-bottom: 20px;
}
@media (max-width: 575.98px) {
  .about-section-four .image-column .inner-column .image-box .image-two {
    display: none;
  }
}
.about-section-four .image-column .inner-column .image-box .image-two img {
  width: 100%;
}
.about-section-four .content-column .inner-column {
  margin-left: 88px;
  padding-top: 70px;
}
@media (max-width: 1399.98px) {
  .about-section-four .content-column .inner-column {
    padding-top: 30px;
    margin-left: 50px;
  }
}
@media (max-width: 1199.98px) {
  .about-section-four .content-column .inner-column {
    margin-left: 30px;
    padding-top: 0;
  }
}
@media (max-width: 991.98px) {
  .about-section-four .content-column .inner-column {
    margin-left: 0;
    padding-top: 0;
    margin-bottom: 70px;
  }
}
@media (max-width: 1199.98px) {
  .about-section-four .content-column .inner-column .sec-title h2 {
    font-size: 44px;
  }
}
.about-section-four .content-column .inner-column .sec-title .text {
  margin-top: 17px;
}
.about-section-four .content-column .inner-column .list-box .inner .thumb {
  margin-bottom: 23px;
  margin-top: 38px;
}
.about-section-four .content-column .inner-column .list-box .inner .thumb img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.about-section-four .content-column .inner-column .list-box .inner .title {
  margin-bottom: 0;
}
@media (max-width: 1199.98px) {
  .about-section-four .content-column .inner-column .list-box .inner .title {
    font-size: 24px;
  }
}
.about-section-four .content-column .inner-column .list-box .inner:hover .thumb img {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.about-section-four .content-column .inner-column .author-box {
  margin-top: 48px;
}
.about-section-four .content-column .inner-column .author-box .inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 991.98px) {
  .about-section-four .content-column .inner-column .author-box .inner {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}
@media (max-width: 575.98px) {
  .about-section-four .content-column .inner-column .author-box .inner {
    display: block;
  }
}
.about-section-four .content-column .inner-column .author-box .inner .contact-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
@media (max-width: 1199.98px) {
  .about-section-four .content-column .inner-column .author-box .inner .contact-info {
    margin-left: 20px;
  }
}
@media (max-width: 575.98px) {
  .about-section-four .content-column .inner-column .author-box .inner .contact-info {
    margin-left: 0;
    margin-top: 40px;
  }
}
.about-section-four .content-column .inner-column .author-box .inner .contact-info .sign {
  margin-left: 32px;
}
.about-section-four .content-column .inner-column .author-box .inner .contact-info .sign .text {
  color: var(--theme-color-dark);
  font-size: 14px;
}
.about-section-four .content-column .inner-column .author-box .inner .contact-info .sign a {
  color: var(--theme-color-dark);
  font-size: 22px;
  font-weight: 700;
  font-family: var(--title-font);
  letter-spacing: 5px;
}
.about-section-four .content-column .inner-column .author-box .inner .contact-info .sign a:hover {
  color: var(--theme-color1);
}

/*** 

====================================================================
    About Section Five
====================================================================

***/
.about-section-five {
  background-color: #f7f4ef;
  padding: 98px 0 120px;
  position: relative;
}
.about-section-five .about8-1 {
  background-image: url(../images/resource/about8-3.png);
  position: absolute;
  width: 1222px;
  height: 100%;
  right: calc(50% - 260px);
  top: 0;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .about-section-five .about8-1 {
    display: none;
  }
}
.about-section-five .about8-2 {
  background-image: url(../images/resource/about8-6.png);
  position: absolute;
  width: 201px;
  height: 332px;
  left: 0px;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}
@media (max-width: 1619.98px) {
  .about-section-five .about8-2 {
    display: none;
  }
}
.about-section-five .sec-title h2 {
  margin-top: -5px;
}
@media (max-width: 991.98px) {
  .about-section-five .sec-title h2 {
    margin-top: 0;
  }
}
.about-section-five .sec-title h2 strong {
  font-weight: 700;
}
.about-section-five .sec-title h2 strong span {
  position: relative;
  line-height: 1;
  top: -16px;
}
@media (max-width: 1399.98px) {
  .about-section-five .sec-title h2 strong span {
    top: 0;
  }
}
.about-section-five .sec-title .text {
  font-style: italic;
  font-weight: 600;
  margin-top: 1px;
  margin-right: 8px;
}
@media (max-width: 991.98px) {
  .about-section-five .sec-title .text {
    margin-top: 10px;
  }
}
@media (max-width: 575.98px) {
  .about-section-five .sec-title .text {
    font-size: 14px;
    line-height: 26px;
  }
}
.about-section-five .image-column .inner-column {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  height: 100%;
}
.about-section-five .image-column .inner-column .image-box {
  position: relative;
  padding-left: 38px;
}
@media (max-width: 1199.98px) {
  .about-section-five .image-column .inner-column .image-box {
    padding-left: 0;
    margin-top: 50px;
  }
}
@media (max-width: 767.98px) {
  .about-section-five .image-column .inner-column .image-box {
    margin-top: 0;
  }
}
.about-section-five .image-column .inner-column .image-box .image {
  position: relative;
  margin-bottom: 0;
}
.about-section-five .image-column .inner-column .image-box .image img {
  border-radius: 5px;
}
.about-section-five .content-column .inner-column {
  margin-top: 48px;
}
@media (max-width: 991.98px) {
  .about-section-five .content-column .inner-column {
    margin-top: 0;
  }
}
@media (max-width: 767.98px) {
  .about-section-five .content-column .inner-column {
    margin-bottom: 80px;
  }
}
@media (max-width: 575.98px) {
  .about-section-five .content-column .inner-column {
    margin-bottom: 70px;
  }
}
.about-section-five .content-column .inner-column .list-style {
  margin-bottom: 38px;
  margin-top: 25px;
}
@media (max-width: 575.98px) {
  .about-section-five .content-column .inner-column .list-style {
    margin-bottom: 30px;
  }
}
.about-section-five .content-column .inner-column .list-style li {
  margin-bottom: 10px;
  color: var(--text-color);
  font-size: 16px;
  font-weight: 400;
  position: relative;
  padding-left: 28px;
}
@media (max-width: 575.98px) {
  .about-section-five .content-column .inner-column .list-style li {
    font-size: 14px;
  }
}
.about-section-five .content-column .inner-column .list-style li:last-child {
  margin-bottom: 0;
}
.about-section-five .content-column .inner-column .list-style li:hover .icon {
  -webkit-transform: rotateY(357deg) !important;
          transform: rotateY(357deg) !important;
}
.about-section-five .content-column .inner-column .list-style li .icon {
  color: var(--theme-color1);
  position: absolute;
  margin-right: 4.5px;
  left: 0;
  top: -2px;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.about-section-five .content-column .inner-column .author-box .inner {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  z-index: 1;
}
.about-section-five .content-column .inner-column .author-box .inner .theme-btn {
  margin-right: 34px;
}
@media (max-width: 991.98px) {
  .about-section-five .content-column .inner-column .author-box .inner .theme-btn {
    margin-right: 24px;
  }
}
@media (max-width: 575.98px) {
  .about-section-five .content-column .inner-column .author-box .inner .theme-btn {
    margin-right: 21px;
  }
}
.about-section-five .content-column .inner-column .author-box .inner .thumb {
  margin-bottom: 0;
  margin-right: 22px;
}
@media (max-width: 991.98px) {
  .about-section-five .content-column .inner-column .author-box .inner .thumb {
    margin-right: 16px;
  }
}
.about-section-five .content-column .inner-column .author-box .inner .info {
  margin-top: -4px;
}
@media (max-width: 991.98px) {
  .about-section-five .content-column .inner-column .author-box .inner .info {
    margin-top: 0;
  }
}
.about-section-five .content-column .inner-column .author-box .inner .info .sign {
  margin-bottom: 3px;
}
.about-section-five .content-column .inner-column .author-box .inner .info .name {
  color: #1C1A1D;
  font-size: 14px;
  font-weight: 500;
}
@media (max-width: 991.98px) {
  .about-section-five .content-column .inner-column .author-box .inner .info .name {
    font-size: 12px;
  }
}
.about-section-five .timetable-block .inner {
  position: relative;
}
@media (max-width: 1199.98px) {
  .about-section-five .timetable-block .inner {
    margin-top: 70px;
  }
}
.about-section-five .timetable-block .inner:after {
  background-image: url(../images/resource/about8-5.png);
  content: "";
  position: absolute;
  width: 154px;
  height: 262px;
  left: 100%;
  top: 20px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .about-section-five .timetable-block .inner:after {
    display: none;
  }
}
.about-section-five .timetable-block .inner .content-top {
  background-color: var(--theme-color1-dark);
  border-radius: 10px 10px 0 0;
  padding: 32px 0 29px;
  text-align: center;
}
.about-section-five .timetable-block .inner .content-top i {
  color: var(--theme-color-light);
  font-size: 35px;
}
.about-section-five .timetable-block .inner .content-top .title {
  color: var(--theme-color-light);
  font-weight: 500;
  margin-top: 6px;
  margin-bottom: 0;
}
.about-section-five .timetable-block .inner .content {
  background-color: var(--theme-color-light);
  -webkit-box-shadow: 0px 4px 30px 0px rgba(var(--theme-color-dark-rgb), 0.05);
          box-shadow: 0px 4px 30px 0px rgba(var(--theme-color-dark-rgb), 0.05);
  border-radius: 0 0 10px 10px;
  padding: 50px 50px 24px;
  position: relative;
}
.about-section-five .timetable-block .inner .content .time-box {
  margin-bottom: 30px;
}
.about-section-five .timetable-block .inner .content .time-box:last-child {
  margin-bottom: 0;
}
.about-section-five .timetable-block .inner .content .time-box .title {
  font-size: 32px;
  margin-bottom: 13px;
}
.about-section-five .timetable-block .inner .content .time-box .title .icon {
  background-color: var(--theme-color1);
  border-radius: 50%;
  color: var(--theme-color-light);
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  width: 40px;
  text-align: center;
  margin-right: 8px;
}
.about-section-five .timetable-block .inner .content .time-box .opening-days {
  font-weight: 500;
  margin-bottom: 5px;
}
.about-section-five .timetable-block .inner .content .time-box .time-box-address {
  font-weight: 500;
  line-height: 36px;
}
.about-section-five .timetable-block .inner .content .bg-image {
  width: 294px;
  height: 137px;
  top: auto;
  left: auto;
  bottom: 0;
  right: 0;
}

/***

====================================================================
    Marquee Section
====================================================================

***/
.marquee-section {
  position: relative;
  padding: 0 0 100px;
}
@media (max-width: 767.98px) {
  .marquee-section {
    padding: 0 0 70px;
  }
}

.marquee {
  position: relative;
  --duration: 30s;
  --gap: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  gap: var(--gap);
}
.marquee .marquee-group {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-pack: distribute;
      justify-content: space-around;
  gap: var(--gap);
  min-width: 100%;
  -webkit-animation: scroll var(--duration) linear infinite;
          animation: scroll var(--duration) linear infinite;
}
.marquee .text {
  position: relative;
  -webkit-text-fill-color: white;
  -webkit-text-stroke: 1px #606060;
  font-size: 100px;
  font-weight: 700;
  line-height: 121.1px;
  padding-left: 70px;
  margin-left: 70px;
  font-family: var(--title-font);
}
@media (max-width: 991.98px) {
  .marquee .text {
    font-size: 80px;
  }
}
.marquee .text:after {
  background-color: var(--theme-color-dark);
  border-radius: 50%;
  content: "";
  position: absolute;
  left: 0;
  top: calc(50% - 2px);
  height: 9px;
  width: 9px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.marquee .text:hover:after {
  background-color: var(--theme-color-dark);
}
.marquee .text:before {
  position: absolute;
  content: attr(data-text);
  top: -35px;
  left: 70px;
  -webkit-text-fill-color: var(--theme-color-dark);
  width: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  overflow: hidden;
  padding-top: 35px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: calc(100% + 55px);
}
.marquee .text:hover:before {
  width: 100%;
}
@media (prefers-reduced-motion: reduce) {
  .marquee .marquee-group {
    -webkit-animation-play-state: play;
            animation-play-state: play;
  }
}
@-webkit-keyframes scroll {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-100% - var(--gap)));
            transform: translateX(calc(-100% - var(--gap)));
  }
}
@keyframes scroll {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-100% - var(--gap)));
            transform: translateX(calc(-100% - var(--gap)));
  }
}
.marquee.slide-right {
  -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
}
.marquee.slide-right .text {
  -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
}

/***

====================================================================
    Marquee Section Two
====================================================================

***/
.marquee-section-two {
  background-color: var(--theme-color5);
  position: relative;
  padding: 21px 0 100px;
}
@media (max-width: 991.98px) {
  .marquee-section-two {
    padding: 50px 0;
  }
}

.marquee-two {
  position: relative;
  --duration: 30s;
  --gap: 0px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  gap: var(--gap);
}
.marquee-two .marquee-group {
  -ms-flex-negative: 0;
      flex-shrink: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-pack: distribute;
      justify-content: space-around;
  gap: var(--gap);
  min-width: 100%;
  -webkit-animation: scroll var(--duration) linear infinite;
          animation: scroll var(--duration) linear infinite;
}
.marquee-two .text {
  position: relative;
  -webkit-text-fill-color: var(--headings-color);
  -webkit-text-stroke: 1px var(--headings-color);
  font-size: 100px;
  font-weight: 300;
  line-height: 121.1px;
  padding-left: 70px;
  margin-left: 70px;
  font-family: var(--title-font);
}
@media (max-width: 1199.98px) {
  .marquee-two .text {
    font-size: 80px;
  }
}
@media (max-width: 767.98px) {
  .marquee-two .text {
    font-size: 60px;
  }
}
@media (max-width: 575.98px) {
  .marquee-two .text {
    font-size: 40px;
  }
}
.marquee-two .text:after {
  background-image: url(../images/icons/shape-marquee1.png);
  content: "";
  position: absolute;
  left: -50px;
  top: calc(50% - 47px);
  height: 105px;
  width: 79px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 575.98px) {
  .marquee-two .text:after {
    top: calc(50% - 42px);
  }
}
.marquee-two .text:before {
  position: absolute;
  content: attr(data-text);
  top: -35px;
  left: 70px;
  -webkit-text-fill-color: var(--theme-color-light);
  width: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  overflow: hidden;
  padding-top: 35px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: calc(100% + 55px);
}
.marquee-two .text:hover:before {
  width: 100%;
}
@media (prefers-reduced-motion: reduce) {
  .marquee-two .marquee-group {
    -webkit-animation-play-state: play;
            animation-play-state: play;
  }
}
@keyframes scroll {
  0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(calc(-100% - var(--gap)));
            transform: translateX(calc(-100% - var(--gap)));
  }
}

/*** 

====================================================================
  Main Slider
====================================================================

***/
.main-slider {
  position: relative;
}
.main-slider .sub-title {
  color: var(--headings-color);
  font-size: 20px;
  font-weight: 400;
  position: relative;
  display: inline-block;
  line-height: var(--sec-title-subtitle-line-height);
  font-family: var(--sec-title-subtitle-font-family);
  margin-top: 0;
}
.main-slider .title {
  text-transform: uppercase;
  position: relative;
  font-size: var(--h1-font-size);
  font-weight: 700;
  line-height: 1.211em;
  margin-bottom: 20px;
}
@media (max-width: 801.98px) {
  .main-slider .title {
    font-size: 50px;
  }
}
@media (max-width: 424.98px) {
  .main-slider .title {
    font-size: 48px;
  }
}
.main-slider .text {
  max-width: 40%;
  position: relative;
}
@media (max-width: 1280px) {
  .main-slider .text {
    max-width: 50%;
  }
}
@media (max-width: 767px) {
  .main-slider .text {
    max-width: 80%;
  }
}
@media (max-width: 575px) {
  .main-slider .text {
    max-width: initial;
  }
}
.main-slider .style-title {
  bottom: 0;
  color: var(--theme-color-light);
  font-family: var(--title-font);
  font-size: 216px;
  font-weight: 700;
  line-height: 115px;
  letter-spacing: 47.5px;
  left: 0;
  margin: 0 auto;
  opacity: 0.5;
  position: absolute;
  right: 0;
  text-align: center;
  text-transform: uppercase;
}
@media (max-width: 1445.98px) {
  .main-slider .style-title {
    font-size: 130px;
    display: block;
    text-align: center;
    margin-top: 20px;
  }
}
@media (max-width: 1199.98px) {
  .main-slider .style-title {
    letter-spacing: 27px;
    font-size: 100px;
  }
}
@media (max-width: 801.98px) {
  .main-slider .style-title {
    letter-spacing: 16px;
    font-size: 52px;
  }
}
@media (max-width: 479.98px) {
  .main-slider .style-title {
    letter-spacing: 21px;
    font-size: 52px;
  }
}
.main-slider .image-curve {
  background-image: url(../images/main-slider/slide-shape-bottom.png);
  position: absolute;
  width: 100%;
  height: 69px;
  left: 0;
  bottom: 0;
  z-index: 1;
}
@media (max-width: 1199.98px) {
  .main-slider .image-curve {
    display: none;
  }
}
@media (max-width: 1199.98px) {
  .main-slider .image-style-one {
    display: none;
  }
}
.main-slider .image-curve {
  background-image: url(../images/main-slider/slide-shape-bottom.png);
  bottom: 0;
  height: 69px;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 1;
}
.main-slider .content-box {
  padding: 175px 0 400px;
}
@media (max-width: 1199.98px) {
  .main-slider .content-box {
    padding-left: 15px;
    padding: 120px 0 200px 15px;
  }
}
.main-slider .content-box .image-leap {
  position: absolute;
  right: 0;
  top: 0;
}
.main-slider .content-box .image {
  position: absolute;
}
.main-slider .content-box .home2-circle-img {
  left: 0;
  right: 0;
  text-align: center;
}
.main-slider .content-box .image2 {
  bottom: 0;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  text-align: center;
}
@media (max-width: 1199.98px) {
  .main-slider .content-box .image2 {
    text-align: right;
  }
}
@media (max-width: 767.98px) {
  .main-slider .content-box .image2 {
    display: none;
  }
}
.main-slider.slider-style-two .content-box {
  padding: 255px 0 250px;
}
@media (max-width: 1280px) {
  .main-slider.slider-style-two .content-box {
    padding-left: 15px;
  }
}
@media (max-width: 767px) {
  .main-slider.slider-style-two .content-box {
    padding: 100px 0 100px 25px;
  }
}
.main-slider.slider-style-two .content-box .image2 {
  left: 30%;
}
.main-slider.slider-style-two .swiper-button-prev,
.main-slider.slider-style-two .swiper-button-next {
  background-color: rgba(28, 26, 29, 0.5);
  border-radius: 50%;
  height: 80px;
  line-height: 80px;
  text-align: center;
  width: 80px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-slider.slider-style-two .swiper-button-prev:hover,
.main-slider.slider-style-two .swiper-button-next:hover {
  background-color: rgba(194, 167, 78, 0.5);
}
@media (max-width: 1280px) {
  .main-slider.slider-style-two .swiper-button-prev,
  .main-slider.slider-style-two .swiper-button-next {
    height: 60px;
    line-height: 60px;
    width: 60px;
  }
}
.main-slider.slider-style-two .swiper-button-prev:after {
  content: "\f177";
  font-family: "Font Awesome 6 Pro";
}
.main-slider.slider-style-two .swiper-button-next:after {
  content: "\f178";
  font-family: "Font Awesome 6 Pro";
}
.main-slider.slider-style-two .swiper-button-prev:after,
.main-slider.slider-style-two .swiper-button-next:after {
  color: var(--theme-color-light);
  font-size: 30px;
  font-weight: 300;
}
@media (max-width: 1280px) {
  .main-slider.slider-style-two .swiper-button-prev:after,
  .main-slider.slider-style-two .swiper-button-next:after {
    font-size: 18px;
  }
}

.bounce-z {
  -webkit-animation: fa-spin 70s infinite;
          animation: fa-spin 70s infinite;
}

/***

====================================================================
    Services Section
====================================================================

***/
.services-section {
  padding: 0 0 93px;
  position: relative;
}
@media (max-width: 767.98px) {
  .services-section {
    padding: 0 0 60px;
  }
}
.services-section .sec-title .text {
  position: relative;
  top: 108px;
  left: 86px;
  margin-right: 116px;
}
@media (max-width: 1199.98px) {
  .services-section .sec-title .text {
    top: 0;
    left: 0;
    margin-right: 0;
  }
}
.services-section .service1-pattrn1 {
  background-image: url(../images/resource/service1-pattrn1.png);
  position: absolute;
  width: 312px;
  height: 427px;
  right: -20px;
  bottom: 60px;
}
@media (max-width: 1399.98px) {
  .services-section .service1-pattrn1 {
    display: none;
  }
}
.services-section .swiper-scrollbar {
  border-radius: 2px;
  height: 3px;
  width: 356px;
  bottom: 11px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  background: -webkit-gradient(linear, right top, left top, color-stop(-1.4%, rgba(217, 217, 217, 0)), to(#D0AC3B));
  background: linear-gradient(270deg, rgba(217, 217, 217, 0) -1.4%, #D0AC3B 100%);
}
.services-section .swiper-scrollbar .swiper-scrollbar-drag {
  background-color: var(--theme-color1);
  border-radius: 50%;
  height: 15px;
  max-width: 15px;
  top: -6px;
  cursor: pointer;
}
.services-section .swiper-wrapper {
  margin-bottom: 40px;
}

.service-block .inner-box {
  -webkit-box-shadow: -5px 5px 40px 0px rgba(190, 188, 188, 0.1490196078);
          box-shadow: -5px 5px 40px 0px rgba(190, 188, 188, 0.1490196078);
  position: relative;
  overflow: hidden;
}
@media (max-width: 991.98px) {
  .service-block .inner-box {
    margin-bottom: 30px;
  }
}
.service-block .inner-box:hover:after {
  border-color: var(--theme-color-light);
}
.service-block .inner-box:hover:before {
  height: 100%;
}
.service-block .inner-box:hover .image-box .bg-image {
  opacity: 1;
}
.service-block .inner-box:hover .content-box .icon {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
  color: var(--theme-color-light);
}
.service-block .inner-box:hover .content-box .title a {
  color: var(--theme-color-light);
}
.service-block .inner-box:after {
  border: 1px dashed #D0AC3B;
  content: "";
  position: absolute;
  top: 10px;
  left: 9px;
  right: 9px;
  bottom: 10px;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box:before {
  background-color: rgba(194, 167, 78, 0.85);
  content: "";
  position: absolute;
  width: 100%;
  height: 0;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .image-box {
  position: relative;
}
.service-block .inner-box .image-box .bg-image {
  width: auto;
  height: 282px;
  opacity: 0;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .image-box .bg-image-two {
  position: absolute;
  width: 205px;
  height: 95px;
  top: 10px;
  right: 9px;
  z-index: 1;
}
.service-block .inner-box .content-box {
  position: absolute;
  top: 0;
  left: 0;
  text-align: center;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  z-index: 2;
}
.service-block .inner-box .content-box .icon {
  color: var(--theme-color1);
  font-size: 81px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block .inner-box .content-box .title {
  font-weight: 500;
  margin-bottom: 0;
  margin-top: 20px;
}
.service-block .inner-box .content-box .title a {
  color: var(--theme-color-dark);
}

/***

====================================================================
    Services Section Two
====================================================================

***/
.services-section-two {
  position: relative;
  padding-top: 100px;
}
.services-section-two .bg-image-four {
  margin-top: -231px;
  padding-top: 231px;
  z-index: -1;
}
.services-section-two .leaf3-pattrn1 {
  background-image: url(../images/resource/leaf3.png);
  position: absolute;
  width: 342px;
  height: 346px;
  top: 0;
  right: 0;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .services-section-two .leaf3-pattrn1 {
    display: none;
  }
}
.services-section-two .about2-8-pattrn2 {
  background-image: url(../images/resource/about2-8.png);
  position: absolute;
  width: 354px;
  height: 427px;
  left: 0;
  bottom: 233px;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .services-section-two .about2-8-pattrn2 {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .service-block-two {
    margin-bottom: 30px;
  }
}
.service-block-two .inner-box {
  border: 1px solid rgba(30, 30, 30, 0.2);
  padding: 30px;
  position: relative;
  background-color: var(--theme-color-light);
  z-index: 1;
}
.service-block-two .inner-box .image-two img {
  width: 100%;
  opacity: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .bg-image {
  position: absolute;
  top: 0;
  right: 0;
  left: auto;
  width: 303px;
  height: 140px;
  z-index: -1;
}
.service-block-two .inner-box:hover .image-two img {
  opacity: 1;
}
.service-block-two .inner-box:hover .content-box-hover:before {
  height: 100%;
  opacity: 1;
}
.service-block-two .inner-box:hover .content-box-hover .content-box .icon,
.service-block-two .inner-box:hover .content-box-hover .content-box .title,
.service-block-two .inner-box:hover .content-box-hover .content-box .text,
.service-block-two .inner-box:hover .content-box-hover .content-box a {
  color: var(--theme-color-light);
  -webkit-transform: translateY(-30px);
          transform: translateY(-30px);
}
.service-block-two .inner-box:hover .content-box-hover .content-box .icon {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
  -webkit-transform: translateY(-28px) rotateY(360deg) !important;
          transform: translateY(-28px) rotateY(360deg) !important;
}
.service-block-two .inner-box:hover .content-box-hover .content-box .read-more {
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
  opacity: 1;
  -webkit-transform: translateY(19px);
          transform: translateY(19px);
}
.service-block-two .inner-box:hover .content-box-hover .image-box {
  margin-top: -100%;
  -webkit-transform: translateY(100%);
          transform: translateY(100%);
}
.service-block-two .inner-box .content-box-hover {
  position: absolute;
  top: 30px;
  left: 30px;
  right: 30px;
  bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  overflow: hidden;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  z-index: 1;
}
.service-block-two .inner-box .content-box-hover:before {
  background-color: rgba(194, 167, 78, 0.9);
  content: "";
  position: absolute;
  width: 100%;
  height: 60%;
  top: 0;
  left: 0;
  z-index: -1;
  opacity: 0;
  -webkit-transition: all 100ms ease;
  transition: all 100ms ease;
}
.service-block-two .inner-box .content-box-hover .content-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  height: 100%;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
  position: relative;
  padding: 15px 20px 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box-hover .content-box .icon {
  color: var(--theme-color1);
  font-size: 65px;
  font-weight: 500;
  display: block;
  line-height: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box-hover .content-box .title {
  margin-top: 19px;
  margin-bottom: 10px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box-hover .content-box .title a:hover {
  color: var(--theme-color-dark);
}
.service-block-two .inner-box .content-box-hover .content-box .text {
  margin: 0 8px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box-hover .content-box .read-more {
  background-color: var(--theme-color1);
  color: var(--theme-color1);
  font-size: 24px;
  height: 37.5px;
  width: 37.5px;
  line-height: 37px;
  border-radius: 50%;
  text-align: center;
  opacity: 0;
  margin-top: -17px;
  -webkit-transform: translateY(19px);
          transform: translateY(19px);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box-hover .content-box .read-more:hover {
  color: var(--theme-color-dark);
}
.service-block-two .inner-box .content-box-hover .image-box {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-two .inner-box .content-box-hover .image-box .image img {
  width: 100%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/***

====================================================================
    Services Section Three
====================================================================

***/
.services-section-three {
  background-color: rgba(var(--theme-color1-rgb), 0.1);
  padding: 100px 0;
  position: relative;
}
.service-block-three .inner-box {
  border: 1px solid rgba(var(--theme-color-dark-rgb), 0.1);
  border-radius: 180px;
  position: relative;
  overflow: hidden;
  padding: 99px 0 134px;
}
@media (max-width: 991.98px) {
  .service-block-three .inner-box {
    margin-bottom: 30px;
  }
}
.service-block-three .inner-box:hover:before {
  height: 100%;
}
.service-block-three .inner-box:hover .image-box .bg-image {
  opacity: 1;
  -webkit-transform: rotate(-8deg) scale(1.3);
          transform: rotate(-8deg) scale(1.3);
}
.service-block-three .inner-box:hover .content-box .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
  color: var(--theme-color-light);
  opacity: 0;
}
.service-block-three .inner-box:hover .content-box .title a {
  color: var(--theme-color-light);
}
.service-block-three .inner-box:hover .content-box .text {
  color: var(--theme-color-light);
}
.service-block-three .inner-box:before {
  background-color: rgba(var(--theme-color-dark-rgb), 0.5);
  content: "";
  position: absolute;
  width: 100%;
  height: 0;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-three .inner-box .image-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.service-block-three .inner-box .image-box .bg-image {
  width: 100%;
  height: 100%;
  opacity: 0;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-three .inner-box .content-box {
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: relative;
  z-index: 2;
}
.service-block-three .inner-box .content-box .icon {
  color: var(--theme-color1);
  font-size: 81px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-three .inner-box .content-box .title {
  font-size: 48px;
  font-weight: 500;
  margin-bottom: 14px;
  margin-top: 40px;
}
@media (max-width: 1199.98px) {
  .service-block-three .inner-box .content-box .title {
    font-size: 36px;
  }
}
.service-block-three .inner-box .content-box .title a {
  color: var(--theme-color-dark);
}
.service-block-three .inner-box .content-box .text {
  color: var(--theme-color3);
  font-size: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/***

====================================================================
    Services Section Four
====================================================================

***/
.services-section-four {
  position: relative;
  padding: 100px 0 120px;
}
.services-section-four .bg-image-four {
  max-height: 588px;
  padding-top: 231px;
  z-index: -1;
}
.services-section-four .leaf3-pattrn1 {
  background-image: url(../images/resource/leaf3.png);
  position: absolute;
  width: 342px;
  height: 346px;
  top: 0;
  right: 0;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .services-section-four .leaf3-pattrn1 {
    display: none;
  }
}
.services-section-four .about2-8-pattrn2 {
  background-image: url(../images/resource/about2-8.png);
  position: absolute;
  width: 354px;
  height: 427px;
  left: 0;
  top: 160px;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .services-section-four .about2-8-pattrn2 {
    display: none;
  }
}

/***

====================================================================
    Services Section Five
====================================================================

***/
.services-section-five {
  background-color: #fbf8f3;
  padding: 66px 0 100px;
  position: relative;
}

.service-block-four .inner-box {
  background-color: var(--theme-color-light);
  position: relative;
  overflow: hidden;
}
.service-block-four .inner-box.style-two {
  background-color: #fff7eb;
}
.service-block-four .inner-box.style-three {
  background-color: #f4ecdf;
}
.service-block-four .inner-box.style-four {
  background-color: #f2efea;
}
.service-block-four .inner-box:hover:before {
  height: 100%;
}
.service-block-four .inner-box:hover .image-box .bg-image {
  opacity: 1;
}
.service-block-four .inner-box:hover .content-box {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.service-block-four .inner-box:hover .content-box .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
  color: var(--theme-color-light);
  opacity: 0;
  width: 0;
}
.service-block-four .inner-box:hover .content-box .title a {
  color: var(--theme-color-light);
}
.service-block-four .inner-box:hover .content-box .text {
  color: var(--theme-color-light);
}
.service-block-four .inner-box:before {
  background-color: rgba(var(--theme-color-dark-rgb), 0.7);
  content: "";
  position: absolute;
  width: 100%;
  height: 0;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-four .inner-box .image-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.service-block-four .inner-box .image-box .bg-image {
  width: 100%;
  height: 100%;
  opacity: 0;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-four .inner-box .content-box {
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: relative;
  padding: 60px 42px;
  min-height: 534px;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-four .inner-box .content-box .icon {
  color: var(--theme-color1);
  font-size: 81px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  text-align: center;
}
.service-block-four .inner-box .content-box .title {
  font-size: 32px;
  font-weight: 400;
  margin-bottom: 21px;
}
.service-block-four .inner-box .content-box .title a {
  color: var(--theme-color4);
}
.service-block-four .inner-box .content-box .text {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-four .inner-box .content-box .theme-btn {
  margin-top: 30px;
}

/***

====================================================================
    Services Section Five
====================================================================

***/
.services-section-seven {
  background-color: #fff6f4;
  padding: 120px 0 60px;
  position: relative;
}
.services-section-seven:before {
  background-image: url(../images/background/service-seven-bottom-shape.png);
  background-repeat: no-repeat;
  bottom: 0;
  content: "";
  height: 76px;
  left: 0;
  position: absolute;
  right: 0;
}
.services-section-seven .object-1 {
  left: 0;
  position: absolute;
  bottom: 0px;
}
@media (max-width: 1199.98px) {
  .services-section-seven .object-1 {
    display: none;
  }
}
.services-section-seven .leaf-1 {
  position: absolute;
  right: 0;
  top: 0;
}

.service-block-five .inner-box {
  border-radius: 20px;
  margin-bottom: 30px;
  position: relative;
  text-align: center;
  z-index: 9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-five .inner-box .thumb-icon {
  display: inline-block;
  height: 142px;
  line-height: 142px;
  margin-bottom: 15px;
  overflow: hidden;
  position: relative;
  text-align: center;
  width: 148px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-block-five .inner-box .thumb-icon .bg {
  height: 98px;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  text-align: center;
  top: 20px;
  width: 98px;
  z-index: 0;
}
.service-block-five .inner-box .thumb-icon svg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-block-five .inner-box .thumb-icon svg path {
  fill: var(--theme-color-light);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-block-five .inner-box .text {
  margin-bottom: 30px;
  padding-bottom: 25px;
}
.service-block-five .inner-box:hover .thumb-icon .bg {
  -webkit-filter: brightness(10);
          filter: brightness(10);
  opacity: 1;
}
.service-block-five .inner-box:hover .thumb-icon img {
  -webkit-filter: brightness(10);
          filter: brightness(10);
}
.service-block-five .inner-box:hover .thumb-icon svg path {
  fill: var(--theme-color1);
}

/***

====================================================================
    Services Section Six
====================================================================

***/
.services-section-six {
  padding: 120px 0;
  position: relative;
}
.services-section-six.pull-up {
  padding-top: 238px;
}
.services-section-six .leaf-1 {
  left: 0;
  position: absolute;
  top: 50%;
}
@media (max-width: 1199.98px) {
  .services-section-six .leaf-1 {
    display: none;
  }
}
.services-section-six .text-bottom {
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
}
.services-section-six .text-bottom:before {
  border-top: 1px dotted #c28565;
  content: "";
  position: absolute;
  height: 100%;
  width: calc(100% - 76px);
  left: 38px;
  top: 20px;
}
.services-section-six .text-bottom .inner-text {
  background-color: #f9f3f3;
  border: 1px dotted #c28565;
  border-radius: 20px;
  display: inline-block;
  font-size: 14px;
  padding: 2px 33px 3px;
  position: relative;
  z-index: 0;
}
@media (max-width: 575.98px) {
  .services-section-six .text-bottom .inner-text {
    display: block;
  }
}
.services-section-six .text-bottom .inner-text a {
  color: #c28565;
  font-weight: 700;
  text-transform: uppercase;
  margin-left: 5px;
}
@media (max-width: 575.98px) {
  .services-section-six .text-bottom .inner-text a {
    display: block;
    margin-top: 1px;
  }
}

.service-block-six .inner-box {
  background-color: var(--theme-color-light);
  border-radius: 20px;
  margin-bottom: 30px;
  padding: 30px 50px 20px 40px;
  position: relative;
  z-index: 9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-six .inner-box .thumb-icon {
  height: 120px;
  line-height: 120px;
  margin-bottom: 15px;
  overflow: hidden;
  position: relative;
  text-align: center;
  width: 120px;
}
.service-block-six .inner-box .thumb-icon .bg {
  position: absolute;
  top: 0;
  z-index: -1;
}
.service-block-six .inner-box .shape-btn {
  position: absolute;
  right: -19px;
  top: -33px;
  width: auto;
  height: auto;
}
.service-block-six .inner-box .shape-btn svg path {
  fill: #f9f3f0 !important;
}
.service-block-six .inner-box .service-btn {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 9;
}
.service-block-six .inner-box .service-btn a.btn {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: var(--theme-color-light);
  border-radius: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-weight: 700;
  height: 64px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  width: 104px;
}
.service-block-six .inner-box .service-btn a.btn i {
  color: var(--theme-color1);
  font-size: 24px;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.service-block-six .inner-box .read-more {
  display: inline-block;
  position: absolute;
  right: 50px;
  bottom: 50px;
}
.service-block-six .inner-box .read-more i {
  font-size: 70px;
  line-height: 70px;
  color: var(--text-color);
  display: inline-block;
}
.service-block-six .inner-box:hover {
  background-color: var(--theme-color1);
}
.service-block-six .inner-box:hover .title,
.service-block-six .inner-box:hover .text {
  color: var(--theme-color-light);
}
.service-block-six .inner-box:hover .theme-btn {
  background-color: var(--theme-color-dark);
}
.service-block-six .inner-box:hover .thumb-icon .bg {
  opacity: 0.1;
}
.service-block-six .inner-box:hover .thumb-icon img {
  -webkit-filter: brightness(10);
          filter: brightness(10);
}

.service-block-seven .inner-box {
  background-color: var(--theme-color-light);
  position: relative;
  overflow: hidden;
}
.service-block-seven .inner-box.style-two {
  background-color: #fff7eb;
}
.service-block-seven .inner-box.style-three {
  background-color: #f4ecdf;
}
.service-block-seven .inner-box.style-four {
  background-color: #f2efea;
}
.service-block-seven .inner-box:hover:before {
  height: 100%;
}
.service-block-seven .inner-box:hover .image-box .bg-image {
  opacity: 1;
  -webkit-transform: rotate(-8deg) scale(1.3);
          transform: rotate(-8deg) scale(1.3);
}
.service-block-seven .inner-box:hover .content-box {
  opacity: 0;
}
.service-block-seven .inner-box:hover .content-box .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
  color: var(--theme-color-light);
  opacity: 0;
  width: 0;
}
.service-block-seven .inner-box:hover .content-box .title a {
  color: var(--theme-color-light);
}
.service-block-seven .inner-box:hover .content-box .text {
  color: var(--theme-color-light);
}
.service-block-seven .inner-box:hover .content-box-hover {
  opacity: 1;
  top: 50%;
  -webkit-transform: translateY(-115px);
          transform: translateY(-115px);
}
.service-block-seven .inner-box:before {
  background-color: rgba(var(--theme-color-dark-rgb), 0.7);
  content: "";
  position: absolute;
  width: 100%;
  height: 0;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-seven .inner-box .image-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.service-block-seven .inner-box .image-box .bg-image {
  width: 100%;
  height: 100%;
  opacity: 0;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-seven .inner-box .content-box {
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  padding: 60px 42px;
  min-height: 534px;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.service-block-seven .inner-box .content-box .icon {
  color: var(--theme-color1);
  font-size: 81px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  text-align: center;
}
.service-block-seven .inner-box .content-box .title {
  font-size: 32px;
  font-weight: 400;
  margin-bottom: 21px;
}
.service-block-seven .inner-box .content-box .title a {
  color: var(--theme-color4);
}
.service-block-seven .inner-box .content-box .theme-btn {
  margin-top: 30px;
}
.service-block-seven .inner-box .content-box-hover {
  position: absolute;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 0 45px;
  z-index: 2;
  top: 0;
  opacity: 0;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.service-block-seven .inner-box .content-box-hover .title {
  font-size: 32px;
  font-weight: 400;
  margin-bottom: 21px;
  color: #fff;
}
.service-block-seven .inner-box .content-box-hover .title a {
  color: #fff;
}
.service-block-seven .inner-box .content-box-hover .text {
  color: #fff;
}
.service-block-seven .inner-box .content-box-hover .theme-btn {
  margin-top: 30px;
}

/***

==================================================================
    Video Section
==================================================================

***/
.video-section {
  position: relative;
  padding: 182px 0 173px;
}
@media (max-width: 767.98px) {
  .video-section {
    padding: 100px 0 140px;
  }
}
@media (max-width: 991.98px) {
  .video-section .sec-title {
    text-align: center;
  }
}
.video-section .sec-title h2 br {
  display: none;
}
@media (max-width: 991.98px) {
  .video-section .sec-title h2 br {
    display: block;
  }
}
@media (max-width: 1399.98px) {
  .video-section .sec-title h2 {
    font-size: 50px;
  }
}
@media (max-width: 1199.98px) {
  .video-section .sec-title h2 {
    font-size: 45px;
  }
}
@media (max-width: 575.98px) {
  .video-section .sec-title h2 {
    font-size: 33px;
  }
}
.video-section .sec-title .pricing-btn {
  padding: 15px 32px 15px 32px;
  margin-top: 39px;
}
@media (max-width: 575.98px) {
  .video-section .sec-title .pricing-btn {
    padding: 13px 25px;
    margin-top: 26px;
  }
}
.video-section .outer-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  top: 36px;
  position: relative;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 991.98px) {
  .video-section .outer-box {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    margin-top: 100px;
    top: 0;
  }
}
@media (max-width: 575.98px) {
  .video-section .outer-box {
    margin-top: 70px;
  }
}
.video-section .outer-box h4 {
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 51px;
  position: relative;
}
.video-section .outer-box h4:before {
  background-image: url(../images/icons/icon-arrow1.png);
  content: "";
  position: absolute;
  bottom: -35px;
  right: -28px;
  width: 73px;
  height: 42px;
}
.video-section .outer-box .play-now {
  border-radius: 50%;
  position: relative;
  display: inline-block;
}
.video-section .outer-box .play-now:after {
  content: "";
  border: 1px dashed #D0AC3B;
  border-radius: 50%;
  bottom: 0;
  top: -8px;
  left: -8px;
  right: 0;
  position: absolute;
  width: 100px;
  height: 100px;
}
.video-section .outer-box .play-now::before {
  display: none;
}
.video-section .outer-box .play-now:hover {
  background-color: var(--theme-color1);
  border-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.video-section .outer-box .play-now:hover .icon {
  color: var(--theme-color-light);
}
.video-section .outer-box .play-now .icon {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
  font-size: 24px;
  height: 84px;
  width: 84px;
  line-height: 84px;
}

/***

==================================================================
    Video Section Two
==================================================================

***/
.video-section-two {
  position: relative;
  padding: 99px 0 252px;
}
@media (max-width: 767.98px) {
  .video-section-two {
    padding: 100px 0;
  }
}
.video-section-two:before {
  background-color: rgba(var(--theme-color1-rgb), 0.9);
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.video-section-two .content {
  text-align: center;
  position: relative;
  z-index: 1;
}
.video-section-two .content .play-now {
  border-radius: 50%;
  position: relative;
  display: inline-block;
}
.video-section-two .content .play-now::before {
  display: none;
}
.video-section-two .content .play-now:hover .icon {
  color: var(--theme-color1);
}
.video-section-two .content .play-now .icon {
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
  font-size: 24px;
  height: 90px;
  width: 90px;
  line-height: 90px;
}
.video-section-two .content h3 {
  color: var(--theme-color-light);
  font-weight: 600;
  line-height: 48px;
  margin-top: 15px;
}
@media (max-width: 991.98px) {
  .video-section-two .content h3 br {
    display: none;
  }
}

/*** 

====================================================================
    Contact Section
====================================================================

***/
.contact-section {
  background-color: #0f1b24;
  position: relative;
  padding: 70px 0 30px;
  margin: 100px 0;
  z-index: 2;
}
@media (max-width: 1199.98px) {
  .contact-section {
    margin: 0;
  }
}
.contact-section:before {
  background: -webkit-gradient(linear, right top, left top, from(#141215), color-stop(rgba(20, 18, 21, 0.7)), color-stop(transparent), to(transparent));
  background: linear-gradient(to left, #141215, rgba(20, 18, 21, 0.7), transparent, transparent);
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
}
.contact-section .bg-image {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  pointer-events: none;
  z-index: -2;
}
.contact-section .curved-shape-top {
  position: absolute;
  top: 0;
  left: 0;
  height: 69px;
  width: 100%;
  pointer-events: none;
  z-index: 2;
}
.contact-section .curved-shape-bottom {
  position: absolute;
  bottom: 0;
  top: auto;
  left: 0;
  height: 69px;
  width: 100%;
  pointer-events: none;
  z-index: 2;
}
.contact-section .form-column {
  position: relative;
}
.contact-section .form-column .sec-title {
  margin-bottom: 39px;
}
.contact-section .form-column .sec-title h3 {
  color: var(--theme-color-light);
}
.contact-section .form-column .sec-title .text {
  color: var(--theme-color-light);
}
@media (max-width: 767.98px) {
  .contact-section .form-column .sec-title .text br {
    display: none;
  }
}
.contact-section .form-column .inner-column {
  position: relative;
}
.contact-section .form-column .contact-block {
  position: absolute;
  top: calc(50% + 2px);
  left: 0;
  -webkit-transform: translate(-100%, -50%);
          transform: translate(-100%, -50%);
}
.contact-section .form-column .contact-block .inner-box {
  position: relative;
}
.contact-section .contact-form .form-control, .contact-section .contact-form .input-text {
  color: #fff;
}
.contact-section .contact-form .form-control::-webkit-input-placeholder, .contact-section .contact-form .input-text::-webkit-input-placeholder {
  color: #fff;
  opacity: 1;
}
.contact-section .contact-form .form-control::-moz-placeholder, .contact-section .contact-form .input-text::-moz-placeholder {
  color: #fff;
  opacity: 1;
}
.contact-section .contact-form .form-control:-ms-input-placeholder, .contact-section .contact-form .input-text:-ms-input-placeholder {
  color: #fff;
  opacity: 1;
}
.contact-section .contact-form .form-control::-ms-input-placeholder, .contact-section .contact-form .input-text::-ms-input-placeholder {
  color: #fff;
  opacity: 1;
}
.contact-section .contact-form .form-control::placeholder, .contact-section .contact-form .input-text::placeholder {
  color: #fff;
  opacity: 1;
}
.contact-section .contact-form .form-control:-ms-input-placeholder, .contact-section .contact-form .input-text:-ms-input-placeholder {
  color: #fff;
}
.contact-section .contact-form .form-control::-ms-input-placeholder, .contact-section .contact-form .input-text::-ms-input-placeholder {
  color: #fff;
}

.contact-form {
  position: relative;
  padding: 56px 0 100px;
}
@media (max-width: 1199.98px) {
  .contact-form {
    padding: 90px 40px 140px;
  }
}
@media (max-width: 575.98px) {
  .contact-form {
    padding: 90px 10px 140px;
  }
}
.contact-form .form-group {
  position: relative;
  margin-bottom: 13px;
}
.contact-form .form-group:last-child {
  margin-bottom: 0;
}
.contact-form .select2-container--default .select2-selection--single,
.contact-form input:not([type=submit]),
.contact-form textarea,
.contact-form select {
  position: relative;
  display: block;
  height: 55px;
  width: 100%;
  padding: 10px 10px 3px 0;
  font-size: var(--body-font-size);
  color: var(--theme-color-light);
  line-height: 29px;
  font-weight: 400;
  font-size: 14px;
  background-color: transparent;
  border-bottom: 1px solid rgba(235, 235, 235, 0.2);
  border-radius: 0;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-form textarea {
  height: auto;
  min-height: 127px;
  padding-top: 20px;
}
.contact-form ::-webkit-input-placeholder {
  color: #fff;
}
.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  border-color: var(--theme-color-light);
}
.contact-form button {
  margin-top: 17px;
}

.contact-details .form-control::-webkit-input-placeholder {
  color: var(--text-color);
}

.contact-details .form-control::-moz-placeholder {
  color: var(--text-color);
}

.contact-details .form-control:-ms-input-placeholder {
  color: var(--text-color);
}

.contact-details .form-control::-ms-input-placeholder {
  color: var(--text-color);
}

.contact-details .form-control::placeholder {
  color: var(--text-color);
}

/*** 

====================================================================
    Contact Section Two
====================================================================

***/
.contact-section-two {
  background-color: #f9f3f0;
  padding: 0 0 120px;
  position: relative;
  z-index: 2;
}
@media (max-width: 991.98px) {
  .contact-section-two {
    padding: 0 0 60px;
  }
}
@media (max-width: 767.98px) {
  .contact-section-two {
    padding: 0;
  }
}
.contact-section-two .bg-image2 {
  width: 633px;
  left: 0;
  height: 100%;
  top: 0;
}
@media (max-width: 1399.98px) {
  .contact-section-two .bg-image2 {
    width: 420px;
  }
}
@media (max-width: 1199.98px) {
  .contact-section-two .bg-image2 {
    display: none;
  }
}
.contact-section-two .content-column .inner-column {
  position: relative;
  padding: 180px 0 0 70px;
}
@media (max-width: 991.98px) {
  .contact-section-two .content-column .inner-column {
    padding: 100px 0 0 70px;
    padding-left: 0;
  }
}
.contact-section-two .content-column .inner-column .sec-title {
  margin-bottom: 20px;
}
.contact-section-two .content-column .inner-column .sec-title h2 {
  font-size: 50px;
  font-weight: 700;
  line-height: 1.2em;
}
.contact-section-two .content-column .inner-column .sec-title .text {
  margin-top: 17px;
}
.contact-section-two .form-column {
  position: relative;
}
.contact-section-two .form-column .inner-column {
  padding: 120px 0 0;
  margin-right: -24px;
}
@media (max-width: 991.98px) {
  .contact-section-two .form-column .inner-column {
    padding: 60px 0;
    margin-right: 0;
  }
}
@media (max-width: 767.98px) {
  .contact-section-two .form-column .inner-column {
    padding: 60px 0 90px;
  }
}
.contact-section-two.style-two .bg-image {
  -webkit-transform: rotateY(-180deg);
          transform: rotateY(-180deg);
}
.contact-section-two.style-two .bg-image2 {
  right: 0;
  left: auto;
}
@media (max-width: 991.98px) {
  .contact-section-two.style-two .bg-image2 {
    display: none;
  }
}
.contact-section-two.style-two .faq1-shape-1 {
  /* background-image: url(../images/icons/faq1-shape-1.png); */
  position: absolute;
  width: 228px;
  height: 190px;
  left: 84px;
  bottom: 88px;
}
@media (max-width: 1399.98px) {
  .contact-section-two.style-two .faq1-shape-1 {
    display: none;
  }
}

.contact-form-two {
  border-radius: 10px;
  position: relative;
  background-color: var(--theme-color1);
  padding: 80px 85px 100px;
}
@media (max-width: 1399.98px) {
  .contact-form-two {
    padding: 80px 30px 100px;
  }
}
@media (max-width: 575.98px) {
  .contact-form-two {
    padding: 30px 20px;
  }
}
.contact-form-two .bg-pattern-1 {
  background-image: url(../images/icons/shape-bg2.png);
}
.contact-form-two .title {
  color: var(--theme-color-light);
  font-weight: 500;
  margin-bottom: 43px;
  text-align: center;
}
@media (max-width: 575.98px) {
  .contact-form-two .title {
    font-size: 24px;
    margin-bottom: 20px;
  }
}
.contact-form-two .form-group {
  position: relative;
  margin-bottom: 26px;
}
.contact-form-two .form-group:last-child {
  margin-bottom: 0;
}
.contact-form-two .select2-container--default .select2-selection--single,
.contact-form-two input:not([type=submit]),
.contact-form-two textarea,
.contact-form-two select {
  position: relative;
  display: block;
  height: 60px;
  width: 100%;
  padding: 15px 21px;
  font-size: var(--body-font-size);
  color: var(--theme-color-dark);
  line-height: 32px;
  font-weight: 400;
  background-color: var(--theme-color-light);
  border: 1px solid transparent;
  border-radius: 3px;
  margin-bottom: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.contact-form-two ::-webkit-input-placeholder {
  color: #393939;
}
.contact-form-two input:focus,
.contact-form-two select:focus,
.contact-form-two textarea:focus {
  border-color: var(--theme-color-dark);
  -webkit-box-shadow: none;
          box-shadow: none;
}
.contact-form-two textarea {
  height: 79px;
  resize: none;
}
.contact-form-two .theme-btn {
  background-color: var(--theme-color-dark);
  width: 100%;
}
.contact-form-two label {
  font-size: 16px;
  line-height: 20px;
  color: #ffffff;
  font-weight: 400;
  display: block;
  letter-spacing: 1px;
  margin-bottom: 15px;
}
.contact-form-two label.error {
  display: block;
  font-weight: 400;
  font-size: 13px;
  text-transform: capitalize;
  line-height: 24px;
  color: #ff0000;
  margin-bottom: 0;
}

/***==================================
    Range Slider One
==================================***/
.range-slider-one {
  position: relative;
  margin-bottom: 5px;
}
.range-slider-one .range-amount {
  position: absolute !important;
  right: 0;
  top: -35px;
  background-color: transparent !important;
  text-align: right;
  border: 0 !important;
  padding: 0 !important;
  max-width: 75px;
  color: #ffffff !important;
  width: auto !important;
  height: auto !important;
}
.range-slider-one .ui-widget.ui-widget-content {
  height: 10px;
  border: none;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.3);
}
.range-slider-one .ui-slider .ui-slider-range {
  top: 0px;
  height: 10px;
  background: var(--theme-color-light);
}
.range-slider-one .ui-state-default {
  top: -5px;
  width: 20px;
  height: 20px;
  background: var(--theme-color-dark);
  cursor: pointer;
  border: 3px solid var(--theme-color-dark);
  border-radius: 20px;
}
.range-slider-one .ui-widget-content .ui-state-default {
  top: -5px;
  width: 20px;
  height: 20px;
  background: var(--theme-color-dark);
  cursor: pointer;
  border: 3px solid var(--theme-color-dark);
  border-radius: 20px;
}
.range-slider-one .ui-slider-horizontal .ui-slider-handle:nth-child(2) {
  margin-left: 0 !important;
}
.range-slider-one .ui-slider-horizontal .ui-slider-handle:nth-child(3) {
  margin-left: -20px !important;
}

/*** 

====================================================================
    FAQ's Section
====================================================================

***/
.faq-section {
  position: relative;
  padding: 120px 0 70px;
}
.faq-section .icon-plane-4 {
  position: absolute;
  bottom: -140px;
  left: 20px;
}
.faq-section .faq-column {
  margin-bottom: 50px;
}
.faq-section .faq-column .inner-column {
  position: relative;
  padding: 60px 0;
  padding-left: 95px;
}
@media (max-width: 1199.98px) {
  .faq-section .faq-column .inner-column {
    padding-left: 40px;
  }
}
@media (max-width: 575.98px) {
  .faq-section .faq-column .inner-column {
    padding: 0;
  }
}
.faq-section .faq-column .inner-column:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  width: 315px;
  height: 100%;
  border-top: 5px solid var(--theme-color2);
  border-bottom: 5px solid var(--theme-color2);
  border-left: 5px solid var(--theme-color2);
}
@media (max-width: 575.98px) {
  .faq-section .faq-column .inner-column:before {
    display: none;
  }
}
.faq-section .faq-column .inner-column .sec-title {
  margin-bottom: 20px;
}
.faq-section .faq-column .inner-column .sec-title .sub-title {
  margin-bottom: 5px;
}
.faq-section .faq-column .accordion-box {
  max-width: 370px;
}
.faq-section .image-column {
  margin-bottom: 50px;
}
.faq-section .image-column .inner-column {
  padding-top: 35px;
  padding-left: 30px;
}
@media (max-width: 991.98px) {
  .faq-section .image-column .inner-column {
    padding: 0;
    text-align: center;
  }
}
@media (max-width: 575.98px) {
  .faq-section .image-column .inner-column {
    margin-bottom: 40px;
  }
}
.faq-section .image-column .inner-column .image-box {
  position: relative;
}
.faq-section .image-column .inner-column .image-box .rating-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 229px;
  height: 100px;
  border: 3px solid var(--theme-color1);
  background-color: var(--theme-color-light);
  text-align: center;
  -webkit-transform: translate(38px, 46px);
          transform: translate(38px, 46px);
}
@media (max-width: 991.98px) {
  .faq-section .image-column .inner-column .image-box .rating-box {
    left: 50px;
  }
}
@media (max-width: 767.98px) {
  .faq-section .image-column .inner-column .image-box .rating-box {
    position: relative;
    left: 0;
    width: 100%;
    -webkit-transform: none;
            transform: none;
    margin-top: 30px;
  }
}
.faq-section .image-column .inner-column .image-box .rating-box .icon {
  position: absolute;
  width: 51px;
  height: 51px;
  line-height: 51px;
  border-radius: 50%;
  background: var(--theme-color1);
  color: var(--theme-color-light);
  font-size: 24px;
  text-align: center;
  -webkit-transform: translate(-30px, -31px);
          transform: translate(-30px, -31px);
}
.faq-section .image-column .inner-column .image-box .rating-box .rating {
  padding-top: 18px;
}
.faq-section .image-column .inner-column .image-box .rating-box .rating li {
  display: inline-block;
  color: #f9b524;
  font-size: 12px;
  margin: 0 2px;
}
.faq-section .image-column .inner-column .image-box .rating-box .reviews {
  margin-bottom: 0;
  font-size: 18px;
  font-weight: 700;
}

.accordion-box.style-three {
  position: relative;
  max-width: 485px;
}
.accordion-box.style-three .block {
  position: relative;
  background-color: transparent;
}
.accordion-box.style-three .block .acc-btn {
  position: relative;
  font-size: 24px;
  line-height: 24px;
  color: var(--theme-color-dark);
  font-weight: 700;
  cursor: pointer;
  padding: 24px 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
@media (max-width: 575.98px) {
  .accordion-box.style-three .block .acc-btn {
    padding: 15px 14px 15px 0;
  }
}
.accordion-box.style-three .block .acc-btn .icon {
  position: absolute;
  top: 15px;
  left: 0;
  line-height: 28px;
  color: var(--theme-color1);
  font-size: 24px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.accordion-box.style-three .block .acc-btn .arrow {
  position: absolute;
  right: 0;
  top: 8px;
  font-size: 14px;
  line-height: 56px;
  font-weight: 800;
  color: var(--theme-color-dark);
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.accordion-box.style-three .block .acc-btn.active {
  color: var(--theme-color1);
}
.accordion-box.style-three .block .acc-btn.active .arrow {
  font-size: 14px;
  color: var(--theme-color1);
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}
.accordion-box.style-three .block .acc-btn.active .arrow:before {
  content: "\f063";
}
.accordion-box.style-three .block .acc-btn.active .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.accordion-box.style-three .block .acc-content {
  position: relative;
  display: none;
}
.accordion-box.style-three .block .acc-content .content {
  position: relative;
  padding: 21px 30px 15px 0;
}
.accordion-box.style-three .block .acc-content .content .text {
  display: block;
  font-size: 16px;
  margin-bottom: 0;
}
.accordion-box.style-three .block .acc-content.current {
  display: block;
}

/*** 

====================================================================
    Fun Fact Section
====================================================================

***/
.fun-fact-section {
  position: relative;
  padding: 90px 0 120px;
}
.fun-fact-section .bg-image-two {
  width: 100%;
  height: 69px;
  bottom: 0;
  left: 0;
  top: auto;
}
.fun-fact-section .feature1-2 {
  background-image: url(../images/resource/feature1-2.png);
  position: absolute;
  width: 285px;
  height: 344px;
  bottom: 37px;
  right: 0;
}
@media (max-width: 767.98px) {
  .fun-fact-section .feature1-2 {
    display: none;
  }
}

.counter-block {
  position: relative;
}
@media (max-width: 991.98px) {
  .counter-block {
    margin-bottom: 40px;
  }
}
.counter-block .inner {
  position: relative;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.counter-block .inner:hover .icon img {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.counter-block .inner:hover .counter-title {
  opacity: 1;
}
.counter-block .icon {
  background-color: var(--theme-color1);
  border-radius: 50%;
  position: relative;
  height: 100px;
  width: 100px;
  line-height: 96px;
  margin-bottom: 35px;
}
.counter-block .icon img {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.counter-block .count-box {
  position: relative;
  display: block;
  font-size: 60px;
  font-weight: 600;
}
.counter-block .count-box .count-text {
  color: var(--headings-color);
  font-size: 80px;
  font-weight: 500;
  font-family: var(--title-font);
}
.counter-block .counter-title {
  display: block;
  font-size: 22px;
  color: var(--headings-color);
  font-weight: 500;
  margin-bottom: 0;
  margin-top: 43px;
}

/*** 

====================================================================
    Fun Fact Section Two
====================================================================

***/
.fun-fact-section-two {
  background-color: var(--theme-color5);
  position: relative;
  padding: 90px 0 120px;
}
.fun-fact-section-two.pull-down {
  margin-bottom: -96px;
}
.fun-fact-section-two .outer-box {
  background-color: #F9F3F0;
  border: 6px solid var(--theme-color-light);
  padding: 60px 60px 50px;
  position: relative;
  margin-bottom: -100px;
  z-index: 1;
}
@media (max-width: 991.98px) {
  .fun-fact-section-two .outer-box {
    padding: 60px 60px 10px;
  }
}
@media (max-width: 575.98px) {
  .fun-fact-section-two .outer-box {
    padding: 60px 20px 10px;
  }
}

.counter-block-two {
  position: relative;
}
@media (max-width: 991.98px) {
  .counter-block-two {
    margin-bottom: 40px;
  }
}
.counter-block-two .inner {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.counter-block-two .inner:hover .icon img {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.counter-block-two .inner:hover .counter-title {
  opacity: 1;
}
.counter-block-two .icon {
  font-size: 64px;
  position: relative;
  line-height: 1;
  margin-bottom: 35px;
  text-align: center;
  min-width: 60px;
}
@media (max-width: 575.98px) {
  .counter-block-two .icon {
    max-width: 60px;
  }
}
.counter-block-two .icon img {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.counter-block-two .inner-content {
  padding-left: 17px;
}
.counter-block-two .count-box {
  position: relative;
  display: block;
  color: var(--headings-color);
  font-size: 64px;
  font-weight: 400;
  font-family: var(--text-font);
}
@media (max-width: 1199.98px) {
  .counter-block-two .count-box {
    font-size: 42px;
  }
}
.counter-block-two .count-box .count-text {
  color: var(--headings-color);
  font-size: 64px;
  font-weight: 600;
  font-family: var(--text-font);
}
@media (max-width: 1199.98px) {
  .counter-block-two .count-box .count-text {
    font-size: 42px;
  }
}
.counter-block-two .counter-title {
  display: block;
  font-size: 22px;
  color: var(--headings-color);
  font-weight: 500;
  margin-bottom: 0;
  margin-top: 25px;
}

/*** 

====================================================================
	Testimonial Section
====================================================================

***/
.testimonial-section {
  padding: 40px 0 200px;
  position: relative;
}
@media (max-width: 991.98px) {
  .testimonial-section {
    padding: 50px 0 100px;
  }
}
.testimonial-section.style-two {
  padding: 130px 0 300px;
  position: relative;
}
@media (max-width: 991.98px) {
  .testimonial-section.style-two {
    padding: 100px 0;
  }
}
.testimonial-section.style-three {
  padding: 200px 0 185px;
  position: relative;
}
.testimonial-section.style-three .bg-image {
  pointer-events: none;
  z-index: -2;
}
.testimonial-section.style-three .instagram1-7 {
  background-image: url("../images/resource/instagram1-7.png");
  background-size: cover;
  position: absolute;
  width: 166px;
  height: 298px;
  top: 270px;
  left: 21px;
}
@media (max-width: 991.98px) {
  .testimonial-section.style-three .instagram1-7 {
    display: none;
  }
}
.testimonial-section.style-three .testimonial-pattrn1-2 {
  background-image: url(../images/resource/testimonial-pattrn1-2.png);
  background-size: cover;
  position: absolute;
  width: 351px;
  height: 293px;
  top: 320px;
  right: 71px;
}
@media (max-width: 1499.98px) {
  .testimonial-section.style-three .testimonial-pattrn1-2 {
    display: none;
  }
}
.testimonial-section .testimonial-pattrn1-1 {
  background-image: url(../images/resource/testimonial-pattrn1-1.png);
  position: absolute;
  width: 205px;
  height: 263px;
  left: 54px;
  top: 54px;
}
@media (max-width: 1399.98px) {
  .testimonial-section .testimonial-pattrn1-1 {
    display: none;
  }
}
.testimonial-section .default-dots .owl-dots {
  left: 0;
  position: absolute;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  bottom: -44px;
}
.testimonial-section .default-dots .owl-dots .owl-dot {
  height: 12px;
  width: 12px;
  margin: 0 8.5px;
  background-color: transparent;
  border: 1px solid #707070;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-section .default-dots .owl-dots .owl-dot.active {
  width: 15px;
  height: 15px;
  background-color: var(--theme-color1);
  border-color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.testimonial-section .carousel-outer {
  position: relative;
}
.testimonial-section .carousel-outer .image-box .image {
  border-radius: 50%;
  margin-bottom: 0;
  position: absolute;
}
.testimonial-section .carousel-outer .image-box .image img {
  border-radius: 50%;
}
.testimonial-section .carousel-outer .image-box .client1 {
  top: -128px;
  left: -153px;
}
@media (max-width: 991.98px) {
  .testimonial-section .carousel-outer .image-box .client1 {
    display: none;
  }
}
.testimonial-section .carousel-outer .image-box .client2 {
  bottom: 26px;
  left: -310px;
}
@media (max-width: 1399.98px) {
  .testimonial-section .carousel-outer .image-box .client2 {
    left: -160px;
  }
}
@media (max-width: 991.98px) {
  .testimonial-section .carousel-outer .image-box .client2 {
    display: none;
  }
}
.testimonial-section .carousel-outer .image-box .client3 {
  bottom: -181px;
  left: -118px;
}
@media (max-width: 991.98px) {
  .testimonial-section .carousel-outer .image-box .client3 {
    display: none;
  }
}
.testimonial-section .carousel-outer .image-box .client4 {
  bottom: -120px;
  right: -210px;
}
@media (max-width: 1399.98px) {
  .testimonial-section .carousel-outer .image-box .client4 {
    right: -130px;
  }
}
@media (max-width: 991.98px) {
  .testimonial-section .carousel-outer .image-box .client4 {
    display: none;
  }
}
.testimonial-section .carousel-outer .image-box .client5 {
  top: -98px;
  right: -361px;
}
@media (max-width: 1399.98px) {
  .testimonial-section .carousel-outer .image-box .client5 {
    right: -150px;
  }
}
@media (max-width: 991.98px) {
  .testimonial-section .carousel-outer .image-box .client5 {
    display: none;
  }
}

.testimonial-block .inner-box .rating {
  margin-bottom: 25px;
}
.testimonial-block .inner-box .rating .icon {
  color: var(--theme-color1);
  font-size: 25px;
  margin-right: 4px;
}
@media (max-width: 575.98px) {
  .testimonial-block .inner-box .rating .icon {
    font-size: 22px;
  }
}
.testimonial-block .inner-box .rating .icon:last-child {
  margin-right: 0;
}
.testimonial-block .inner-box .text {
  font-size: 30px;
  font-weight: 500;
  font-style: italic;
  font-family: var(--title-font);
  line-height: 36.33px;
}
@media (max-width: 575.98px) {
  .testimonial-block .inner-box .text {
    font-size: 22px;
  }
}
.testimonial-block .inner-box .info-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-top: 30px;
}
.testimonial-block .inner-box .info-box .name {
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 6px;
}
@media (max-width: 575.98px) {
  .testimonial-block .inner-box .info-box .name {
    font-size: 25px;
  }
}
.testimonial-block .inner-box .info-box .designation {
  display: block;
  margin-bottom: -5px;
}
@media (max-width: 575.98px) {
  .testimonial-block .inner-box .info-box .designation {
    font-size: 15px;
  }
}

/*** 

====================================================================
    Testimonial Style
====================================================================

***/
.testimonial-style-two .instagram1-7 {
  background-image: url(../images/resource/instagram1-7.png);
  position: absolute;
  width: 201px;
  height: 360px;
  bottom: 0;
  left: 50px;
}
@media (max-width: 1399.98px) {
  .testimonial-style-two .instagram1-7 {
    display: none;
  }
}
.testimonial-style-two .testimonial-pattrn1-2 {
  background-image: url(../images/resource/testimonial-pattrn1-2.png);
  position: absolute;
  width: 472px;
  height: 401px;
  bottom: 80px;
  right: 0;
}
@media (max-width: 1399.98px) {
  .testimonial-style-two .testimonial-pattrn1-2 {
    display: none;
  }
}

/*** 

====================================================================
    Testimonial Style Three
====================================================================

***/
.testimonial-section-three {
  background-image: url(../images/background/testimonial-bg.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}
.testimonial-section-three .image-1 {
  left: 50px;
  max-width: 320px;
  position: absolute;
  top: 50px;
}
@media (max-width: 767.98px) {
  .testimonial-section-three .image-1 {
    display: none;
  }
}

.testimonial-block-three {
  position: relative;
  margin-bottom: 30px;
}
.testimonial-block-three .inner-box {
  background-color: var(--theme-color-light);
  border: 1px solid #F4E8E1;
  border-radius: 10px;
  overflow: hidden;
  padding: 38px 30px 30px 180px;
  position: relative;
}
@media (max-width: 991.98px) {
  .testimonial-block-three .inner-box {
    padding: 38px 30px 30px;
  }
}
.testimonial-block-three .inner-box .img-1 {
  left: 0;
  position: absolute;
  top: 0;
  width: auto !important;
}
.testimonial-block-three .inner-box .img-2 {
  bottom: 0;
  position: absolute;
  right: 0;
  width: auto !important;
}
.testimonial-block-three .inner-box .icon {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-radius: 12px;
  bottom: 100px;
  color: transparent;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 100px;
  height: 21px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  left: 80px;
  position: absolute;
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
  top: auto;
  width: 21px;
  -webkit-text-fill-color: white;
  -webkit-text-stroke: 3px #F9F3F0;
}
.testimonial-block-three .inner-box:hover .thumb img {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.testimonial-block-three .thumb {
  height: 100px;
  left: 40px;
  position: absolute;
  top: 40px;
  width: 100px;
}
@media (max-width: 991.98px) {
  .testimonial-block-three .thumb {
    left: 0;
    margin-bottom: 30px;
    position: relative;
    top: 0;
  }
}
.testimonial-block-three .thumb img {
  border-radius: 20px;
  -webkit-transition: all 400ms ease;
  transition: all 400ms ease;
}
.testimonial-block-three .info-box {
  margin-bottom: 20px;
  position: relative;
  text-align: left;
  z-index: 2;
}
.testimonial-block-three .info-box .text {
  color: #6E6E6E;
  font-family: "Cormorant";
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  margin-bottom: 15px;
}
.testimonial-block-three .info-box .name {
  margin-bottom: 0px;
}
.testimonial-block-three .info-box .designation {
  color: #D0AC3B;
  display: block;
  margin-bottom: 8px;
}
.testimonial-block-three .info-box .rating {
  color: #D0AC3B;
  font-size: var(--body-font-size);
  line-height: 20px;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}
.testimonial-block-three .info-box .rating i {
  margin-right: 4px;
}

/***

====================================================================
    Process Section
====================================================================

***/
.process-section {
  position: relative;
  padding: 120px 0 50px;
}
.process-section .bg:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #232331;
  opacity: 0.95;
}
.process-section .overlay-3 {
  position: absolute;
  bottom: 0;
  left: 0;
}
.process-section .icon-plane-9 {
  position: absolute;
  top: 40%;
  left: -45px;
  z-index: 3;
}
@media only screen and (max-width: 1699px) {
  .process-section .icon-plane-9 {
    display: none;
  }
}
.process-section .float-image {
  position: absolute;
  top: 0;
  right: 0;
}
@media only screen and (max-width: 1699px) {
  .process-section .float-image {
    max-width: 30%;
  }
}
@media (max-width: 1399.98px) {
  .process-section .float-image {
    display: none;
  }
}
.process-section .sec-title {
  margin-bottom: 80px;
}

.process-block {
  margin-bottom: 70px;
}
.process-block:last-child .inner-box:before {
  display: none;
}
.process-block:nth-child(2) .inner-box:before {
  -webkit-transform: scaleY(-1);
          transform: scaleY(-1);
}
.process-block .inner-box {
  position: relative;
  max-width: 250px;
  margin: 0 auto;
  text-align: center;
}
.process-block .inner-box:before {
  content: "";
  position: absolute;
  top: 40px;
  right: -140px;
  width: 137px;
  height: 18px;
}
.process-block .inner-box:hover .icon {
  -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
  background-color: var(--theme-color1) !important;
  color: var(--theme-color-light) !important;
}
.process-block .inner-box:hover .count {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.process-block .inner-box .content {
  position: relative;
  padding: 115px 35px 75px;
  border-radius: 200px;
  overflow: hidden;
  background-color: #050c11;
}
.process-block .inner-box .icon-box {
  position: absolute;
  top: -20px;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 3;
}
.process-block .inner-box .icon-box:before {
  content: "";
  position: absolute;
  top: -15px;
  right: 70px;
  width: 70px;
  height: 117px;
}
.process-block .inner-box .icon-box .icon {
  position: relative;
  display: inline-block;
  width: 91px;
  height: 91px;
  line-height: 91px;
  font-size: 45px;
  border-radius: 50%;
  color: var(--theme-color1);
  background-color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.process-block .inner-box .title {
  color: var(--theme-color-light);
  margin-bottom: 5px;
}
.process-block .inner-box .title a:hover {
  color: var(--theme-color1);
}
.process-block .inner-box .text {
  color: var(--theme-color-light);
  margin-bottom: 35px;
}
.process-block .inner-box .count {
  position: relative;
  width: 72px;
  height: 72px;
  line-height: 72px;
  border-radius: 50%;
  margin: 0 auto;
  color: var(--theme-color-light);
  background-color: #0b1720;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.process-block .inner-box .icon-shapes {
  position: absolute;
  bottom: 0;
  right: 0;
}

/***

====================================================================
    Product Section
====================================================================

***/
.product-section {
  padding: 140px 0 100px;
  position: relative;
}
@media (max-width: 991.98px) {
  .product-section {
    padding: 100px 0 0;
  }
}
.product-section.style-two {
  padding: 130px 0 100px;
}
.product-section.style-two .testimonial-pattrn1-1 {
  top: 125px;
}
@media (max-width: 767.98px) {
  .product-section .sec-title .text br {
    display: none;
  }
}
.product-section .testimonial-pattrn1-1 {
  /* background-image: url(../images/resource/testimonial-pattrn1-1.png); */
  position: absolute;
  width: 205px;
  height: 263px;
  top: 35px;
  right: 48px;
  pointer-events: none;
}
@media (max-width: 991.98px) {
  .product-section .testimonial-pattrn1-1 {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .product-block.home-style {
    margin-bottom: 40px;
  }
}
.product-block.home-style .inner-box {
  border: none;
}
.product-block.home-style .inner-box:hover {
  border: none;
  -webkit-box-shadow: none;
          box-shadow: none;
}
.product-block.home-style .inner-box:hover .image-box:before {
  height: 100%;
}
.product-block.home-style .inner-box:hover .image-box .image {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.product-block.home-style .inner-box:hover .image-box .icon-box {
  bottom: 23px;
}
.product-block.home-style .inner-box:hover .image-box .icon-box .icon {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.product-block.home-style .inner-box .image-box {
  overflow: hidden;
  position: relative;
}
.product-block.home-style .inner-box .image-box:before {
  background: -webkit-gradient(linear, left bottom, left top, color-stop(28.77%, rgba(0, 0, 0, 0.4)), to(rgba(0, 0, 0, 0)));
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 28.77%, rgba(0, 0, 0, 0) 100%);
  bottom: 0;
  content: "";
  height: 0;
  left: 0;
  position: absolute;
  pointer-events: none;
  width: 100%;
  z-index: 1;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.product-block.home-style .inner-box .image-box .image {
  width: 100%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.product-block.home-style .inner-box .image-box .image a {
  width: 100%;
}
.product-block.home-style .inner-box .image-box .image a img {
  width: 100%;
}
.product-block.home-style .inner-box .image-box .inner {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  position: relative;
}
.product-block.home-style .inner-box .image-box .icon-box {
  bottom: -40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  left: 0;
  margin: 0 auto;
  position: absolute;
  right: 0;
  top: auto;
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.product-block.home-style .inner-box .image-box .icon-box .icon {
  background-color: var(--theme-color-dark);
  border-radius: 50%;
  color: var(--theme-color-light);
  display: inline-block;
  font-size: 20px;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  height: 40px;
  line-height: 40px;
  margin-right: 10px;
  text-align: center;
  -webkit-transform: scale(0);
          transform: scale(0);
  width: 40px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.product-block.home-style .inner-box .image-box .icon-box .icon:last-child {
  margin-right: 0;
}
.product-block.home-style .inner-box .image-box .icon-box .icon:hover {
  background-color: var(--theme-color1);
}
.product-block.home-style .inner-box .content-box {
  margin-top: 34px;
}
@media (max-width: 767.98px) {
  .product-block.home-style .inner-box .content-box {
    margin-bottom: 24px;
  }
}
.product-block.home-style .inner-box .content-box .inner {
  text-align: center;
}
.product-block.home-style .inner-box .content-box .inner .price {
  color: var(--theme-color1);
  font-family: var(--title-font);
  font-size: 30px;
  font-weight: 500;
}
@media (max-width: 767.98px) {
  .product-block.home-style .inner-box .content-box .inner .price {
    font-size: 24px;
  }
}
.product-block.home-style .inner-box .content-box .inner .price .price-style {
  position: relative;
}
.product-block.home-style .inner-box .content-box .inner .price .price-style:before {
  background-color: var(--theme-color1);
  bottom: 14px;
  content: "";
  display: block;
  height: 1px;
  left: 0;
  position: absolute;
  width: 100%;
}
@media (max-width: 767.98px) {
  .product-block.home-style .inner-box .content-box .inner .price .price-style:before {
    bottom: 10px;
  }
}
.product-block.home-style .inner-box .content-box .inner .title {
  color: var(--headings-color);
  font-size: var(--h4-font-size);
  font-weight: 500;
  margin-bottom: 0;
  margin-top: 9px;
}
@media (max-width: 767.98px) {
  .product-block.home-style .inner-box .content-box .inner .title {
    font-size: 24px;
    margin-top: 6px;
  }
}
.product-block.home-style .inner-box .content-box .inner .title a {
  color: var(--headings-color);
}
.product-block.home-style .inner-box .content-box .inner .title:hover {
  color: var(--theme-color1);
}

.product-block.home-style .title a {
  color: var(--headings-color);
}

/***

====================================================================
    Product Deals Section
====================================================================

***/
.product-deals-section {
  padding: 204px 0;
  position: relative;
}
@media (max-width: 575.98px) {
  .product-deals-section {
    padding: 170px 0;
  }
}
.product-deals-section:before {
  background: -webkit-gradient(linear, right top, left top, from(#141215), color-stop(rgba(20, 18, 21, 0.6392156863)), color-stop(transparent), to(transparent));
  background: linear-gradient(to left, #141215, rgba(20, 18, 21, 0.6392156863), transparent, transparent);
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: 1;
}
.product-deals-section .outer-box {
  position: relative;
  z-index: 1;
}
.product-deals-section .curved-shape-top {
  width: 100%;
  height: 69px;
  top: 0;
  left: 0;
  z-index: 1;
}
.product-deals-section .curved-shape-bottom {
  width: 100%;
  height: 69px;
  bottom: 0;
  left: 0;
  top: auto;
  z-index: 1;
}
.product-deals-section .sec-title h1 {
  color: var(--theme-color-light);
  font-size: 80px;
  font-weight: 400;
}
@media (max-width: 767.98px) {
  .product-deals-section .sec-title h1 {
    font-size: 60px;
  }
}
@media (max-width: 767.98px) {
  .product-deals-section .sec-title h1 {
    font-size: 45px;
  }
}
.product-deals-section .sec-title .text-two {
  color: var(--theme-color-light);
  display: block;
  font-size: 40px;
  font-weight: 500;
  font-family: var(--title-font);
  margin-top: 5px;
  margin-bottom: 20px;
}
@media (max-width: 767.98px) {
  .product-deals-section .sec-title .text-two {
    font-size: 30px;
  }
}
@media (max-width: 767.98px) {
  .product-deals-section .sec-title .text-two {
    font-size: 24px;
    margin-top: 0;
  }
}
.product-deals-section .sec-title .text {
  color: var(--theme-color-light);
}
.product-deals-section .sec-title .text-three {
  color: var(--theme-color1);
  display: block;
  font-size: 40px;
  font-weight: 500;
  font-family: var(--title-font);
  margin-top: 14px;
  margin-bottom: 38px;
}
@media (max-width: 767.98px) {
  .product-deals-section .sec-title .text-three {
    font-size: 34px;
  }
}

.countdown-block-outer {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
@media (max-width: 1199.98px) {
  .countdown-block-outer {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
}
@media (max-width: 1199.98px) {
  .countdown-block-outer .countdown-block {
    margin: 0 15px;
  }
}
@media (max-width: 1199.98px) {
  .countdown-block-outer .countdown-block {
    margin: 0 5px;
  }
}
.countdown-block-outer .countdown-block .inner-box .content-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background-color: var(--theme-color1);
  border: 1px dashed var(--theme-color-light);
  border-radius: 50% 50% 50% 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 134px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
  width: 136px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 767.98px) {
  .countdown-block-outer .countdown-block .inner-box .content-box {
    width: 100px;
    height: 100px;
  }
}
@media (max-width: 575.98px) {
  .countdown-block-outer .countdown-block .inner-box .content-box {
    width: 80px;
    height: 80px;
  }
}
.countdown-block-outer .countdown-block .inner-box .content-box .countdown-time {
  color: var(--theme-color-light);
  font-size: 40px;
  font-weight: 500;
  font-family: var(--title-font);
  margin-bottom: 12px;
}
@media (max-width: 767.98px) {
  .countdown-block-outer .countdown-block .inner-box .content-box .countdown-time {
    font-size: 30px;
    margin-bottom: 5px;
  }
}
@media (max-width: 575.98px) {
  .countdown-block-outer .countdown-block .inner-box .content-box .countdown-time {
    font-size: 26px;
    margin-bottom: 0;
  }
}
.countdown-block-outer .countdown-block .inner-box .content-box .countdown-title {
  color: var(--theme-color-light);
}
@media (max-width: 767.98px) {
  .countdown-block-outer .countdown-block .inner-box .content-box .countdown-title {
    font-size: 14px;
  }
}
@media (max-width: 767.98px) {
  .countdown-block-outer .countdown-block .inner-box .content-box .countdown-title {
    font-size: 12px;
  }
}
.countdown-block-outer .countdown-block .inner-box:hover .content-box {
  background-color: var(--theme-color-light);
}
.countdown-block-outer .countdown-block .inner-box:hover .content-box .countdown-time,
.countdown-block-outer .countdown-block .inner-box:hover .content-box .countdown-title {
  color: var(--theme-color1);
}

/***

====================================================================
    Team Section
====================================================================

***/
.team-section {
  padding: 123px 0 100px;
}
.team-section .default-dots .owl-dots {
  left: 0;
  position: absolute;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  bottom: -78px;
}
.team-section .default-dots .owl-dots .owl-dot {
  height: 12px;
  width: 12px;
  margin: 0 8.5px;
  background-color: transparent;
  border: 1px solid #707070;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.team-section .default-dots .owl-dots .owl-dot.active {
  width: 15px;
  height: 15px;
  background-color: var(--theme-color1);
  border-color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.team-block .inner-box {
  position: relative;
  margin-bottom: 26px;
}
.team-block .inner-box:hover:before {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.team-block .inner-box:hover .content-box .name {
  -webkit-transform: translateY(-40px);
          transform: translateY(-40px);
  opacity: 1;
}
.team-block .inner-box:hover .content-box .designation {
  -webkit-transform: translateY(-38px);
          transform: translateY(-38px);
  opacity: 1;
}
.team-block .inner-box:before {
  background-color: rgba(194, 167, 78, 0.85);
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(0);
          transform: scale(0);
  left: 0%;
  top: 0%;
  z-index: 1;
  pointer-events: none;
  border-radius: 91% 61% 68% 84%/73% 62% 89% 79%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.team-block .inner-box .image-box .bg-image {
  bottom: -26px;
  left: -6px;
  top: auto;
  right: auto;
  width: 397px;
  height: 390px;
  pointer-events: none;
  z-index: 2;
  border-radius: 91% 61% 68% 84%/73% 62% 89% 79%;
  border: 1px solid #F3E9FF;
}
@media (max-width: 1199.98px) {
  .team-block .inner-box .image-box .bg-image {
    display: none;
  }
}
.team-block .inner-box .image-box .image img {
  border-radius: 91% 61% 68% 84%/73% 62% 89% 79%;
}
.team-block .content-box {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  z-index: 2;
}
.team-block .content-box .name {
  color: var(--theme-color-light);
  font-weight: 500;
  margin-bottom: 8px;
  opacity: 0;
  -webkit-transform: translate(0);
          transform: translate(0);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.team-block .content-box .name a:hover {
  color: var(--theme-color-dark);
}
.team-block .content-box .designation {
  color: var(--theme-color-light);
  font-size: 22px;
  font-weight: 500;
  font-family: var(--title-font);
  opacity: 0;
  -webkit-transform: translateY(0);
          transform: translateY(0);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/***

====================================================================
    Team Section Two
====================================================================

***/
.team-section-two {
  padding: 100px 0 150px;
  position: relative;
}
@media (max-width: 991.98px) {
  .team-section-two {
    padding: 0 0 150px;
  }
}
@media (max-width: 767.98px) {
  .team-section-two {
    padding: 0 0 100px;
  }
}
.team-section-two .team-column .inner-column {
  position: relative;
}
.team-section-two .team2-2 {
  background-image: url(../images/resource/team2-2.png);
  position: absolute;
  width: 317px;
  height: 329px;
  left: 0;
  top: 0;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .team-section-two .team2-2 {
    display: none;
  }
}
.team-section-two .leaf4 {
  background-image: url(../images/resource/leaf4.png);
  position: absolute;
  width: 253px;
  height: 144px;
  right: 0;
  top: -21px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .team-section-two .leaf4 {
    display: none;
  }
}

.team-block-two {
  margin-bottom: 40px;
}
@media (max-width: 767.98px) {
  .team-block-two {
    margin-bottom: 28px;
  }
}
@media (max-width: 575.98px) {
  .team-block-two {
    margin-bottom: 20px;
  }
}
.team-block-two:last-child {
  margin-bottom: 0;
}
.team-block-two .inner-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid rgba(112, 112, 112, 0.25);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding-bottom: 34px;
}
@media (max-width: 767.98px) {
  .team-block-two .inner-box {
    padding-bottom: 28px;
  }
}
@media (max-width: 575.98px) {
  .team-block-two .inner-box {
    padding-bottom: 20px;
  }
}
.team-block-two .inner-box:hover .bg-image {
  -webkit-transform: scale(1);
          transform: scale(1);
}
.team-block-two .inner-box:hover .info-box .name a {
  color: var(--theme-color1);
}
.team-block-two .inner-box:hover .icon {
  color: var(--theme-color1);
  -webkit-transform: rotate(43deg);
          transform: rotate(43deg);
}
.team-block-two .inner-box .bg-image {
  position: absolute;
  top: 120px;
  left: calc(100% - 526px);
  width: 350px;
  height: 405px;
  pointer-events: none;
  -webkit-transform: scale(0);
          transform: scale(0);
  z-index: 2;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 767.98px) {
  .team-block-two .inner-box .bg-image {
    display: none;
  }
}
.team-block-two .inner-box .info-box {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.team-block-two .inner-box .info-box .name {
  margin-bottom: 0;
  margin-right: 58px;
}
@media (max-width: 767.98px) {
  .team-block-two .inner-box .info-box .name {
    margin-right: 38px;
    font-size: 24px;
  }
}
@media (max-width: 575.98px) {
  .team-block-two .inner-box .info-box .name {
    margin-right: 18px;
    font-size: 20px;
  }
}
.team-block-two .inner-box .info-box .name:before {
  background-color: var(--headings-color);
  content: "";
  position: absolute;
  bottom: 23px;
  right: -48px;
  height: 1px;
  width: 28.64px;
  -webkit-transform: rotate(114deg);
          transform: rotate(114deg);
}
@media (max-width: 767.98px) {
  .team-block-two .inner-box .info-box .name:before {
    bottom: 12px;
    right: -32px;
    width: 24px;
  }
}
@media (max-width: 575.98px) {
  .team-block-two .inner-box .info-box .name:before {
    bottom: 11px;
    right: -19px;
    width: 18px;
  }
}
.team-block-two .inner-box .info-box .designation {
  color: var(--theme-color-dark);
  display: block;
  font-size: 20px;
  font-weight: 500;
  font-family: var(--title-font);
  text-transform: uppercase;
}
@media (max-width: 767.98px) {
  .team-block-two .inner-box .info-box .designation {
    font-size: 18px;
  }
}
@media (max-width: 575.98px) {
  .team-block-two .inner-box .info-box .designation {
    font-size: 16px;
  }
}
.team-block-two .inner-box .icon {
  color: var(--theme-color-dark);
  font-size: 40px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
@media (max-width: 767.98px) {
  .team-block-two .inner-box .icon {
    font-size: 28px;
  }
}
@media (max-width: 575.98px) {
  .team-block-two .inner-box .icon {
    font-size: 20px;
  }
}

/***

====================================================================
    Team Section Three
====================================================================

***/
.team-section-three {
  background-color: var(--theme-color-light);
  padding: 90px 0 150px;
  position: relative;
}
@media (max-width: 767.98px) {
  .team-section-three {
    padding: 90px 0 100px;
  }
}
.team-section-three .team-column .inner-column {
  position: relative;
}
.team-section-three .team2-2 {
  background-image: url(../images/resource/team2-2.png);
  position: absolute;
  width: 317px;
  height: 329px;
  left: 0;
  top: 235px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .team-section-three .team2-2 {
    display: none;
  }
}
.team-section-three .leaf4 {
  background-image: url(../images/resource/leaf4.png);
  position: absolute;
  width: 253px;
  height: 144px;
  right: 0;
  top: 265px;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .team-section-three .leaf4 {
    display: none;
  }
}

/***

====================================================================
    Clients Section
====================================================================

***/
.clients-section {
  position: relative;
  padding: 100px 0 96px;
}
.clients-section .swiper-wrapper {
  padding-top: 50px;
}
.clients-section .carousel-outer {
  margin: 0 -44px;
}
.clients-section .clients-carousel:before {
  border: 1px solid #000;
  content: "";
  opacity: 10%;
  position: absolute;
  top: 10px;
  width: 100%;
}
.clients-section .clients-carousel .swiper-button-prev,
.clients-section .clients-carousel .swiper-button-next {
  background-color: #F9F6F1;
  color: var(--theme-color-dark);
  font-size: 8px;
  height: 16px;
  line-height: 16px;
  opacity: 1;
  position: absolute;
  width: 16px;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.clients-section .clients-carousel .swiper-button-prev:after,
.clients-section .clients-carousel .swiper-button-next:after {
  font-size: 8px;
}
.clients-section .clients-carousel .swiper-button-prev {
  left: 48.8%;
  right: auto;
  top: 25px;
}
@media (max-width: 1199.98px) {
  .clients-section .clients-carousel .swiper-button-prev {
    left: 48%;
  }
}
.clients-section .clients-carousel .swiper-button-next {
  left: auto;
  right: 48.8%;
  top: 25px;
}
@media (max-width: 1199.98px) {
  .clients-section .clients-carousel .swiper-button-next {
    right: 48%;
  }
}

.client-block .inner-box:hover .image-box .image img {
  opacity: 0.6;
}
.client-block .inner-box .image-box .image {
  text-align: center;
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.client-block .inner-box .image-box .image img {
  opacity: 1;
  width: auto;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

/*** 

====================================================================
    Why Choose Us
====================================================================

***/
.why-choose-us {
  position: relative;
  padding: 0 0 54px;
  z-index: 1;
}
@media (max-width: 1199.98px) {
  .why-choose-us {
    padding: 0;
  }
}
.why-choose-us .bg {
  background-size: auto;
  background-position: right bottom;
  bottom: -222px;
  pointer-events: none;
  top: auto;
  z-index: -1;
}
.why-choose-us .icon-sailboat-line-1 {
  pointer-events: none;
  position: absolute;
  left: 0;
  bottom: 24px;
  z-index: -1;
}
.why-choose-us .icon-wheel-compass-1 {
  position: absolute;
  top: -50px;
  right: 155px;
  -webkit-animation: fa-spin 70s infinite;
          animation: fa-spin 70s infinite;
  z-index: -1;
}
@media (max-width: 1399.98px) {
  .why-choose-us .icon-wheel-compass-1 {
    opacity: 0.1;
  }
}
.why-choose-us .sec-title-two {
  text-align: center;
}
.why-choose-us .sec-title-two h2 {
  opacity: 0.2 !important;
  color: var(--text-color);
  font-family: var(--text-font);
  font-size: 120px;
  font-weight: 400;
  line-height: 1.3em;
  margin-bottom: 0;
  -webkit-text-fill-color: white;
  -webkit-text-stroke: 1px var(--text-color);
}
@media (max-width: 991.98px) {
  .why-choose-us .sec-title-two h2 {
    font-size: 80px;
  }
}
@media (max-width: 767.98px) {
  .why-choose-us .sec-title-two h2 {
    font-size: 60px;
  }
}
@media (max-width: 575.98px) {
  .why-choose-us .sec-title-two h2 {
    font-size: 50px;
  }
}
.why-choose-us .content-column {
  position: relative;
  margin-bottom: 50px;
}
@media (max-width: 1199.98px) {
  .why-choose-us .content-column {
    margin-bottom: 0;
  }
}
.why-choose-us .content-column .inner-column {
  position: relative;
}
.why-choose-us .image-column {
  position: relative;
  margin-bottom: 50px;
}
.why-choose-us .image-column .inner-column {
  position: relative;
}
@media (max-width: 1199.98px) {
  .why-choose-us .image-column .inner-column {
    padding-right: 0;
  }
}
.why-choose-us .image-column .inner-column .image-box {
  position: relative;
}
.why-choose-us .image-column .inner-column .image-box .image {
  position: relative;
  margin-bottom: 0;
  margin-top: -202px;
  z-index: -1;
}
@media (max-width: 991.98px) {
  .why-choose-us .image-column .inner-column .image-box .image img {
    display: none;
  }
}
.why-choose-us .image-column .inner-column .image-box .image-2 {
  position: absolute;
  top: 17px;
  left: 100px;
  margin-bottom: 0;
}
@media (max-width: 991.98px) {
  .why-choose-us .image-column .inner-column .image-box .image-2 {
    display: none;
  }
}
.why-choose-us .image-column .inner-column .image-box .image-3 {
  position: absolute;
  bottom: 104px;
  right: 3px;
  margin-bottom: 0;
}
@media (max-width: 991.98px) {
  .why-choose-us .image-column .inner-column .image-box .image-3 {
    display: none;
  }
}
.why-choose-us .image-column .inner-column .image-box .image-3 img {
  outline: 10px solid var(--theme-color-light);
  outline-offset: -10px;
}

.accordion-box {
  position: relative;
}
.accordion-box .block {
  position: relative;
  background-color: var(--theme-color-light);
}
.accordion-box .block .acc-btn {
  border-bottom: 1px solid #e1e1e1;
  position: relative;
  font-family: var(--title-font);
  font-size: 18px;
  line-height: 24px;
  color: var(--theme-color-dark);
  font-weight: 400;
  cursor: pointer;
  padding: 28px 20px 20px 40px;
  letter-spacing: -0.01em;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.accordion-box .block .acc-btn .icon {
  position: absolute;
  top: 27px;
  left: 0;
  line-height: 28px;
  color: var(--theme-color1);
  font-size: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.accordion-box .block .acc-btn .arrow {
  position: absolute;
  right: 10px;
  top: 30px;
  font-size: 20px;
  line-height: 1;
  font-weight: 400;
  color: var(--theme-color-dark);
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.accordion-box .block .acc-btn.active {
  color: var(--theme-color-dark);
}
.accordion-box .block .acc-btn.active .arrow {
  font-size: 20px;
  color: var(--theme-color-dark);
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.accordion-box .block .acc-btn.active .arrow:before {
  content: "\f068";
}
.accordion-box .block .acc-btn.active .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.accordion-box .block .acc-content {
  font-size: 14px;
  position: relative;
  display: none;
}
.accordion-box .block .acc-content .content {
  position: relative;
  padding: 11px 30px 3px 0;
}
.accordion-box .block .acc-content .content .text {
  display: block;
  font-size: 14px;
  margin-bottom: 0;
}
.accordion-box .block .acc-content.current {
  display: block;
}
.accordion-box.style-two .block {
  position: relative;
  border: none;
  width: 100%;
  padding: 7px 17px 10px;
  margin-bottom: 12px;
  -webkit-box-shadow: 0 0 43px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 43px rgba(0, 0, 0, 0.06);
  background-color: var(--theme-color-light);
}
@media (max-width: 991.98px) {
  .accordion-box.style-two .block {
    padding: 7px 10px 10px;
  }
}
.accordion-box.style-two .block .acc-btn {
  font-weight: 700;
}
@media (max-width: 575.98px) {
  .accordion-box.style-two .block .acc-btn {
    padding-left: 70px;
  }
}
.accordion-box.style-two .block .arrow {
  right: 4px;
}
.accordion-box.style-two .block .content {
  padding: 10px 0 10px;
}
.accordion-box.style-two .block .content .text {
  font-size: 16px;
  line-height: 30px;
  letter-spacing: -0.01em;
  color: #7a7a7a;
}
@media (max-width: 991.98px) {
  .accordion-box.style-two .block .content .text {
    line-height: 24px;
    margin-top: 5px;
  }
}
.accordion-box.style-two .block .acc-btn.active {
  color: var(--theme-color1);
}
.accordion-box.style-two .block .acc-btn.active .arrow,
.accordion-box.style-two .block .acc-btn.active .icon {
  color: var(--theme-color1);
}
.accordion-box.style-two .block .acc-btn.active .count {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.accordion-box.style-two .count {
  position: absolute;
  top: 0;
  left: 0;
  width: 57px;
  height: 57px;
  line-height: 57px;
  color: #a6a6a6;
  background-color: #f2f3f6;
  text-align: center;
}
@media (max-width: 575.98px) {
  .accordion-box.style-two .count {
    top: 5px;
  }
}

/*** 

====================================================================
    Why Choose Us Two
====================================================================

***/
.why-choose-us-two {
  background-color: #fff;
  padding: 150px 0;
  position: relative;
  z-index: 2;
}
@media (max-width: 1199.98px) {
  .why-choose-us-two {
    padding: 100px 0;
  }
}
@media (max-width: 991.98px) {
  .why-choose-us-two {
    padding: 100px 0 80px;
  }
}
.why-choose-us-two .sec-title .text {
  margin-top: 16px;
}
.why-choose-us-two .bg-image {
  position: absolute;
  top: 0;
  right: 0;
  left: auto;
  height: 100%;
  width: 56%;
  z-index: -1;
}
.why-choose-us-two .content-column {
  position: relative;
}
.why-choose-us-two .content-column:before {
  content: "";
  position: absolute;
  max-height: 621px;
  width: 100px;
  background-color: #bfa888;
  left: -28px;
  top: -45px;
  height: 116.7%;
}
@media (max-width: 1199.98px) {
  .why-choose-us-two .content-column:before {
    max-height: 690px;
  }
}
.why-choose-us-two .content-column .inner-column {
  background-color: #fff;
  padding: 64px 133px 80px 61px;
  position: relative;
  z-index: 1;
}
@media (max-width: 1199.98px) {
  .why-choose-us-two .content-column .inner-column {
    padding: 60px 60px 60px 50px;
  }
}
@media (max-width: 575.98px) {
  .why-choose-us-two .content-column .inner-column {
    padding: 60px 30px 60px 30px;
  }
}
.why-choose-us-two .inner-column .text-box text {
  font-size: 200px;
  font-family: Allison;
}
.why-choose-us-two .icon-big-boat-2 {
  position: absolute;
  left: -45px;
  bottom: 3px;
  z-index: -1;
}
@media (max-width: 1199.98px) {
  .why-choose-us-two .icon-big-boat-2 {
    display: none;
  }
}
.why-choose-us-two .content-column-two {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}
@media (max-width: 991.98px) {
  .why-choose-us-two .content-column-two {
    display: none;
  }
}
.why-choose-us-two .content-column-two .text {
  color: white;
  font-size: 250px;
  font-family: Allison;
  text-align: end;
  letter-spacing: 10px;
  position: relative;
  left: 100px;
  top: -100px;
  opacity: 0.5;
}
@media (max-width: 1199.98px) {
  .why-choose-us-two .content-column-two .text {
    font-size: 150px;
    left: -70px;
  }
}

/*** 

====================================================================
    Why Choose Us Three
====================================================================

***/
.why-choose-us-three {
  position: relative;
  padding: 100px 0;
  z-index: 1;
}
.why-choose-us-three .icon-big-boat-5 {
  position: absolute;
  right: 80px;
  bottom: 105px;
  pointer-events: none;
  z-index: -1;
}
@media (max-width: 1399.98px) {
  .why-choose-us-three .icon-big-boat-5 {
    display: none;
  }
}
.why-choose-us-three .bg {
  background-size: auto;
  background-position: left bottom;
  bottom: 0;
  pointer-events: none;
  top: auto;
  z-index: -1;
}
@media (max-width: 1199.98px) {
  .why-choose-us-three .bg {
    background-size: cover;
  }
}
.why-choose-us-three .icon-sailboat-line-1 {
  pointer-events: none;
  position: absolute;
  left: 0;
  bottom: 24px;
  z-index: -1;
}
.why-choose-us-three .icon-wheel-compass-1 {
  position: absolute;
  top: -50px;
  right: 155px;
  -webkit-animation: fa-spin 70s infinite;
          animation: fa-spin 70s infinite;
  z-index: -1;
}
.why-choose-us-three .sec-title-two {
  text-align: center;
}
.why-choose-us-three .sec-title-two h2 {
  opacity: 0.2 !important;
  color: var(--text-color);
  font-family: var(--text-font);
  font-size: 120px;
  font-weight: 400;
  line-height: 1.3em;
  margin-bottom: 0;
  -webkit-text-fill-color: white;
  -webkit-text-stroke: 1px var(--text-color);
}
.why-choose-us-three .content-column {
  position: relative;
}
.why-choose-us-three .content-column .inner-column {
  position: relative;
  background-color: #f4efec;
  padding: 81px 98px 92px 98px;
}
@media (max-width: 767.98px) {
  .why-choose-us-three .content-column .inner-column {
    padding: 60px 35px 55px 39px;
  }
}
.why-choose-us-three .content-column .inner-column:before {
  border: 1px solid var(--theme-color1);
  content: "";
  position: absolute;
  left: 18px;
  top: 18px;
  right: 18px;
  bottom: 18px;
}
.why-choose-us-three .content-column .inner-column .bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  z-index: 1;
}

.accordion-box-three {
  background-color: transparent;
  position: relative;
}
.accordion-box-three .block {
  background-color: transparent;
  position: relative;
}
.accordion-box-three .block .acc-btn {
  border-bottom: 1px solid #e1e1e1;
  position: relative;
  font-family: var(--title-font);
  font-size: 18px;
  line-height: 24px;
  color: var(--theme-color-dark);
  font-weight: 400;
  cursor: pointer;
  padding: 28px 20px 20px 40px;
  letter-spacing: -0.01em;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.accordion-box-three .block .acc-btn .icon {
  position: absolute;
  top: 27px;
  left: 0;
  line-height: 28px;
  color: var(--theme-color1);
  font-size: 20px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.accordion-box-three .block .acc-btn .arrow {
  position: absolute;
  right: 10px;
  top: 30px;
  font-size: 20px;
  line-height: 1;
  font-weight: 400;
  color: var(--theme-color-dark);
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.accordion-box-three .block .acc-btn.active {
  color: var(--theme-color-dark);
}
.accordion-box-three .block .acc-btn.active .arrow {
  font-size: 20px;
  color: var(--theme-color-dark);
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.accordion-box-three .block .acc-btn.active .arrow:before {
  content: "\f068";
}
.accordion-box-three .block .acc-btn.active .icon {
  -webkit-transform: scale(-1) rotate(180deg);
          transform: scale(-1) rotate(180deg);
}
.accordion-box-three .block .acc-content {
  font-size: 14px;
  position: relative;
  display: none;
}
.accordion-box-three .block .acc-content .content {
  position: relative;
  padding: 11px 30px 3px 0;
}
.accordion-box-three .block .acc-content .content .text {
  display: block;
  font-size: 14px;
  margin-bottom: 0;
}
.accordion-box-three .block .acc-content.current {
  display: block;
}
.accordion-box-three.style-two .block {
  position: relative;
  border: none;
  width: 100%;
  padding: 7px 17px 10px;
  margin-bottom: 12px;
  -webkit-box-shadow: 0 0 43px rgba(0, 0, 0, 0.06);
          box-shadow: 0 0 43px rgba(0, 0, 0, 0.06);
  background-color: var(--theme-color-light);
}
.accordion-box-three.style-two .block .acc-btn {
  padding-left: 92px;
  font-weight: 700;
}
.accordion-box-three.style-two .block .arrow {
  right: 4px;
}
.accordion-box-three.style-two .block .content {
  padding: 10px 0 10px;
}
.accordion-box-three.style-two .block .content .text {
  font-size: 16px;
  line-height: 30px;
  letter-spacing: -0.01em;
  color: #7a7a7a;
}
.accordion-box-three.style-two .block .acc-btn.active {
  color: var(--theme-color1);
}
.accordion-box-three.style-two .block .acc-btn.active .arrow,
.accordion-box-three.style-two .block .acc-btn.active .icon {
  color: var(--theme-color1);
}
.accordion-box-three.style-two .block .acc-btn.active .count {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.accordion-box-three.style-two .count {
  position: absolute;
  top: 0;
  left: 0;
  width: 57px;
  height: 57px;
  line-height: 57px;
  color: #a6a6a6;
  background-color: #f2f3f6;
  text-align: center;
}

/*** 

====================================================================
    Blog Section
====================================================================

***/
.blog-section {
  position: relative;
  padding: 40px 0 145px;
}
@media (max-width: 767.98px) {
  .blog-section {
    padding: 0 0 160px;
  }
}
.blog-section .sec-title {
  margin-bottom: 50px;
}
.blog-section .default-dots .owl-dots {
  left: 0;
  position: absolute;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  bottom: -75px;
}
.blog-section .default-dots .owl-dots .owl-dot {
  height: 12px;
  width: 12px;
  margin: 0 8.5px;
  background-color: transparent;
  border: 1px solid #707070;
  border-radius: 50%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.blog-section .default-dots .owl-dots .owl-dot.active {
  width: 15px;
  height: 15px;
  background-color: var(--theme-color1);
  border-color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.blog-block {
  margin-bottom: 40px;
}
.blog-block .inner-box {
  position: relative;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.blog-block .inner-box:hover .image-box .image img:first-child {
  -webkit-transform: translateX(0) scaleX(1);
          transform: translateX(0) scaleX(1);
  opacity: 1;
  -webkit-filter: blur(0);
          filter: blur(0);
}
.blog-block .inner-box:hover .image-box .image img:nth-child(2) {
  -webkit-transform: translateX(-50%) scaleX(2);
          transform: translateX(-50%) scaleX(2);
  opacity: 0;
  -webkit-filter: blur(10px);
          filter: blur(10px);
}
.blog-block .image-box {
  position: relative;
}
.blog-block .image-box .image {
  position: relative;
  overflow: hidden;
  margin-bottom: 0;
}
.blog-block .image-box .image img {
  position: relative;
  width: 100%;
  display: block;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.blog-block .image-box .image img:first-child {
  position: absolute;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  z-index: 1;
  -webkit-transform: translateX(50%) scaleX(2);
          transform: translateX(50%) scaleX(2);
  opacity: 0;
  -webkit-filter: blur(10px);
          filter: blur(10px);
}
.blog-block .image-box .date {
  position: absolute;
  left: 30px;
  bottom: 30px;
  width: 72px;
  display: block;
  z-index: 3;
}
.blog-block .image-box .date strong {
  position: relative;
  display: block;
  width: 72px;
  font-size: 30px;
  font-weight: 700;
  font-family: var(--title-font);
  padding: 5px 0 13px;
  line-height: 1;
  color: var(--theme-color-light);
  background-color: var(--theme-color1);
  text-align: center;
}
.blog-block .image-box .date strong span {
  display: block;
  font-size: 14px;
  font-family: var(--text-font);
  margin-top: 4px;
}
.blog-block .content-box {
  margin-top: 18px;
}
.blog-block .content-box .post-meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 53px;
  margin-bottom: 19px;
}
.blog-block .content-box .post-meta li {
  position: relative;
  font-family: var(--title-font);
  font-size: 22px;
  font-weight: 500;
}
.blog-block .content-box .post-meta .categories:hover a {
  color: var(--theme-color1);
}
.blog-block .content-box .post-meta .categories:before {
  content: "";
  position: absolute;
  border-bottom: 1px solid #1C1A1D;
  width: 43px;
  left: -53px;
  bottom: 3px;
}
.blog-block .content-box .post-meta .categories a {
  color: #092C4C;
}
.blog-block .content-box .post-meta .date {
  color: #707070;
  margin-left: 36px;
}
.blog-block .content-box .post-meta .date:before {
  bottom: 10px;
  bottom: 13px;
  border-radius: 50%;
  background-color: #D25239;
  content: "";
  position: absolute;
  width: 3px;
  height: 3px;
  left: -20px;
}
.blog-block .content-box .title {
  color: #092C4C;
  font-weight: 500;
  margin-bottom: 20px;
}
@media (max-width: 575.98px) {
  .blog-block .content-box .title {
    font-size: 26px;
    margin-bottom: 20px;
  }
}
.blog-block .content-box .title:hover {
  color: var(--theme-color1);
}
.blog-block .content-box .read-more {
  color: var(--theme-color-dark);
  font-size: 22px;
  font-weight: 500;
  line-height: 1;
  margin-bottom: 0;
  position: relative;
  font-family: var(--title-font);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.blog-block .content-box .read-more:before {
  background-color: var(--theme-color-dark);
  content: "";
  height: 1px;
  position: absolute;
  top: 50%;
  right: -60px;
  bottom: 0;
  width: 51px;
}
.blog-block .content-box .read-more:after {
  background-color: var(--theme-color1);
  content: "";
  height: 1px;
  position: absolute;
  top: 50%;
  right: -9px;
  bottom: 0;
  width: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.blog-block .content-box .read-more:hover {
  color: var(--theme-color1);
}
.blog-block .content-box .read-more:hover:after {
  right: -60px;
  width: 51px;
}
.blog-block .content-box .read-more:hover .icon {
  color: var(--theme-color1);
}
.blog-block .content-box .read-more .icon {
  color: var(--theme-color-dark);
  font-size: 12px;
  position: absolute;
  bottom: 6px;
  right: -62px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

/*** 

====================================================================
    Blog Section Two
====================================================================

***/
.blog-section-two {
  position: relative;
  padding: 122px 0 95px;
}
@media (max-width: 767.98px) {
  .blog-section-two {
    padding: 50px 0 70px;
  }
}
.blog-section-two .sec-title {
  margin-bottom: 50px;
}

/***

==================================================================
	Main Footer
==================================================================

***/
.main-footer {
  background-color: var(--theme-color-dark);
  position: relative;
  padding: 140px 0 0;
}
@media (max-width: 991.98px) {
  .main-footer {
    padding: 100px 0 0;
  }
}
.main-footer .widgets-section {
  padding-bottom: 88px;
}
@media (max-width: 991.98px) {
  .main-footer .widgets-section {
    padding-bottom: 80px;
  }
}
.main-footer .timetable-widget {
  max-width: 253px;
}
@media (max-width: 991.98px) {
  .main-footer .timetable-widget {
    margin-bottom: 50px;
  }
}
@media (max-width: 991.98px) {
  .main-footer .about-widget {
    text-align: left !important;
    margin-bottom: 50px;
  }
}
.main-footer .about-widget .logo {
  margin-bottom: 25px;
}
.main-footer .about-widget .text {
  margin: 0 15px;
}
@media (max-width: 991.98px) {
  .main-footer .about-widget .text {
    margin: 0;
    font-size: 14px;
  }
}
@media (max-width: 767.98px) {
  .main-footer .about-widget .text {
    margin: 0;
  }
}
.main-footer .contacts-widget {
  text-align: right;
}
@media (max-width: 991.98px) {
  .main-footer .contacts-widget {
    text-align: left;
  }
}
.main-footer .footer-widget .widget-title {
  color: var(--theme-color-light);
  font-weight: 500;
  margin-bottom: 40px;
}
@media (max-width: 991.98px) {
  .main-footer .footer-widget .widget-title {
    margin-bottom: 20px;
    font-size: 28px;
  }
}
.main-footer .footer-widget .timetable li {
  color: var(--theme-color-light);
}
@media (max-width: 991.98px) {
  .main-footer .footer-widget .timetable li {
    font-size: 14px;
  }
}
.main-footer .footer-widget .timetable li span {
  float: right;
}
.main-footer .footer-widget .text {
  color: var(--theme-color-light);
}
@media (max-width: 991.98px) {
  .main-footer .footer-widget .text {
    font-size: 14px;
  }
}
.main-footer .footer-widget .social-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-top: 22px;
}
.main-footer .footer-widget .social-icon li {
  margin-right: 23px;
}
.main-footer .footer-widget .social-icon li:last-child {
  margin-right: 0;
}
.main-footer .footer-widget .social-icon li a {
  font-size: 25px;
  font-weight: 600;
  color: var(--theme-color-light);
}
@media (max-width: 991.98px) {
  .main-footer .footer-widget .social-icon li a {
    font-size: 20px;
  }
}
.main-footer .footer-widget .social-icon li a:hover {
  color: var(--theme-color1);
}
.main-footer .footer-widget .contact-info li a {
  color: var(--theme-color-light);
}
@media (max-width: 991.98px) {
  .main-footer .footer-widget .contact-info li a {
    font-size: 14px;
  }
}
.main-footer .footer-widget .contact-info li a:hover {
  color: var(--theme-color1);
}
.main-footer .footer1-1 {
  background-image: url(../images/icons/footer1-1.png);
  position: absolute;
  width: 268px;
  height: 533px;
  bottom: 0;
  left: 0;
  pointer-events: none;
}
@media (max-width: 1399.98px) {
  .main-footer .footer1-1 {
    opacity: 0.4;
  }
}

/***

==================================================================
	Footer Style
==================================================================

***/
.footer-style-two {
  background-color: transparent;
  padding: 100px 0 0;
  position: relative;
}
.footer-style-two .bg-image {
  z-index: -1;
}
.footer-style-two .widgets-section {
  padding-bottom: 55px;
}
.footer-style-two .widgets-section .footer-pattrn1 {
  background-image: url(../images/resource/footer-pattrn1.png);
  position: absolute;
  width: 285px;
  height: 344px;
  bottom: -30px;
  right: 0;
  pointer-events: none;
}
@media (max-width: 1199.98px) {
  .footer-style-two .about-widget {
    margin-bottom: 50px;
  }
}
.footer-style-two .about-widget .text {
  margin: 0 0 28px 0;
}
.footer-style-two .footer-widget .timetable li {
  line-height: 45px;
}
.footer-style-two .contacts-widget {
  text-align: start;
}
.footer-style-two .contacts-widget .text {
  line-height: 45px;
}
.footer-style-two .contacts-widget .contact-info li {
  line-height: 45px;
}
.footer-style-two .contacts-widget .contact-info li .text-style-one {
  text-decoration: underline;
}
.footer-style-two .contacts-widget .contact-info li .text-style-two {
  font-weight: 700;
}
.footer-style-two .footer-bottom .inner-container {
  background-color: #141215;
  border: none;
  border-radius: 10px 10px 0px 0px;
  padding: 21px 0 20px 0;
}
.footer-style-two .footer-bottom .inner-container .copyright-text {
  width: 100%;
  text-align: center;
}

/* Subscribe Form */
.subscribe-form {
  position: relative;
}
.subscribe-form .form-group {
  position: relative;
  margin-bottom: 0;
}
.subscribe-form .form-group input[type=text],
.subscribe-form .form-group input[type=email] {
  position: relative;
  display: block;
  height: 60px;
  width: 100%;
  font-size: 12px;
  line-height: 24px;
  color: #707070;
  padding: 15px 30px;
  padding-right: 60px;
  background: #141215;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.subscribe-form .form-group input[type=text]::-webkit-input-placeholder, .subscribe-form .form-group input[type=email]::-webkit-input-placeholder {
  color: #707070;
}
.subscribe-form .form-group input[type=text]::-moz-placeholder, .subscribe-form .form-group input[type=email]::-moz-placeholder {
  color: #707070;
}
.subscribe-form .form-group input[type=text]:-ms-input-placeholder, .subscribe-form .form-group input[type=email]:-ms-input-placeholder {
  color: #707070;
}
.subscribe-form .form-group input[type=text]::-ms-input-placeholder, .subscribe-form .form-group input[type=email]::-ms-input-placeholder {
  color: #707070;
}
.subscribe-form .form-group input[type=text]::placeholder,
.subscribe-form .form-group input[type=email]::placeholder {
  color: #707070;
}
.subscribe-form .form-group .theme-btn {
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -22px;
  height: 44px;
  width: 44px;
  line-height: 44px;
  font-size: 20px;
  border-radius: 5%;
  color: var(--theme-color1);
  background: transparent;
  display: block;
}
.subscribe-form .form-group .theme-btn:hover {
  color: #FFFFFF;
}

/* Links Widget */
.links-widget {
  position: relative;
  left: 109px;
}
@media (max-width: 1199.98px) {
  .links-widget {
    margin-bottom: 50px;
    left: 0;
  }
}
.links-widget .user-links li a {
  color: var(--theme-color-light);
  font-size: 16px;
  font-weight: 400;
  line-height: 45px;
}
.links-widget .user-links li a:hover {
  color: var(--theme-color1);
}

/*=== Footer Bottom ===*/
.footer-bottom {
  position: relative;
  width: 100%;
}
.footer-bottom .image {
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .footer-bottom .image {
    margin-bottom: 5px;
  }
}
.footer-bottom .inner-container {
  border-top: 1px solid rgba(255, 255, 255, 0.25);
  background-color: transparent;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  padding: 30px 0 30px 0;
}
@media (max-width: 575.98px) {
  .footer-bottom .inner-container {
    display: block;
  }
}
.footer-bottom .inner-container .copyright-text {
  color: var(--theme-color-light);
  position: relative;
  font-weight: 400;
  margin-bottom: 0px;
}
@media (max-width: 991.98px) {
  .footer-bottom .inner-container .copyright-text {
    font-size: 14px;
  }
}
.footer-bottom .inner-container .copyright-text a {
  color: inherit;
}
.footer-bottom .inner-container .copyright-text a:hover {
  color: var(--theme-color1);
}
.footer-bottom .inner-container .link {
  color: var(--theme-color-light);
}
@media (max-width: 991.98px) {
  .footer-bottom .inner-container .link {
    font-size: 14px;
  }
}
.footer-bottom .inner-container .link:hover {
  color: var(--theme-color1);
}

.page-wrapper.home3-style {
  background-color: var(--theme-color5);
}

@-webkit-keyframes modal-video {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-video {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes modal-video-inner {
  from {
    -webkit-transform: translate(0, 100px);
            transform: translate(0, 100px);
  }
  to {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
}
@keyframes modal-video-inner {
  from {
    -webkit-transform: translate(0, 100px);
            transform: translate(0, 100px);
  }
  to {
    -webkit-transform: translate(0, 0);
            transform: translate(0, 0);
  }
}
.modal-video {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000000;
  cursor: pointer;
  opacity: 1;
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
  -webkit-animation-duration: 0.3s;
          animation-duration: 0.3s;
  -webkit-animation-name: modal-video;
          animation-name: modal-video;
  -webkit-transition: opacity 0.3s ease-out;
  transition: opacity 0.3s ease-out;
}

.modal-video-effect-exit {
  opacity: 0;
}
.modal-video-effect-exit .modal-video-movie-wrap {
  -webkit-transform: translate(0, 100px);
  transform: translate(0, 100px);
}

.modal-video-body {
  max-width: 960px;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 0 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.modal-video-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  width: 100%;
  height: 100%;
}
@media (orientation: landscape) {
  .modal-video-inner {
    padding: 10px 60px;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
  }
}

.modal-video-movie-wrap {
  width: 100%;
  height: 0;
  position: relative;
  padding-bottom: 56.25%;
  background-color: #333;
  -webkit-animation-timing-function: ease-out;
          animation-timing-function: ease-out;
  -webkit-animation-duration: 0.3s;
          animation-duration: 0.3s;
  -webkit-animation-name: modal-video-inner;
          animation-name: modal-video-inner;
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
}
.modal-video-movie-wrap iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.modal-video-close-btn {
  position: absolute;
  z-index: 2;
  top: -45px;
  right: 0px;
  display: inline-block;
  width: 35px;
  height: 35px;
  overflow: hidden;
  border: none;
  background: transparent;
}
@media (orientation: landscape) {
  .modal-video-close-btn {
    top: 0;
    right: -45px;
  }
}
.modal-video-close-btn:before {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.modal-video-close-btn:after {
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
}
.modal-video-close-btn:before, .modal-video-close-btn:after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  top: 50%;
  left: 0;
  margin-top: -1px;
  background: #fff;
  border-radius: 5px;
  margin-top: -6px;
}

.page-wrapper.home4-style,
.page-wrapper.home7-style {
  background-color: #F9F6F1;
}

.home4-style {
  background-color: #F9F6F1 !important;
}

.main-header.header-style-four {
  background-color: transparent;
}
.main-header.header-style-four .header-lower {
  background-color: transparent;
  padding-left: 100px;
  padding-right: 100px;
}
@media (max-width: 1299.98px) {
  .main-header.header-style-four .header-lower {
    padding-left: 50px;
    padding-right: 50px;
  }
}
@media (max-width: 1199.98px) {
  .main-header.header-style-four .header-lower {
    padding-left: 12px;
    padding-right: 12px;
  }
}
.main-header.header-style-four .outer-box {
  max-width: none;
  padding: 0;
  width: 100%;
}
.main-header.header-style-four .outer-box .outer-box {
  width: auto;
}
.main-header.header-style-four .main-box .nav-outer {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
@media (max-width: 1499.98px) {
  .main-header.header-style-four .main-menu .navigation > li {
    margin-right: 40px;
  }
}
@media (max-width: 1299.98px) {
  .main-header.header-style-four .sticky-header .main-menu .navigation > li {
    margin-left: 10px;
  }
}
.main-header.header-style-home5 {
  background-color: #F4ECDF;
}
.main-header.header-style-home5 .header-lower {
  border-radius: 10px;
}

/*** 

====================================================================
    Banner 1 Home4 Style 1
====================================================================

***/
.banner-style1-home4 {
  padding: 120px 0 0;
  position: relative;
}
@media (max-width: 991.98px) {
  .banner-style1-home4 {
    padding-top: 100px;
  }
}
.banner-style1-home4 .content-column {
  padding-bottom: 120px;
}
.banner-style1-home4 .content-column .text {
  color: #707070;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 30px;
  max-width: 680px;
}
.banner-style1-home4 .content-column .title {
  color: var(--headings-color);
  font-weight: 500;
  font-size: 96px;
  line-height: 116px;
}
@media (max-width: 767.98px) {
  .banner-style1-home4 .content-column .title {
    font-size: 60px;
    line-height: 1.3;
  }
}
.banner-style1-home4 .content-column .title a {
  background-image: url(../images/banner/tilte-bg.jpg);
  border-radius: 50px;
  display: inline-block;
  height: 70px;
  width: 143px;
}
@media (max-width: 767.98px) {
  .banner-style1-home4 .content-column .title a {
    vertical-align: sub;
  }
}
.banner-style1-home4 .image-content {
  position: relative;
}
.banner-style1-home4 .image-content .img {
  position: absolute;
  top: -200px;
}
@media (max-width: 1199.98px) {
  .banner-style1-home4 .image-content .img {
    position: relative;
    top: 0;
  }
}

.banner-style2-home4 {
  height: 637px;
  position: relative;
}
@media (max-width: 1199.98px) {
  .banner-style2-home4 {
    height: 350px;
  }
}
.banner-style2-home4 .bg {
  background-attachment: fixed;
}

/*** 

====================================================================
    Banner 1 Home5 Style 1
====================================================================

***/
.banner-style1-home5 {
  background-color: #F4ECDF;
  overflow: hidden;
  padding: 320px 0 60px;
  position: relative;
}
@media (max-width: 1399.98px) {
  .banner-style1-home5 {
    padding: 200px 0 40px;
  }
}
@media (max-width: 1299.98px) {
  .banner-style1-home5 {
    padding: 220px 0 0;
  }
}
@media (max-width: 1199.98px) {
  .banner-style1-home5 {
    padding: 120px 0 0;
  }
}
.banner-style1-home5 .leaf-1 {
  bottom: 0;
  position: absolute;
  right: 0;
  z-index: 3;
}
.banner-style1-home5 .banner-bottom {
  bottom: -1px;
  position: absolute;
  z-index: 2;
}
.banner-style1-home5 .auto-container {
  max-width: 1600px;
  width: 100%;
}
@media (max-width: 991.98px) {
  .banner-style1-home5 {
    padding-top: 100px;
  }
}
.banner-style1-home5 .content-column {
  margin-top: -80px;
  padding-bottom: 120px;
  padding-left: 70px;
  position: relative;
  z-index: 3;
}
@media (max-width: 1199.98px) {
  .banner-style1-home5 .content-column {
    margin-top: 0;
  }
}
@media (max-width: 767.98px) {
  .banner-style1-home5 .content-column {
    padding-left: 0;
  }
}
.banner-style1-home5 .content-column .title-bg {
  position: relative;
  margin-bottom: 30px;
}
.banner-style1-home5 .content-column .title-bg span {
  color: rgba(255, 255, 255, 0.4);
  font-weight: 700;
  font-size: 96px;
  left: -50px;
  line-height: 116px;
  letter-spacing: 0.22em;
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  top: -40px;
}
@media (max-width: 767.98px) {
  .banner-style1-home5 .content-column .title-bg span {
    font-size: 50px;
  }
}
.banner-style1-home5 .content-column .title-bg:before {
  border: 2px solid #D0AC3B;
  content: "";
  height: 0px;
  left: 70px;
  position: absolute;
  top: 20px;
  width: 170px;
  z-index: 1;
}
.banner-style1-home5 .content-column .text {
  color: #707070;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 30px;
  max-width: 680px;
}
.banner-style1-home5 .content-column .title {
  color: #1C1A1D;
  font-size: 80px;
  line-height: 97px;
  text-transform: uppercase;
}
@media (max-width: 767.98px) {
  .banner-style1-home5 .content-column .title {
    font-size: 60px;
    line-height: 1.3;
  }
}
@media (max-width: 1499.98px) {
  .banner-style1-home5 .content-column .title br {
    display: none;
  }
}
.banner-style1-home5 .image-content {
  position: relative;
  z-index: 1;
}
.banner-style1-home5 .image-content .bg-circle {
  border: 70px solid rgba(194, 167, 78, 0.1);
  border-radius: 50%;
  bottom: -340px;
  height: 653px;
  position: absolute;
  width: 653px;
}
@media (max-width: 1399.98px) {
  .banner-style1-home5 .image-content .bg-circle {
    bottom: -420px;
  }
}
@media (max-width: 1299.98px) {
  .banner-style1-home5 .image-content .bg-circle {
    bottom: -410px;
  }
}
.banner-style1-home5 .image-content .img {
  position: absolute;
  top: -220px;
  z-index: 0;
}
@media (max-width: 1499.98px) {
  .banner-style1-home5 .image-content .img {
    top: -120px;
  }
}
@media (max-width: 1299.98px) {
  .banner-style1-home5 .image-content .img {
    top: -80px;
  }
}
@media (max-width: 1199.98px) {
  .banner-style1-home5 .image-content .img {
    position: relative;
    top: 0;
  }
}
.banner-style1-home5 .image-content .img-2 {
  position: absolute;
  right: -50px;
  top: 0;
  z-index: -1;
}
.banner-style1-home5.home7-style {
  padding: 270px 0 80px;
}
@media (max-width: 1599.98px) {
  .banner-style1-home5.home7-style {
    padding: 200px 0 0px;
  }
}
@media (max-width: 1199.98px) {
  .banner-style1-home5.home7-style {
    padding: 110px 0 0px;
  }
}
.banner-style1-home5.home7-style .image-content .img {
  left: -70px;
  top: -180px;
}
@media (max-width: 1599.98px) {
  .banner-style1-home5.home7-style .image-content .img {
    top: -60px;
  }
}
@media (max-width: 1519.98px) {
  .banner-style1-home5.home7-style .image-content .img {
    left: 0;
    top: -30px;
  }
}
@media (max-width: 1399.98px) {
  .banner-style1-home5.home7-style .image-content .img {
    top: -20px;
  }
}
@media (max-width: 1299.98px) {
  .banner-style1-home5.home7-style .image-content .img {
    left: 0;
    top: 20px;
  }
}
@media (max-width: 1219.98px) {
  .banner-style1-home5.home7-style .image-content .img {
    top: 60px;
  }
}

.banner-style2-home4 {
  height: 637px;
  position: relative;
}
@media (max-width: 1199.98px) {
  .banner-style2-home4 {
    height: 350px;
  }
}
.banner-style2-home4 .bg {
  background-attachment: fixed;
}

/*** 

====================================================================
    About Us Home 4 Style 1
====================================================================

***/
.about-us-home4 {
  padding: 120px 0 0;
  position: relative;
}
.about-us-home4 .auto-container {
  max-width: 1600px;
  width: 100%;
}
.about-us-home4 .list-style1-home4 li {
  color: #1C1A1D;
  line-height: 40px;
}
.about-us-home4 .list-style1-home4 li:first-child {
  margin-bottom: 20px;
}
.about-us-home4 .image-column {
  position: relative;
}
.about-us-home4 .image-column .bg-circle {
  border: 70px solid rgba(194, 167, 78, 0.1);
  border-radius: 50%;
  height: 610px;
  left: 50px;
  position: absolute;
  top: -60px;
  width: 610px;
}
.about-us-home4 .image-column .img-2 {
  bottom: 0;
  position: absolute;
  right: 0;
  z-index: -1;
}
.about-us-home4 .content-column {
  margin-bottom: 50px;
}

/*** 

====================================================================
    About Us Home 5 Style 1
====================================================================

***/
.about-section-home5 {
  padding: 120px 0;
  position: relative;
}

.about-block-home5 {
  position: relative;
}
.about-block-home5 .inner-box {
  padding-left: 90px;
}
@media (max-width: 1279.98px) {
  .about-block-home5 .inner-box {
    padding-left: 15px;
  }
}
.about-block-home5 .thumb-box img {
  border-radius: 0 300px 300px 0;
}
.about-block-home5 .list-style1-home4 li {
  color: #1C1A1D;
  line-height: 40px;
}
.about-block-home5 .list-style1-home4 li:first-child {
  margin-bottom: 20px;
}

.about-section2-home5 {
  padding: 120px 0;
  position: relative;
}
.about-section2-home5 .list-style1-home4 li {
  color: #1C1A1D;
  line-height: 40px;
}
.about-section2-home5 .list-style1-home4 li:first-child {
  margin-bottom: 20px;
}

.about-block2-home5 {
  position: relative;
}
.about-block2-home5 .inner-box {
  padding-right: 90px;
}
@media (max-width: 1199.98px) {
  .about-block2-home5 .inner-box {
    padding-right: 0;
  }
}
.about-block2-home5 .thumb-box img {
  border-radius: 300px 0 0 300px;
}

/*** 

====================================================================
    Funfact Sectiom Two
====================================================================

***/
.funfact-section-home5 {
  padding: 120px 0;
  position: relative;
}

.counter-block-home5-style .inner-box {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  position: relative;
}
@media (max-width: 767.98px) {
  .counter-block-home5-style .inner-box {
    margin-bottom: 30px;
  }
}
.counter-block-home5-style .inner-box .count-box {
  color: rgba(194, 167, 78, 0.2);
  display: block;
  font-family: "Cormorant";
  font-size: 200px;
  font-weight: 400;
  position: relative;
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .counter-block-home5-style .inner-box .count-box {
    font-size: 110px;
  }
}
.counter-block-home5-style .inner-box .counter-text {
  color: #000000;
  font-family: "Plus Jakarta Sans";
  font-size: 24px;
  font-weight: 400;
  line-height: 30px;
  margin-bottom: 0;
  position: absolute;
  top: 40%;
  right: 0;
}
@media (max-width: 767.98px) {
  .counter-block-home5-style .inner-box .counter-text {
    position: relative;
  }
}
@media (max-width: 575.98px) {
  .counter-block-home5-style .inner-box .counter-text {
    font-size: 18px;
  }
}

/*** 

====================================================================
    Product Banner Home 4 Style 1
====================================================================

***/
.product-banner-hom4-style1 {
  position: relative;
}
.product-banner-hom4-style1 .contant-inner {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  width: 55%;
}
@media (max-width: 1199.98px) {
  .product-banner-hom4-style1 .contant-inner {
    width: 100%;
  }
}
.product-banner-hom4-style1 .contant-inner:before {
  background-color: rgba(194, 167, 78, 0.95);
  bottom: 0;
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
}
.product-banner-hom4-style1 .contant-inner .contant-box {
  padding: 130px 120px;
}
@media (max-width: 1499.98px) {
  .product-banner-hom4-style1 .contant-inner .contant-box {
    padding: 130px 90px;
  }
}
@media (max-width: 575.98px) {
  .product-banner-hom4-style1 .contant-inner .contant-box {
    padding: 80px 30px;
  }
}
.product-banner-hom4-style1 .contant-inner .text {
  font-size: 16px;
  font-weight: 400;
  line-height: 30px;
  margin-bottom: 45px;
}
.product-banner-hom4-style1 .contant-inner .title {
  font-weight: 600;
  font-size: 60px;
  line-height: 73px;
  margin-bottom: 40px;
}
@media (max-width: 991.98px) {
  .product-banner-hom4-style1 .contant-inner .title {
    font-size: 40px;
    line-height: 53px;
  }
}
.product-banner-hom4-style1 .contant-inner .title,
.product-banner-hom4-style1 .contant-inner .text {
  color: var(--theme-color-light);
  position: relative;
}
.product-banner-hom4-style1 .bg-image {
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  left: auto;
  right: 0;
  width: 45%;
}

/*** 

====================================================================
    Why Chose Us Home 4
====================================================================

***/
.whychose-us-home4 {
  background-image: url(../images/background/whychose-us-home4.jpg);
  background-repeat: no-repeat;
  background-size: cover;
  padding: 120px 0;
  position: relative;
}
.whychose-us-home4:before {
  background-color: rgba(194, 167, 78, 0.95);
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.whychose-us-home4 .image-content,
.whychose-us-home4 .whychose-content-box {
  position: relative;
}
.whychose-us-home4 .whychose-content-box {
  padding-left: 50px;
}
@media (max-width: 991.98px) {
  .whychose-us-home4 .whychose-content-box {
    padding-left: 0;
  }
}
.whychose-us-home4 .whychose-icon-box {
  position: relative;
}
.whychose-us-home4 .whychose-icon-box .icon {
  max-width: 65px;
  width: 100%;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.whychose-us-home4 .whychose-icon-box .title {
  color: var(--theme-color-light);
  font-weight: 400;
  font-size: 32px;
  line-height: 39px;
  margin-bottom: 5px;
}
.whychose-us-home4 .whychose-icon-box .text {
  color: var(--theme-color-light);
  line-height: 30px;
}
.whychose-us-home4 .whychose-icon-box:hover .icon {
  -webkit-transform: rotateY(360deg);
          transform: rotateY(360deg);
}

/*** 

====================================================================
    Testimonial Section Five
====================================================================

***/
.testimonial-section-home4 {
  background-image: url(../images/background/testimonial-bg-home4.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 120px 0 90px;
  position: relative;
}
.testimonial-section-home4:before {
  background-color: rgba(194, 167, 78, 0.95);
  bottom: 0;
  content: "";
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}
.testimonial-section-home4 .sec-title {
  margin-bottom: 40px;
}
.testimonial-section-home4 .sec-title .sub-title {
  padding-left: 0;
}
.testimonial-section-home4 .sec-title .sub-title:before {
  display: none;
}
.testimonial-section-home4 .testimonials {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin: 0 auto;
  max-width: 795px;
  overflow: hidden;
  position: relative;
  text-align: center;
  width: 100%;
}
.testimonial-section-home4 .testimonials .swiper-horizontal {
  padding-bottom: 0px;
}
.testimonial-section-home4 .testimonials .testimonial-thumbs {
  margin: 0 auto;
  overflow: hidden;
  padding: 0;
  width: 310px;
}
.testimonial-section-home4 .testimonials .testimonial-thumbs .swiper-wrapper {
  padding: 30px 0;
}
.testimonial-section-home4 .testimonials .testimonial-thumbs .swiper-slide {
  margin: 0;
  text-align: center;
  width: auto !important;
}
.testimonial-section-home4 .testimonials .testimonial-thumbs .swiper-slide img {
  border-radius: 50%;
  cursor: pointer;
  height: 100px;
  text-align: center;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
  width: 100px;
  -webkit-transform: scale(0.8);
          transform: scale(0.8);
}
.testimonial-section-home4 .testimonials .testimonial-thumbs .swiper-slide.swiper-slide-next img {
  -webkit-transform: scale(1);
          transform: scale(1);
}

.testimonial-block-home4 .inner-box {
  text-align: center;
}
.testimonial-block-home4 .inner-box .info-box {
  margin-bottom: 18px;
  margin: 0 auto;
}
.testimonial-block-home4 .inner-box .info-box .name {
  color: var(--theme-color-light);
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 0px;
}
.testimonial-block-home4 .inner-box .info-box .designation {
  color: var(--theme-color-light);
  display: block;
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0px;
}
.testimonial-block-home4 .inner-box .text {
  color: var(--theme-color-light);
  font-size: 20px;
  font-weight: 400;
  line-height: 40px;
  max-width: 895px;
  margin: 0 auto 30px;
}
@media (max-width: 767.98px) {
  .testimonial-block-home4 .inner-box .text {
    font-size: 18px;
    line-height: 26px;
  }
}
.testimonial-block-home4 .inner-box .quote-icon .icon {
  font-size: 52px;
  color: var(--theme-color-light);
  line-height: 1em;
}

/*** 

====================================================================
    Blog Section Four
====================================================================

***/
.blog-section-home4 {
  background-color: rgba(194, 167, 78, 0.1);
  padding: 120px 0 70px;
  position: relative;
}

/*** 

====================================================================
    Blog Section Home Seven
====================================================================

***/
.blog-section-home7 {
  padding: 120px 0 70px;
  position: relative;
}

/*** 

====================================================================
    Team Section Home Seven
====================================================================

***/
.team-section {
  position: relative;
}
.team-section.home7-style {
  padding: 120px 0 190px;
}
.team-section.home7-style .default-dots .owl-dots {
  bottom: -65px;
}

/*** 

====================================================================
    Contact Section Home Seven
====================================================================

***/
.contact-section-two {
  position: relative;
}
.contact-section-two.home7-style {
  padding: 0;
}
.contact-section-two.home7-style .content-column .inner-column {
  background-color: var(--theme-color1);
  padding: 239px 85px 239px;
  position: relative;
  text-align: center;
}
@media (max-width: 1279.98px) {
  .contact-section-two.home7-style .content-column .inner-column {
    padding: 204px 85px;
  }
}
@media (max-width: 1199.98px) {
  .contact-section-two.home7-style .content-column .inner-column {
    padding: 204px 55px;
  }
}
@media (max-width: 991.98px) {
  .contact-section-two.home7-style .content-column .inner-column {
    padding: 70px 40px;
  }
}
.contact-section-two.home7-style .content-column .inner-column .title {
  color: var(--theme-color-light);
  font-size: 48px;
  font-style: normal;
  font-weight: 600;
  line-height: 70px;
  margin-bottom: 25px;
}
.contact-section-two.home7-style .content-column .inner-column .theme-btn {
  background-color: var(--theme-color-light);
  color: var(--theme-color1);
}
.contact-section-two.home7-style .contact-form-two {
  background-color: #F2EFEA;
  border-radius: 0;
  padding: 65px 100px 50px;
}
@media (max-width: 767.98px) {
  .contact-section-two.home7-style .contact-form-two {
    padding: 65px 50px 50px;
  }
}
.contact-section-two.home7-style .contact-form-two .title {
  color: var(--headings-color);
  font-size: 48px;
  font-style: normal;
  font-weight: 600;
  line-height: 70px;
}
.contact-section-two.home7-style .contact-form-two textarea {
  height: auto;
}
.contact-section-two.home7-style .contact-form-two .form-group {
  margin-bottom: 20px;
}
.contact-section-two.home7-style .form-column .inner-column {
  margin: 0;
  padding: 0;
}

/*** 

====================================================================
    Funfact Home Seven Style
====================================================================

***/
.counter-block.home7-style .inner .icon {
  background-color: var(--theme-color-light);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.counter-block.home7-style .inner .icon path {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.counter-block.home7-style .inner:hover .icon {
  background-color: var(--theme-color1);
}
.counter-block.home7-style .inner:hover .icon path {
  fill: var(--theme-color-light);
}

/*** 

====================================================================
    Whychose Us Home Seven Style
====================================================================

***/
.whychose-section-home7 {
  padding: 120px 0 0;
  position: relative;
}
.whychose-section-home7 .leaf-1 {
  left: 0;
  position: absolute;
  top: 50%;
}
.whychose-section-home7 .whychose-us-title {
  color: rgba(255, 255, 255, 0.5);
  font-size: 170px;
  font-style: normal;
  font-weight: 700;
  letter-spacing: 0.2em;
  line-height: 206px;
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  top: 35px;
  width: 100%;
}
@media (max-width: 1199.98px) {
  .whychose-section-home7 .whychose-us-title {
    font-size: 130px;
    line-height: 1;
    text-align: center;
  }
}
@media (max-width: 1279.98px) {
  .whychose-section-home7 .whychose-us-title {
    font-size: 120px;
  }
}
@media (max-width: 991.98px) {
  .whychose-section-home7 .whychose-us-title {
    font-size: 70px;
    top: 10%;
  }
}
@media (max-width: 575.98px) {
  .whychose-section-home7 .whychose-us-title {
    display: none;
  }
}
.whychose-section-home7 .image-column {
  position: relative;
}
.whychose-section-home7 .image-column .inner-content {
  position: relative;
  z-index: 0;
}
.whychose-section-home7 .image-column .img-2,
.whychose-section-home7 .image-column .img-3 {
  bottom: 0;
  position: absolute;
}
.whychose-section-home7 .image-column .img-2 {
  right: 0;
  z-index: -1;
}
.whychose-section-home7 .image-column .img-3 {
  left: 0;
  z-index: -1;
}
.whychose-section-home7 .content-column {
  position: relative;
}
.whychose-section-home7 .content-column .inner-content {
  position: relative;
}
.whychose-section-home7 .list-style1-home7 li {
  color: #1C1A1D;
  line-height: 40px;
}
.whychose-section-home7 .list-style1-home7 li:first-child {
  margin-bottom: 20px;
}

/*** 

====================================================================
    Service Section Home Seven Style
====================================================================

***/
.service-section-home7 {
  position: relative;
}
.service-section-home7 .service-pattrn {
  background-image: url(../images/resource/service-stone.png);
  height: 209px;
  pointer-events: none;
  position: absolute;
  left: 80px;
  top: 76%;
  width: 163px;
}
.service-section-home7 .service-block .inner-box {
  background-color: var(--theme-color-light);
  margin-bottom: 30px;
  position: relative;
}
.service-section-home7 .about-style-home7 {
  padding-left: 75px;
  position: relative;
}
@media (max-width: 991.98px) {
  .service-section-home7 .about-style-home7 {
    padding-left: 0;
  }
}
.service-section-home7 .about-style-home7 .thumb {
  margin-bottom: 45px;
}
.service-section-home7 .about-style-home7 .title {
  color: #1C1A1D;
  font-size: 48px;
  font-weight: 600;
  line-height: 70px;
  margin-bottom: 10px;
}
.service-section-home7 .about-style-home7 .text {
  color: #707070;
  font-size: 18px;
  line-height: 36px;
}
.service-section-home7 .about-style-home7 .theme-btn {
  letter-spacing: 0.1em;
}

/***

====================================================================
    Products details
====================================================================

***/
.product-details .bxslider .image-box {
  position: relative;
  display: block;
  margin-right: 30px;
  margin-bottom: 10px;
}
.product-details .bxslider .image-box img {
  width: 100%;
}
.product-details .bxslider .thumb-box li {
  position: relative;
  display: inline-block;
  float: left;
  margin-right: 10px;
  margin-bottom: 15px;
  width: 100px;
  height: 100px;
}
.product-details .bxslider .thumb-box li:last-child {
  margin: 0px !important;
}
.product-details .bxslider .thumb-box li a {
  position: relative;
  display: inline-block;
}
.product-details .bxslider .thumb-box li a:before {
  position: absolute;
  content: "";
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  opacity: 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.product-details .bxslider .thumb-box li a.active:before {
  opacity: 1;
}
.product-details .bx-wrapper {
  margin-bottom: 30px;
}
.product-details .product-info .product-details__top {
  position: relative;
  display: block;
  margin-top: -8px;
}
.product-details .product-info .product-details__title {
  font-size: 40px;
  line-height: 44px;
  font-weight: 700;
  margin: 0;
}
.product-details .product-info .product-details__title span {
  position: relative;
  display: inline-block;
  color: var(--theme-color1);
  font-size: 20px;
  line-height: 26px;
  font-weight: 700;
  margin-left: 20px;
  letter-spacing: 0;
}
.product-details .product-info .product-details__reveiw {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 22px;
  padding-bottom: 20px;
  margin-bottom: 31px;
  border-bottom: 1px solid #e0e4e8;
}
.product-details .product-info .product-details__reveiw i {
  font-size: 16px;
  color: var(--review-color);
}
.product-details .product-info .product-details__reveiw i + i {
  margin-left: 4px;
}
.product-details .product-info .product-details__reveiw span {
  position: relative;
  top: 1px;
  line-height: 1;
  font-size: 16px;
  color: var(--theme-color1);
  margin-left: 18px;
}
.product-details .product-info .product-details__quantity-title {
  margin: 0;
  color: #222;
  font-size: 20px;
  line-height: 30px;
  font-weight: 700;
  margin-right: 20px;
}
.product-details .product-info .product-details__buttons {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 30px;
  margin-top: 40px;
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__buttons {
    display: block;
  }
}
.product-details .product-info .product-details__buttons-1 {
  position: relative;
  display: block;
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__buttons-1 {
    margin-bottom: 10px;
    margin-right: 10px;
  }
}
.product-details .product-info .product-details__buttons-2 {
  position: relative;
  display: block;
  margin-left: 10px;
}
.product-details .product-info .product-details__buttons-2 .thm-btn {
  background-color: var(--theme-color2);
}
.product-details .product-info .product-details__buttons-2 .thm-btn:before {
  background-color: var(--theme-color1);
}
.product-details .product-info .product-details__buttons-2 .thm-btn:after {
  background-color: var(--theme-color1);
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__buttons-2 {
    margin-left: 0;
    margin-top: 0;
  }
}
.product-details .product-info .product-details__social {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.product-details .product-info .product-details__social .title {
  position: relative;
  display: block;
}
.product-details .product-info .product-details__social .title h3 {
  color: #222;
  font-size: 20px;
  line-height: 20px;
  font-weight: 700;
}
.product-details .product-info .product-details__social .social-icon-one {
  margin-left: 30px;
}
@media only screen and (max-width: 767px) {
  .product-details .product-info .product-details__social {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: start;
  }
  .product-details .product-info .product-details__social .social-icon-one {
    margin-left: 0;
  }
}

.product-details__quantity {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-top: 20px;
}
.product-details__quantity .quantity-box {
  position: relative;
  width: 98px;
  border-radius: 10px;
  height: 60px;
}
.product-details__quantity .quantity-box input {
  width: 98px;
  border-radius: 10px;
  height: 60px;
  border: 1px solid #e0e4e8;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  padding-left: 30px;
  outline: none;
  font-size: 18px;
  font-weight: 500;
  color: var(--theme-color1);
}
.product-details__quantity .quantity-box button {
  width: 29px;
  height: 29px;
  background-color: transparent;
  color: var(--theme-color-light);
  font-size: 8px;
  position: absolute;
  top: 1px;
  right: 1px;
  background-color: var(--theme-color1);
  border: none;
  border-left: 1px solid #e0e4e8;
  border-top-right-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  outline: none;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.product-details__quantity .quantity-box button:hover {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}
.product-details__quantity .quantity-box button.sub {
  bottom: 1px;
  top: auto;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 10px;
}

.product-discription {
  position: relative;
  display: block;
}
.product-discription .product-description__title {
  font-size: 30px;
  margin-bottom: 27px;
}
.product-discription .product-description__text1 {
  font-size: 16px;
  line-height: 30px;
  margin: 0;
}
.product-discription .product-description__list {
  position: relative;
  display: block;
  margin-top: 30px;
  margin-bottom: 30px;
}
.product-discription .product-description__list ul {
  position: relative;
  display: block;
}
.product-discription .product-description__list ul li {
  position: relative;
  display: block;
  margin-bottom: 2px;
}
.product-discription .product-description__list ul li:last-child {
  margin-bottom: 0px;
}
.product-discription .product-description__list ul li p {
  margin: 0;
  font-weight: 600;
  color: var(--headings-color);
}
.product-discription .product-description__list ul li p span:before {
  position: relative;
  display: inline-block;
  color: var(--theme-color1);
  font-size: 17px;
  line-height: 17px;
  margin-right: 11px;
  top: 2px;
}
.product-discription .tabs-content .text p {
  margin-bottom: 17px;
}
.product-discription .tabs-content .text p:last-child {
  margin-bottom: 0px;
}
.product-discription .tab-btn-box {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 60px;
}
.product-discription .tab-btn-box:before {
  position: absolute;
  content: "";
  background-color: #e1e8e4;
  width: 100%;
  height: 1px;
  left: 0px;
  top: 28px;
}
.product-discription .tab-btn-box .tab-btns li {
  position: relative;
  display: inline-block;
  font-size: 14px;
  text-transform: uppercase;
  color: #1e2434;
  text-align: center;
  padding: 14px 30px;
  background-color: #fff;
  border: 1px solid #e1e8e4;
  cursor: pointer;
  margin: 0px 8.5px;
  margin-bottom: 15px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.product-discription .tab-btn-box .tab-btns li.active-btn {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}
.product-discription .tab-btn-box .tab-btns li:last-child {
  margin-bottom: 0;
}
.product-discription .single-comment-box .inner-box {
  position: relative;
  display: block;
  background-color: #f4f5f4;
  padding: 34px 30px 34px 125px;
}
.product-discription .single-comment-box .inner-box .comment-thumb {
  position: absolute;
  left: 30px;
  top: 40px;
  border-radius: 50%;
  width: 80px;
}
.product-discription .single-comment-box .inner-box .comment-thumb img {
  width: 100%;
  border-radius: 50%;
}
.product-discription .single-comment-box .inner-box .rating {
  position: relative;
  display: block;
  margin-bottom: 2px;
}
.product-discription .single-comment-box .inner-box .rating li {
  position: relative;
  display: inline-block;
  font-size: 12px;
  float: left;
  margin-right: 4px;
  color: #fdc009;
}
.product-discription .single-comment-box .inner-box .rating li:last-child {
  margin: 0px !important;
}
.product-discription .single-comment-box .inner-box h5 {
  display: block;
  font-size: 16px;
  line-height: 26px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 16px;
}
.product-discription .single-comment-box .inner-box h5 span {
  font-weight: 400;
  text-transform: capitalize;
}
.product-discription .customer-comment {
  position: relative;
  display: block;
  margin-bottom: 60px;
}
.product-discription .comment-box {
  position: relative;
  display: block;
  background-color: #fff;
  padding: 51px 60px 60px 60px;
  -webkit-box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
}
.product-discription .comment-box h3 {
  display: block;
  font-size: 24px;
  line-height: 34px;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 23px;
}
.product-discription .comment-box .form-group {
  position: relative;
  margin-bottom: 15px;
}
.product-discription .comment-box .form-group label {
  position: relative;
  display: block;
  font-size: 18px;
  line-height: 28px;
  color: #707582;
  margin-bottom: 8px;
}
.product-discription .comment-box .column:last-child .form-group {
  margin-bottom: 0px;
}
.product-discription .comment-box .review-box {
  position: relative;
  display: block;
  margin-top: 8px;
}
.product-discription .comment-box .review-box p {
  position: relative;
  float: left;
  margin-right: 10px;
}
.product-discription .comment-box .review-box .rating {
  position: relative;
  float: left;
}
.product-discription .comment-box .review-box .rating li {
  position: relative;
  display: inline-block;
  font-size: 14px;
  line-height: 28px;
  float: left;
  margin-right: 4px;
  color: #fdc009;
}
.product-discription .comment-box .review-box .rating li:last-child {
  margin: 0px !important;
}
.product-discription .comment-box .custom-controls-stacked {
  position: relative;
  float: left;
}
.product-discription .comment-box input::-webkit-input-placeholder, .product-discription .comment-box textarea::-webkit-input-placeholder {
  color: var(--text-color);
}
.product-discription .comment-box input::-moz-placeholder, .product-discription .comment-box textarea::-moz-placeholder {
  color: var(--text-color);
}
.product-discription .comment-box input:-ms-input-placeholder, .product-discription .comment-box textarea:-ms-input-placeholder {
  color: var(--text-color);
}
.product-discription .comment-box input::-ms-input-placeholder, .product-discription .comment-box textarea::-ms-input-placeholder {
  color: var(--text-color);
}
.product-discription .comment-box input::placeholder,
.product-discription .comment-box textarea::placeholder {
  color: var(--text-color);
}

.related-product h3 {
  margin-bottom: 30px;
}

@media only screen and (max-width: 767px) {
  .product-details__img {
    margin-bottom: 50px;
  }
  .product-details__title span {
    margin-left: 0;
    display: block;
  }
  .product-details__buttons {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: baseline;
        -ms-flex-align: baseline;
            align-items: baseline;
  }
  .product-details__buttons-2 {
    margin-left: 0;
    margin-top: 10px;
  }
  .product-details__social-link {
    margin-left: 0;
    margin-top: 20px;
  }
}
/***

====================================================================
 Categories Section
====================================================================

***/
.categories-section {
  position: relative;
  padding: 100px 0 70px;
}
.categories-section .bg-pattern {
  position: absolute;
  left: 0;
  top: -220px;
  width: 100%;
  height: 100%;
  content: "";
  background-repeat: no-repeat;
  background-position: left top;
  z-index: -1;
}
.categories-section:before {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 300px;
  width: 100%;
  background: var(--theme-color3);
  content: "";
  z-index: 1;
}
.categories-section:after {
  position: absolute;
  left: 0;
  bottom: -50px;
  height: 70px;
  width: 100%;
  z-index: 2;
  content: "";
  background-repeat: no-repeat;
  background-position: center bottom;
}

.category-block {
  position: relative;
  margin-bottom: 30px;
  z-index: 9;
}
.category-block .inner-box {
  position: relative;
  text-align: center;
  background: #ffffff;
  padding: 40px 30px 30px;
  border-radius: 10px;
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block .inner-box:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 190px;
  background-repeat: no-repeat;
  background-position: center bottom;
  content: "";
}
.category-block .inner-box:hover {
  -webkit-transform: translateY(-20px);
  transform: translateY(-20px);
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.category-block .inner-box:hover .image img {
  -webkit-transform: rotate(10deg) scale(1.2);
  transform: rotate(10deg) scale(1.2);
}
.category-block .image {
  position: relative;
  display: inline-block;
  height: 180px;
  width: 180px;
  overflow: hidden;
  border-radius: 50%;
  margin-bottom: 15px;
}
.category-block .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block h4 {
  font-size: 20px;
  color: var(--theme-color3);
  font-weight: 700;
  margin-bottom: 10px;
}
.category-block h4 a {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block h4 a:hover {
  color: var(--theme-color1);
}
.category-block p {
  font-size: 14px;
  color: #797f7d;
  line-height: 26px;
  margin-bottom: 0px;
}

/***

====================================================================
    Categories Section Two
====================================================================

***/
.categories-section-two {
  position: relative;
  padding: 120px 0 90px;
}

.category-block-two {
  position: relative;
  padding-top: 70px;
  margin-bottom: 30px;
  z-index: 9;
}
.category-block-two .inner-box {
  position: relative;
  text-align: center;
  background: #ffffff;
  border-radius: 10px;
  margin: 0 auto;
  padding: 18px;
}
.category-block-two .inner-box:before {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 60px;
  width: 100%;
  border-radius: 10px;
  background: #e3eee5;
  content: "";
}
.category-block-two .inner-box:hover .image:before {
  left: 100%;
  -webkit-transition: all 1000ms ease;
  transition: all 1000ms ease;
}
.category-block-two .inner-box:hover .image img {
  -webkit-transform: scale(0.9);
  transform: scale(0.9);
}
.category-block-two .inner-box:hover h4 a {
  color: var(--theme-color1);
}
.category-block-two .content {
  position: relative;
  background: #ffffff;
  border-radius: 10px;
  padding: 0 40px 40px;
  z-index: 1;
  -webkit-box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
  box-shadow: 0 10px 60px rgba(0, 0, 0, 0.07);
}
.category-block-two .content:before {
  position: absolute;
  top: -88px;
  left: 0;
  width: 180px;
  height: 180px;
  right: 0;
  margin: 0 auto;
  border-radius: 50%;
  background: #e3eee5;
  content: "";
}
.category-block-two .content:after {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  font-size: 30px;
  line-height: 1em;
  color: #e8f3ea;
  height: 15px;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  content: attr(data-text);
}
.category-block-two .image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  margin-bottom: 25px;
  margin-top: -70px;
}
.category-block-two .image img {
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block-two .image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 120%;
  width: 100%;
  background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(rgb(255, 255, 255)));
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#00ffffff",GradientType=1 );
  content: "";
  opacity: 0.3;
  left: -100%;
  pointer-events: none;
  z-index: 1;
}
.category-block-two h4 {
  font-size: 22px;
  color: var(--theme-color3);
  font-weight: 700;
  margin-bottom: 15px;
}
.category-block-two h4 a {
  color: var(--theme-color3);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block-two p {
  font-size: 16px;
  color: #797f7d;
  line-height: 26px;
  margin-bottom: 0px;
}
.category-block-two .link {
  position: relative;
  display: inline-block;
  height: 50px;
  width: 50px;
  background: #e8f3ea;
  border-radius: 50%;
  line-height: 50px;
  margin-top: 25px;
  color: #608174;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.category-block-two .link:hover {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
.category-block-two.child-two .link {
  background: #f0e2e3;
}
.category-block-two.child-two .inner-box:before {
  background: #f0e2e3;
}
.category-block-two.child-two .inner-box:hover h4 a {
  color: #e69da2;
}
.category-block-two.child-two .content:before {
  background: #f0e2e3;
}
.category-block-two.child-two .content:after {
  color: #f0e2e3;
}
.category-block-two.child-three .link {
  background: #f1ede1;
}
.category-block-two.child-three .inner-box:before {
  background: #f1ede1;
}
.category-block-two.child-three .inner-box:hover h4 a {
  color: #c9b579;
}
.category-block-two.child-three .content:before {
  background: #f1ede1;
}
.category-block-two.child-three .content:after {
  color: #f1ede1;
}

/***

====================================================================
    Products Section
====================================================================

***/
.products-section {
  position: relative;
  padding: 120px 0;
}

.products-section .bg-image {
  position: absolute;
  left: 0;
  top: 0;
  height: 670px;
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  background-attachment: fixed;
}

.products-section .bg-image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-color: #113629;
  opacity: 0.7;
  content: "";
}

.products-section .bg-image:after {
  position: absolute;
  left: 0;
  bottom: 0;
  height: 50px;
  width: 100%;
  background-position: center bottom;
  content: "";
}

.products-section .sec-title h2 {
  font-size: 60px;
}

.products-section .sec-title .theme-btn {
  margin-top: 30px;
}

.products-box {
  max-width: 1530px;
  position: relative;
  padding: 120px 60px 90px;
  margin: 120px auto 0;
  background-color: #f7f5ee;
  overflow: hidden;
  border-radius: 10px;
}

.products-box:before {
  position: absolute;
  left: -90px;
  bottom: 0;
  height: 70%;
  width: 100%;
  content: "";
}

.products-box .sec-title {
  margin-bottom: 30px;
}

.products-box .outer-box {
  position: relative;
  padding-right: 400px;
}

.product-block-two {
  position: relative;
  margin-bottom: 30px;
}

.product-block-two .inner-box {
  position: relative;
  border: 2px solid transparent;
  border-radius: 10px;
  background: #ffffff;
  padding: 20px 20px;
  padding-left: 150px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  min-height: 150px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block-two .inner-box:hover {
  border: 2px solid var(--theme-color1);
}

.product-block-two .image {
  position: absolute;
  left: 20px;
  top: 20px;
  border-radius: 50%;
  overflow: hidden;
  height: 110px;
  width: 110px;
  border: 1px solid #e4e1d6;
  margin-bottom: 0px;
}

.product-block-two .image img {
  width: auto;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block-two .inner-box:hover .image img {
  -webkit-transform: scale(-1) rotate(180deg);
  transform: scale(-1) rotate(180deg);
}

.product-block-two h4 {
  display: block;
  font-size: 22px;
  color: var(--theme-color3);
  font-weight: 700;
  margin-bottom: 5px;
}

.product-block-two h4 a {
  color: var(--theme-color3);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block-two .price {
  display: block;
  font-size: 16px;
  line-height: 26px;
  color: var(--theme-color1);
  font-weight: 600;
}

.product-block-two .price del {
  display: inline-block;
  margin-left: 15px;
  font-size: 16px;
  color: #ff0000;
  line-height: 27px;
  opacity: 0.3;
}

.product-block-two .rating {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 14px;
  color: #ffc737;
}

.products-carousel .owl-nav {
  display: none;
}

.products-carousel .owl-dots {
  position: absolute;
  right: 0;
  top: -80px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 8;
}

.products-carousel .owl-dot {
  position: relative;
  height: 7px;
  width: 7px;
  display: block;
  background: #879d91;
  margin-right: 5px;
  border-radius: 5px;
  display: block;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.products-carousel .owl-dot.active {
  background: var(--theme-color3);
}

/***

====================================================================
    Featured Products
====================================================================

***/
.featured-products {
  position: relative;
  padding: 120px 0 90px;
}

.featured-products .bg-shape {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-size: 700px;
}

/*=== Mixitup Gallery ===*/
.featured-products .filters {
  margin-bottom: 40px;
  text-align: center;
}

.featured-products .filters .filter-tabs {
  position: relative;
  display: inline-block;
}

.featured-products .filters li {
  position: relative;
  display: inline-block;
  line-height: 24px;
  padding: 0px 2px 10px;
  cursor: pointer;
  color: #797f7d;
  font-weight: 500;
  font-size: 18px;
  margin: 0 12px 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.featured-products .filters li:last-child {
  margin-right: 0;
}

.featured-products .filters .filter.active,
.featured-products .filters .filter:hover {
  color: var(--theme-color1);
}

.featured-products .filters li:before {
  position: absolute;
  left: 0;
  bottom: 8px;
  height: 2px;
  width: 100%;
  content: "";
  background-color: #ffc737;
  -webkit-transform: scale(0, 1);
  transform: scale(0, 1);
  -webkit-transform-origin: top right;
  transform-origin: top right;
  -webkit-transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  -webkit-transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1);
  transition: transform 500ms cubic-bezier(0.86, 0, 0.07, 1), -webkit-transform 500ms cubic-bezier(0.86, 0, 0.07, 1); /* easeInOutQuint */
  -webkit-transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
  transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1); /* easeInOutQuint */
}

.featured-products .filters li.active:before,
.featured-products .filters li:hover:before {
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1);
  -webkit-transform-origin: bottom left;
  transform-origin: bottom left;
}

.product-block {
  position: relative;
  margin-bottom: 30px;
}

.product-block.mix {
  display: none;
}

.product-block .inner-box {
  position: relative;
  border: 2px solid #e4e1d5;
  border-radius: 10px;
  text-align: center;
  background: #ffffff;
  overflow: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .inner-box:hover {
  border: 2px solid var(--theme-color1);
  -webkit-box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.1);
}

.product-block .image {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
  margin-bottom: 0px;
}

.product-block .image:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(rgb(255, 255, 255)));
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgb(255, 255, 255) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ffffff", endColorstr="#00ffffff",GradientType=1 );
  content: "";
  opacity: 0.3;
  left: -100%;
  pointer-events: none;
  z-index: 1;
}

.product-block .inner-box:hover .image:before {
  left: 100%;
  -webkit-transition: all 1000ms ease;
  transition: all 1000ms ease;
}

.product-block .image img {
  width: auto;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .inner-box:hover .image img {
  -webkit-transform: scale(0.9);
  transform: scale(0.9);
}

.product-block .content {
  position: relative;
  padding: 30px 30px 30px;
}

.product-block h4 {
  display: block;
  font-size: 22px;
  color: var(--theme-color1);
  font-weight: 700;
  margin-bottom: 5px;
}

.product-block h4 a {
  color: var(--theme-color1);
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block h4 a:hover {
  color: var(--theme-color1);
}

.product-block .price {
  display: block;
  font-size: 16px;
  line-height: 26px;
  color: var(--text-gray-silver);
  font-weight: 600;
}

.product-block .price del {
  display: inline-block;
  margin-left: 15px;
  font-size: 16px;
  color: #ff0000;
  line-height: 27px;
  opacity: 0.3;
}

.product-block .rating {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 14px;
  color: #ffc737;
}

.product-block .tag {
  position: absolute;
  top: 30px;
  left: 20px;
  font-size: 14px;
  line-height: 23px;
  color: #ffffff;
  background: #FD5F5C;
  font-weight: 400;
  padding: 0 12px;
  border-radius: 3px;
  z-index: 9;
  font-style: italic;
  text-transform: uppercase;
}

.product-block .icon-box {
  position: absolute;
  right: 20px;
  top: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .inner-box:hover .icon-box {
  top: 20px;
  opacity: 1;
  visibility: visible;
  -webkit-transition-delay: 300ms;
  transition-delay: 300ms;
}

.product-block .ui-btn {
  position: relative;
  display: block;
  height: 40px;
  width: 40px;
  font-size: 14px;
  line-height: 40px;
  text-align: center;
  color: #ffffff;
  z-index: 9;
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
  cursor: pointer;
  border-radius: 50px;
  margin-bottom: 10px;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}

.product-block .ui-btn:hover {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}

.product-block .cat {
  display: block;
  font-size: 18px;
  color: #707070;
  font-style: italic;
  margin-bottom: 5px;
}

/*
 * shop-catalog-layouts.scss
 * -----------------------------------------------
*/
table.tbl-shopping-cart .product-thumbnail, table.cart-total .product-thumbnail {
  min-width: 64px;
}
table.tbl-shopping-cart img, table.cart-total img {
  width: 64px;
  -webkit-box-shadow: none;
          box-shadow: none;
}
table.tbl-shopping-cart th,
table.tbl-shopping-cart td, table.cart-total th,
table.cart-total td {
  vertical-align: middle;
  border-left: 1px solid #e3e3e3;
  padding: 20px 30px;
}
table.tbl-shopping-cart .product-name a, table.cart-total .product-name a {
  color: var(--headings-color);
}
table.tbl-shopping-cart .product-name .variation, table.cart-total .product-name .variation {
  font-size: 0.9rem;
  list-style: none;
}
table.tbl-shopping-cart .product-remove a, table.cart-total .product-remove a {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 24px;
  -webkit-transition: all 300ms ease-out;
  transition: all 300ms ease-out;
  background-color: #757575;
  color: #ffffff;
  text-align: center;
}
table.tbl-shopping-cart .coupon-form .apply-button, table.cart-total .coupon-form .apply-button {
  position: relative;
  display: inline-block;
  color: #1e2434;
  background: #f4f5f4;
  padding: 15px 29px;
  cursor: pointer;
  text-transform: uppercase;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
table.tbl-shopping-cart .coupon-form .apply-button:hover, table.cart-total .coupon-form .apply-button:hover {
  background-color: var(--theme-color1);
  color: var(--text-color-bg-theme-color1);
}

table.tbl-shopping-cart > thead > tr > th,
table.tbl-shopping-cart > tbody > tr > th,
table.tbl-shopping-cart > tfoot > tr > th {
  color: #444;
}

.cart-form .form-control::-webkit-input-placeholder {
  color: var(--text-color);
}

.cart-form .form-control::-moz-placeholder {
  color: var(--text-color);
}

.cart-form .form-control:-ms-input-placeholder {
  color: var(--text-color);
}

.cart-form .form-control::-ms-input-placeholder {
  color: var(--text-color);
}

.cart-form .form-control::placeholder {
  color: var(--text-color);
}
.cart-form .form-control {
  color: var(--text-color);
}

.page-cart .form-control::-webkit-input-placeholder {
  color: var(--text-color);
}

.page-cart .form-control::-moz-placeholder {
  color: var(--text-color);
}

.page-cart .form-control:-ms-input-placeholder {
  color: var(--text-color);
}

.page-cart .form-control::-ms-input-placeholder {
  color: var(--text-color);
}

.page-cart .form-control::placeholder {
  color: var(--text-color);
}

.payment-method .accordion-box .block {
  background: #f4f5f4;
  -webkit-box-shadow: none;
          box-shadow: none;
  margin-bottom: 20px;
}
.payment-method .accordion-box .block .acc-content .payment-info {
  position: relative;
  display: block;
  background-color: #fff;
  padding: 30px 30px 10px 30px;
}
.payment-method .accordion-box .block:last-child {
  margin-bottom: 0px;
}
.payment-method .accordion-box .block .acc-btn {
  padding: 19px 30px 22px 30px;
}
.payment-method .accordion-box .block .acc-btn .icon-outer {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  position: absolute;
  top: 50%;
  right: 30px;
  height: auto;
  /* line-height: 65px; */
  font-size: 28px;
  color: #1e2434;
}
.payment-method .accordion-box .block .acc-btn.active .icon-outer {
  color: var(--text-color-bg-theme-color1);
}
.payment-method .payment-method h3 {
  margin-bottom: 32px;
}

.checkout-form .form-control::-webkit-input-placeholder {
  color: var(--text-color);
}

.checkout-form .form-control::-moz-placeholder {
  color: var(--text-color);
}

.checkout-form .form-control:-ms-input-placeholder {
  color: var(--text-color);
}

.checkout-form .form-control::-ms-input-placeholder {
  color: var(--text-color);
}

.checkout-form .form-control::placeholder {
  color: var(--text-color);
}
.checkout-form textarea {
  height: auto;
  color: var(--text-color);
}

.shop-sidebar {
  position: relative;
  display: block;
}
.shop-sidebar .sidebar-search {
  margin-bottom: 30px;
}
.shop-sidebar .sidebar-search .search-form .form-group {
  position: relative;
  margin: 0px;
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search] {
  position: relative;
  width: 100%;
  height: 52px;
  background-color: var(--theme-light-background);
  border: 1px solid var(--theme-light-background);
  border-radius: 5px;
  color: #646578;
  padding: 10px 60px 10px 20px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search]::-webkit-input-placeholder {
  color: var(--text-color);
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search]::-moz-placeholder {
  color: var(--text-color);
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search]:-ms-input-placeholder {
  color: var(--text-color);
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search]::-ms-input-placeholder {
  color: var(--text-color);
}
.shop-sidebar .sidebar-search .search-form .form-group input[type=search]::placeholder {
  color: var(--text-color);
}
.shop-sidebar .sidebar-search .search-form .form-group button {
  position: absolute;
  display: inline-block;
  top: 5px;
  right: 5px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  text-align: center;
  font-size: 18px;
  color: var(--theme-color-light);
  background-color: var(--theme-color1);
  cursor: pointer;
  border-radius: 3px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.shop-sidebar .sidebar-search .search-form .form-group button:hover {
  color: var(--theme-color-light);
  background-color: var(--theme-color4);
}
.shop-sidebar .sidebar-widget {
  position: relative;
  display: block;
  background-color: var(--theme-light-background);
  padding: 35px 30px 37px 30px;
  border-radius: 5px;
  margin-bottom: 30px;
}
.shop-sidebar .sidebar-widget:last-child {
  margin-bottom: 0px;
}
.shop-sidebar .sidebar-widget .widget-title {
  position: relative;
  display: block;
  margin-bottom: 16px;
}
.shop-sidebar .category-widget .category-list li {
  position: relative;
  display: block;
  margin-bottom: 14px;
}
.shop-sidebar .category-widget .category-list li:last-child {
  margin-bottom: 0px;
}
.shop-sidebar .category-widget .category-list li a {
  position: relative;
  display: inline-block;
  color: #646578;
  font-weight: 400;
  padding-left: 20px;
}
.shop-sidebar .category-widget .category-list li a:before {
  position: absolute;
  content: "\f0da";
  font-family: "Font Awesome 6 Pro";
  left: 0px;
  top: 0px;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color2);
}
.shop-sidebar .category-widget .category-list li a:hover {
  color: var(--theme-color1);
}
.shop-sidebar .price-filters .widget-title {
  margin-bottom: 28px;
}
.shop-sidebar .post-widget {
  padding-bottom: 9px;
}
.shop-sidebar .post-widget .post {
  position: relative;
  padding-left: 90px;
  padding-bottom: 24px;
  margin-bottom: 23px;
  min-height: 108px;
  border-bottom: 1px solid #e1e1e1;
}
.shop-sidebar .post-widget .post:last-child {
  margin-bottom: 0px;
  border-bottom: none;
}
.shop-sidebar .post-widget .post .post-thumb {
  position: absolute;
  left: 0px;
  top: 7px;
  width: 70px;
  height: 70px;
  border: 1px solid #d0d4dd;
  border-radius: 5px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.shop-sidebar .post-widget .post .post-thumb img {
  width: 100%;
  border-radius: 5px;
}
.shop-sidebar .post-widget .post a {
  position: relative;
  display: inline-block;
  font-size: 16px;
  line-height: 26px;
  color: #646578;
  margin-bottom: 7px;
}
.shop-sidebar .post-widget .post .price {
  position: relative;
  display: block;
  font-size: 14px;
  line-height: 24px;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  color: #0a267a;
}
.shop-sidebar .post-widget .post:hover .post-thumb {
  border-color: var(--theme-color1);
}
.shop-sidebar .post-widget .post:hover a {
  color: var(--theme-color1);
}

.range-slider {
  position: relative;
}
.range-slider .title {
  line-height: 26px;
  position: relative;
  display: inline-block;
  margin-right: 4px;
}
.range-slider .title:before {
  position: absolute;
  content: "$";
  left: -5px;
  top: -19px;
  color: #646578;
  font-size: 18px;
}
.range-slider p {
  position: relative;
  display: inline-block;
  color: #646578;
  margin-right: 10px !important;
}
.range-slider .input {
  color: #646578;
  max-width: 75px;
  font-size: 18px;
  margin-top: 5px;
  position: relative;
  display: inline-block;
}
.range-slider .input input {
  background: none;
  color: #646578;
  font-size: 15px;
  text-align: left;
}
.range-slider .ui-widget.ui-widget-content {
  height: 4px;
  border: none;
  margin-bottom: 14px;
  background-color: #d0d4dd;
  border-radius: 2px;
}
.range-slider .ui-slider .ui-slider-range {
  top: 0px;
  height: 4px;
  background-color: var(--theme-color1);
}
.range-slider .ui-state-default {
  top: -5px;
  width: 14px;
  height: 14px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  margin-left: 0px;
  background-color: var(--theme-color1);
  position: absolute;
}
.range-slider .ui-state-default:before {
  position: absolute;
  content: "";
  background-color: #ffffff;
  width: 6px;
  height: 6px;
  left: 4px;
  top: 4px;
  border-radius: 50%;
}
.range-slider .ui-widget-content .ui-state-default {
  top: -5px;
  width: 14px;
  height: 14px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  margin-left: 0px;
  background-color: var(--theme-color1);
}
.range-slider .ui-widget-content .ui-state-default:before {
  position: absolute;
  content: "";
  background-color: #ffffff;
  width: 6px;
  height: 6px;
  left: 4px;
  top: 4px;
  border-radius: 50%;
}
.range-slider input[type=submit] {
  position: relative;
  display: block;
  background: var(--theme-color1);
  float: right;
  text-align: center;
  border: none;
  font-size: 14px;
  font-weight: 600;
  margin-top: 0;
  text-transform: capitalize;
  cursor: pointer;
  padding: 7px 20px;
  border-radius: 10px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.range-slider input[type=submit]:hover {
  color: #fff;
  background-color: var(--theme-color2);
}

.range-slider .price-text {
  position: relative;
}

.horizontal-slider {
  background-color: #efefef;
  border: none;
  border-radius: 2px;
  height: 4px;
}

.horizontal-slider .example-track {
  height: 4px;
}

.example-track.example-track-1 {
  background: var(--theme-color1);
}

.example-track.example-track-2 {
  background: #d0d4dd;
}

.horizontal-slider .example-thumb.example-thumb-0 {
  left: 0;
  right: auto;
}

.horizontal-slider .example-thumb.example-thumb-1 {
  left: auto;
  right: 0;
}

.horizontal-slider .example-thumb {
  background-color: var(--theme-color1);
  border-radius: 50%;
  border: 5px solid #fff;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
          box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  cursor: pointer;
  color: #fff;
  font-size: 12px;
  height: 45px;
  line-height: 35px;
  outline: 0;
  text-align: center;
  top: -20px;
  width: 45px;
}

/*** 

====================================================================
Page Title
====================================================================

***/
/* 
@-webkit-keyframes "ripple" {
  70% {
    -webkit-box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}
@keyframes "ripple" {
  70% {
    -webkit-box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 70px rgba(255, 255, 255, 0);
  }
  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
} */
.page-title {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  padding: 175px 0 275px;
}
@media (max-width: 1199.98px) {
  .page-title {
    padding: 175px 0;
  }
}
.page-title:before {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background: #131313;
  opacity: 0.7;
  content: "";
}
.page-title .title {
  font-size: 64px;
  color: #ffffff;
  margin-bottom: 17px;
}
.page-title .text {
  position: relative;
  color: #ffffff;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0.05em;
  max-width: 520px;
}
.page-title .image-curve {
  background-image: url(../images/main-slider/slide-shape-bottom.png);
  position: absolute;
  width: 100%;
  height: 69px;
  left: 0;
  bottom: 0;
  z-index: 1;
}
.page-title .large-title {
  color: rgba(255, 255, 255, 0.1);
  bottom: -50px;
  font-size: 216px;
  line-height: normal;
  font-family: "Cormorant";
  font-style: normal;
  font-weight: 700;
  left: 0;
  position: absolute;
  right: 0;
  text-align: center;
  text-transform: uppercase;
}
@media (max-width: 1199.98px) {
  .page-title .large-title {
    bottom: -30px;
    font-size: 150px;
  }
}
@media (max-width: 767.98px) {
  .page-title .large-title {
    display: none;
  }
}

.page-breadcrumb {
  position: relative;
  margin-top: 5px;
}
.page-breadcrumb li {
  position: relative;
  display: inline-block;
  margin-right: 12px;
  padding-right: 13px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 400;
  text-transform: capitalize;
}
.page-breadcrumb li:after {
  position: absolute;
  content: "\f105";
  right: -6px;
  top: 1px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 900;
  font-family: "Font Awesome 6 Pro";
  color: #ffffff;
}
.page-breadcrumb li:last-child {
  padding-right: 0px;
  margin-right: 0px;
}
.page-breadcrumb li:last-child::after {
  display: none;
}
.page-breadcrumb li a {
  color: var(--theme-color-light);
  font-weight: 500;
  text-transform: capitalize;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
}
.page-breadcrumb li a:hover {
  color: var(--theme-color-light);
}

.page-title.style-two {
  background-position: center center;
}
.page-title.style-two .page-breadcrumb-outer {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 10px 0;
  -webkit-transform: translateY(100%);
  transform: translateY(100%);
  z-index: 8;
}
.page-title.style-two .page-breadcrumb li {
  color: rgb(7, 7, 16);
  font-weight: 600;
}
.page-title.style-two .page-breadcrumb li:after {
  color: rgb(7, 7, 16);
}
.page-title.style-two .page-breadcrumb li a {
  color: rgba(7, 7, 16, 0.6);
}
.page-title.style-two .page-breadcrumb li a:hover {
  color: rgb(7, 7, 16);
}

.play-now {
  position: relative;
  display: block;
  z-index: 9;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.play-now .icon {
  position: relative;
  display: inline-block;
  height: 70px;
  width: 70px;
  text-align: center;
  line-height: 70px;
  background-color: #ffffff;
  color: #ff6d2e;
  z-index: 1;
  padding-left: 5px;
  font-size: 14px;
  display: block;
  border-radius: 50%;
  -webkit-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -ms-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -o-box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  box-shadow: 0 0px 10px 0 rgba(255, 255, 255, 0.3);
  -webkit-transform-origin: center;
  transform-origin: center;
}
.play-now .ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
}
.play-now .ripple:before {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  content: "";
  position: absolute;
}
.play-now .ripple:after {
  position: absolute;
  top: 50%;
  left: 50%;
  height: 70px;
  width: 70px;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  -webkit-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -ms-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -o-box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.6);
  -webkit-animation: ripple 3s infinite;
  animation: ripple 3s infinite;
  -webkit-animation-delay: 0.6s;
  animation-delay: 0.6s;
  content: "";
  position: absolute;
}

.background-image {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  background-repeat: repeat;
  background-position: center;
  background-size: cover;
}

.error-page__inner {
  position: relative;
  display: block;
  text-align: center;
}
.error-page__inner .error-page__title-box {
  position: relative;
  display: block;
}
.error-page__inner .error-page__title {
  position: relative;
  display: inline-block;
  font-size: 280px;
  line-height: 280px;
  margin-bottom: 0;
  color: var(--theme-color1);
}
.error-page__inner .error-page__sub-title {
  font-size: 40px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  line-height: 50px;
  margin-top: -16px;
}
.error-page__inner .error-page__text {
  font-size: 20px;
  line-height: 30px;
  text-align: center;
}
.error-page__inner .error-page__form {
  position: relative;
  display: block;
  margin: 42px auto 20px;
}
.error-page__inner .error-page__form input[type=search] {
  height: 60px;
  width: 100%;
  border: none;
  outline: none;
  background-color: #f5f5f5;
  font-size: 14px;
  color: #333;
  padding-left: 50px;
  padding-right: 75px;
  border-radius: 7px;
}
.error-page__inner .error-page__form input[type=search]::-webkit-input-placeholder {
  color: var(--text-color);
}
.error-page__inner .error-page__form input[type=search]::-moz-placeholder {
  color: var(--text-color);
}
.error-page__inner .error-page__form input[type=search]:-ms-input-placeholder {
  color: var(--text-color);
}
.error-page__inner .error-page__form input[type=search]::-ms-input-placeholder {
  color: var(--text-color);
}
.error-page__inner .error-page__form input[type=search]::placeholder {
  color: var(--text-color);
}
.error-page__inner .error-page__form button[type=submit] {
  background-color: transparent;
  font-size: 22px;
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  width: 72px;
  outline: none;
  border: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
}
.error-page__inner .error-page__form-input {
  position: relative;
  display: block;
  max-width: 570px;
  width: 100%;
  margin: 0 auto;
}

/***
=============================================
    Services Details
=============================================
***/
.service-sidebar {
  position: relative;
  display: block;
  max-width: 365px;
  width: 100%;
}
.service-sidebar .service-sidebar-single {
  position: relative;
  display: block;
  margin-bottom: 30px;
}
.service-sidebar .service-sidebar-single-services {
  position: relative;
  display: block;
  background: #f6f4ec;
  border-radius: 10px;
  padding: 35px 30px 25px;
}
.service-sidebar .service-sidebar-single-services .title {
  position: relative;
  display: block;
  margin-bottom: 12px;
  padding-left: 20px;
}
.service-sidebar .service-sidebar-single-services .title h3 {
  color: var(--headings-color);
  font-size: 20px;
  line-height: 30px;
  letter-spacing: -0.02em;
}
.service-sidebar .service-sidebar-single-services ul {
  position: relative;
  display: block;
  margin-top: 10px;
}
.service-sidebar .service-sidebar-single-services ul li {
  position: relative;
  display: block;
  margin-bottom: 5px;
  margin-top: -10px;
}
.service-sidebar .service-sidebar-single-services ul li:last-child {
  margin-bottom: 0;
}
.service-sidebar .service-sidebar-single-services ul li a {
  position: relative;
  display: block;
  color: var(--agriox-color-1, #687469);
  font-size: 18px;
  padding: 22px 20px 22px;
  border-radius: 10px;
  background: transparent;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
  z-index: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.service-sidebar .service-sidebar-single-services ul li a:hover {
  color: var(--headings-color);
}
.service-sidebar .service-sidebar-single-services ul li a:hover::before {
  opacity: 1;
  -webkit-transform: perspective(400px) rotateX(0deg);
          transform: perspective(400px) rotateX(0deg);
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}
.service-sidebar .service-sidebar-single-services ul li a:hover i {
  color: var(--theme-color1);
}
.service-sidebar .service-sidebar-single-services ul li a::before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  content: "";
  z-index: -1;
  opacity: 1;
  -webkit-transform: perspective(400px) rotateX(90deg);
          transform: perspective(400px) rotateX(90deg);
  -webkit-transform-origin: bottom;
          transform-origin: bottom;
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
  background: #ffffff;
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}
.service-sidebar .service-sidebar-single-services ul li a i {
  font-size: 16px;
}
.service-sidebar .service-sidebar-single-services ul li.current a::before {
  opacity: 1;
  -webkit-transform: perspective(400px) rotateX(0deg);
          transform: perspective(400px) rotateX(0deg);
  -webkit-transition: all 300ms linear;
  transition: all 300ms linear;
  -webkit-transition-delay: 0.1s;
          transition-delay: 0.1s;
}
.service-sidebar .service-sidebar-single-services ul li.current a i {
  color: var(--theme-color1);
}
.service-sidebar .service-sidebar-single-services ul li.current:first-child {
  margin-top: 20px;
}
.service-sidebar .service-sidebar-single-services ul li.current:last-child {
  margin-bottom: 35px;
}
.service-sidebar .service-sidebar-single-contact-box {
  position: relative;
  display: block;
  background-attachment: scroll;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 10px;
  padding: 50px 0px 44px;
  z-index: 1;
}
.service-sidebar .service-sidebar-single-contact-box::before {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(109, 140, 84, 0.93);
  border-radius: 10px;
  content: "";
  z-index: -1;
}
.service-sidebar .service-sidebar-single-contact-box .icon {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 60px;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 50%;
  background: var(--theme-color2);
}
.service-sidebar .service-sidebar-single-contact-box .icon:hover {
  background-color: var(--headings-color);
}
.service-sidebar .service-sidebar-single-contact-box .icon:hover span::before {
  color: #fff;
}
.service-sidebar .service-sidebar-single-contact-box .icon span::before {
  position: relative;
  display: inline-block;
  color: var(--headings-color);
  font-size: 30px;
  line-height: 60px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-sidebar .service-sidebar-single-contact-box .title {
  position: relative;
  display: block;
  margin-top: 20px;
  margin-bottom: 42px;
}
.service-sidebar .service-sidebar-single-contact-box .title h2 {
  color: #ffffff;
  font-size: 36px;
}
.service-sidebar .service-sidebar-single-contact-box .phone {
  font-size: 24px;
  line-height: 34px;
}
.service-sidebar .service-sidebar-single-contact-box .phone a {
  color: #ffffff;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.service-sidebar .service-sidebar-single-contact-box .phone a:hover {
  color: var(--theme-color1);
}
.service-sidebar .service-sidebar-single-contact-box p {
  color: #ffffff;
  font-size: 14px;
  line-height: 22px;
}
.service-sidebar .service-sidebar-single-btn {
  position: relative;
  display: block;
}
.service-sidebar .service-sidebar-single-btn .thm-btn {
  font-size: 16px;
  padding: 13px 50px 28px;
}
.service-sidebar .service-sidebar-single-btn .thm-btn span::before {
  position: relative;
  display: inline-block;
  top: 13px;
  color: #334b35;
  font-size: 40px;
  padding-right: 25px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
  font-weight: 500;
}
.service-sidebar .service-sidebar-single-btn .thm-btn:hover span::before {
  color: #ffffff;
}
.service-sidebar .banner-widget {
  position: relative;
  display: block;
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  -webkit-box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.08);
          box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.08);
}
.service-sidebar .banner-widget .widget-content {
  position: relative;
  display: block;
  width: 100%;
  padding: 45px 30px 40px 30px;
  background-size: cover;
  background-repeat: no-repeat;
}
.service-sidebar .banner-widget .widget-content .shape {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 278px;
  background-repeat: no-repeat;
  background-size: cover;
}
.service-sidebar .banner-widget .content-box {
  position: relative;
  max-width: 200px;
  width: 100%;
}
.service-sidebar .banner-widget .content-box .icon-box {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 60px;
  line-height: 60px;
  font-size: 40px;
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
  text-align: center;
  border-radius: 5px;
  margin-bottom: 23px;
}
.service-sidebar .banner-widget .content-box .icon-box .icon-shape {
  position: absolute;
  top: -15px;
  right: -38px;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
}
.service-sidebar .banner-widget .content-box h3 {
  display: block;
  font-size: 24px;
  line-height: 32px;
  color: #ffffff;
  font-weight: 600;
  text-transform: uppercase;
  margin-bottom: 22px;
}
.service-sidebar .banner-widget .content-box .theme-btn-two:hover {
  background: #0a267a;
}
.service-sidebar .service-sidebar-single-btn .theme-btn {
  padding: 20px 50px;
}
.service-sidebar .service-sidebar-single-btn .theme-btn .btn-title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.service-sidebar .service-sidebar-single-btn .theme-btn span::before {
  position: relative;
  display: inline-block;
  font-size: 36px;
  padding-right: 25px;
  margin-top: 7px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
  font-weight: 500;
}

.services-details__content .feature-list .single-item {
  position: relative;
  display: block;
  border: 1px solid #e1e8e4;
  padding: 16px 30px 16px 53px;
  margin-bottom: 20px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
}
.services-details__content .feature-list .single-item .icon-box {
  color: var(--theme-color1);
  position: absolute;
  left: 20px;
  top: 16px;
  font-size: 18px;
  -webkit-transition: all 600ms ease;
  transition: all 600ms ease;
}
.services-details__content .feature-list .single-item .title {
  display: block;
  margin: 0;
  font-size: 16px;
  line-height: 32px;
  font-weight: 600;
  text-transform: uppercase;
}
.services-details__content .feature-list .single-item:hover {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}
.services-details__content .feature-list .single-item:hover .icon-box {
  color: var(--text-color-bg-theme-color2);
}

.service-list li {
  position: relative;
  display: block;
  margin-bottom: 10px;
}
.service-list li:last-child {
  margin-bottom: 0px;
}
.service-list li a {
  position: relative;
  display: block;
  font-size: 18px;
  color: var(--headings-color);
  font-weight: 600;
  background-color: #fff;
  padding: 17px 20px 17px 50px;
  -webkit-box-shadow: 20px 5px 20px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 20px 5px 20px 0px rgba(0, 0, 0, 0.05);
}
.service-list li a:hover, .service-list li a.current {
  color: var(--text-color-bg-theme-color1);
  background-color: var(--theme-color1);
  padding-left: 80px;
}
.service-list li a:hover i, .service-list li a.current i {
  width: 60px;
  color: var(--text-color-bg-theme-color2);
  background-color: var(--theme-color2);
}
.service-list li i {
  position: absolute;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  left: 0px;
  top: 0px;
  width: 30px;
  height: 100%;
  background-color: #f6f4ec;
  text-align: center;
  font-size: 16px;
  color: #707582;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}

.sidebar-service-list {
  margin: 0;
}
.sidebar-service-list li + li {
  margin-top: 10px;
}
.sidebar-service-list li a {
  font-size: 18px;
  font-weight: 700;
  position: relative;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  display: block;
  background-color: #f5faff;
  border-radius: 15px;
  padding: 19px 40px;
}
.sidebar-service-list li a:hover {
  color: var(--theme-color2);
}
.sidebar-service-list li a:hover i {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #fff;
  background-color: var(--theme-color2);
}
.sidebar-service-list li a i {
  height: 32px;
  width: 45px;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  color: #191825;
  background-color: #fff;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-size: 16px;
  border-radius: 15px;
}
.sidebar-service-list li.current a {
  color: var(--theme-color1);
}
.sidebar-service-list li.current a i {
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #fff;
  background-color: var(--theme-color1);
}

.service-details-help {
  position: relative;
  display: block;
  padding: 57px 55px 50px;
  margin-top: 30px;
  text-align: center;
  z-index: 1;
  background-color: var(--theme-color1);
  border-radius: 15px;
  overflow: hidden;
}

.help-shape-1 {
  position: absolute;
  bottom: -215px;
  left: -95px;
  width: 220px;
  height: 500px;
  background-color: #303030;
  mix-blend-mode: soft-light;
  border-radius: 150px;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  z-index: -1;
}

.help-shape-2 {
  position: absolute;
  top: -118px;
  right: -130px;
  width: 180px;
  height: 350px;
  background-color: #fff;
  mix-blend-mode: soft-light;
  border-radius: 186px;
  -webkit-transform: rotate(48deg);
          transform: rotate(48deg);
}

.help-icon {
  height: 73px;
  width: 73px;
  background-color: #fff;
  color: #191825;
  font-size: 32px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  border-radius: 50%;
  margin: 0 auto 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.help-icon:hover {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}

.help-title {
  font-size: 38px;
  color: #fff;
  line-height: 40px;
  margin-bottom: 21px;
  font-weight: 700;
}

.help-contact {
  position: relative;
  display: block;
  margin-top: 21px;
}
.help-contact p {
  font-size: 14px;
  color: #fff;
  margin: 0;
  line-height: 32px;
  font-weight: 600;
  opacity: 0.7;
}
.help-contact a {
  font-size: 30px;
  color: #fff;
  font-weight: 600;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.help-contact a:hover {
  color: #fff;
}

/***

====================================================================
    Blog Details
====================================================================

***/
.blog-details {
  position: relative;
  display: block;
}

.blog-details__left {
  position: relative;
  display: block;
}

.blog-details__img {
  position: relative;
  display: block;
  border-radius: 10px;
}
.blog-details__img img {
  width: 100%;
  border-radius: 10px;
}

.blog-details__date {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--theme-color1);
  text-align: center;
  padding: 21px 24px 20px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.blog-details__date .day {
  font-size: 16px;
  color: #fff;
  font-weight: var(--body-font-weight-bold);
  line-height: 16px;
}
.blog-details__date .month {
  position: relative;
  display: block;
  font-size: 10px;
  font-weight: var(--body-font-weight-bold);
  color: #fff;
  line-height: 12px;
  text-transform: uppercase;
}

.blog-details__content {
  position: relative;
  display: block;
  margin-top: 22px;
}

.blog-details__meta {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.blog-details__meta li + li {
  margin-left: 18px;
}
.blog-details__meta li a {
  font-size: 15px;
  color: #777;
  font-weight: 500;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.blog-details__meta li a:hover {
  color: var(--theme-color1);
}
.blog-details__meta li a i {
  color: var(--theme-color1);
  margin-right: 6px;
}

.blog-details__title {
  font-size: 30px;
  line-height: 40px;
  margin-top: 12px;
  margin-bottom: 21px;
  font-weight: var(--body-font-weight-bold);
}

.blog-details__bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 30px 0 30px;
  margin-top: 49px;
  border-top: 1px solid #ece9e0;
}
@media only screen and (max-width: 767px) {
  .blog-details__bottom {
    gap: 30px;
  }
}
.blog-details__bottom p {
  margin: 0;
}

.blog-details__tags span {
  color: #0e2207;
  font-size: 20px;
  margin-right: 14px;
  font-weight: var(--body-font-weight-bold);
}
.blog-details__tags a {
  position: relative;
  font-size: 12px;
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
  display: inline-block;
  padding: 5px 30px 5px;
  font-weight: var(--body-font-weight-bold);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  border-radius: 35px;
}
.blog-details__tags a:hover {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
  text-decoration: none;
}
.blog-details__tags a + a {
  margin-left: 6px;
}

.blog-details__social-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.blog-details__social-list a {
  position: relative;
  height: 43px;
  width: 43px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  font-size: 15px;
  border-radius: 50%;
  overflow: hidden;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 1;
}
.blog-details__social-list a:hover {
  color: var(--theme-color-light);
}
.blog-details__social-list a:hover:after {
  opacity: 1;
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
}
.blog-details__social-list a:after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-color: var(--theme-color2);
  -webkit-transition-delay: 0.1s;
  transition-delay: 0.1s;
  -webkit-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
  -webkit-transition-duration: 0.4s;
  transition-duration: 0.4s;
  -webkit-transition-property: all;
  transition-property: all;
  opacity: 1;
  -webkit-transform-origin: top;
  transform-origin: top;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
  z-index: -1;
}
.blog-details__social-list a + a {
  margin-left: 10px;
}

.blog-details__pagenation-box {
  position: relative;
  display: block;
  overflow: hidden;
  margin-bottom: 53px;
}

.blog-details__pagenation {
  position: relative;
  display: block;
}
.blog-details__pagenation li {
  position: relative;
  float: left;
  font-size: 20px;
  color: #0e2207;
  font-weight: var(--body-font-weight-bold);
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  line-height: 30px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  max-width: 370px;
  width: 100%;
  padding-left: 60px;
  padding-right: 60px;
  padding-top: 52px;
  padding-bottom: 52px;
  border-radius: 10px;
}
.blog-details__pagenation li:hover {
  background-color: var(--theme-color2);
  color: var(--text-color-bg-theme-color2);
}
.blog-details__pagenation li + li {
  margin-left: 30px;
}

/* Nav Links */
.nav-links {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  margin-bottom: 53px;
}
@media only screen and (max-width: 767px) {
  .nav-links {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    gap: 20px;
  }
}
.nav-links .prev {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  width: calc(50% - 15px);
  margin-right: 30px;
}
@media only screen and (max-width: 767px) {
  .nav-links .prev {
    width: 100%;
  }
}
.nav-links .prev .thumb {
  margin-right: 20px;
}
.nav-links .next {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  width: calc(50% - 15px);
}
@media only screen and (max-width: 767px) {
  .nav-links .next {
    width: 100%;
  }
}
.nav-links .next .thumb {
  margin-left: 20px;
}
.nav-links > div {
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.nav-links > div .thumb {
  display: inline-block;
  min-width: 60px;
  width: 60px;
  height: 60px;
  overflow: hidden;
}
.nav-links > div .thumb a {
  display: inline-block;
}
.nav-links > div > a {
  display: inline-block;
  word-wrap: break-word;
  white-space: -moz-pre-wrap;
  white-space: pre-wrap;
  font-size: 20px;
  line-height: 1.637;
  font-weight: var(--body-font-weight-bold);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  background-color: var(--theme-light-background);
  color: var(--theme-light-background-text-color);
  padding: 52px 50px;
  border-radius: 10px;
  width: 100%;
}
@media only screen and (max-width: 767px) {
  .nav-links > div > a {
    padding: 30px;
  }
}
.nav-links > div > a:hover {
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
}

/*** 

====================================================================
Sidebar
====================================================================

***/
@media (max-width: 991px) {
  .sidebar {
    margin-top: 50px;
  }
}
.sidebar__single + .sidebar__single {
  margin-top: 30px;
}

.sidebar__title {
  margin: 0;
  font-size: 20px;
  margin-bottom: 5px;
  font-weight: var(--h4-font-weight);
}

.sidebar__search {
  position: relative;
  display: block;
}

.sidebar__search-form {
  position: relative;
}
.sidebar__search-form input[type=search] {
  display: block;
  border: none;
  outline: none;
  background-color: var(--theme-color1);
  color: var(--theme-color-light);
  font-size: 16px;
  font-weight: 500;
  padding-left: 50px;
  height: 74px;
  width: 100%;
  padding-right: 80px;
  border-radius: 10px;
}
.sidebar__search-form input[type=search]::-webkit-input-placeholder {
  color: var(--theme-color-light);
  opacity: 1;
}
.sidebar__search-form input[type=search]::-webkit-input-placeholder, .sidebar__search-form input[type=search]:-ms-input-placeholder, .sidebar__search-form input[type=search]::-ms-input-placeholder, .sidebar__search-form input[type=search]::placeholder {
  color: var(--theme-color-light);
  opacity: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__search-form input[type=search] {
    padding-left: 30px;
  }
}
.sidebar__search-form button[type=submit] {
  background-color: transparent;
  color: var(--theme-color-light);
  font-size: 22px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 72px;
  outline: none;
  border: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0;
}
@media only screen and (max-width: 767px) {
  .sidebar__search-form button[type=submit] {
    width: 42px;
  }
}

.sidebar__post {
  position: relative;
  display: block;
  padding: 46px 30px 30px;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  border-radius: 10px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__post {
    padding: 30px;
  }
}
.sidebar__post .sidebar__title {
  margin-left: 20px;
}
@media only screen and (max-width: 767px) {
  .sidebar__post .sidebar__title {
    margin-left: 0;
  }
}
.sidebar__post .sidebar__post-list {
  margin: 0;
}
.sidebar__post .sidebar__post-list li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 16px 20px 17px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
@media only screen and (max-width: 767px) {
  .sidebar__post .sidebar__post-list li {
    padding: 16px 0;
  }
}
.sidebar__post .sidebar__post-list li:hover {
  background-color: #ffffff;
  border-radius: 10px;
}
.sidebar__post .sidebar__post-list li + li {
  margin-top: 11px;
}
.sidebar__post .sidebar__post-list .sidebar__post-image {
  margin-right: 20px;
  -webkit-box-flex: 70px;
      -ms-flex: 70px 0 0px;
          flex: 70px 0 0;
}
.sidebar__post .sidebar__post-list .sidebar__post-image > img {
  width: 80px;
  border-radius: 10px;
}
.sidebar__post .sidebar__post-list .sidebar__post-content {
  position: relative;
  top: -3px;
}
.sidebar__post .sidebar__post-list .sidebar__post-content h3 {
  font-size: 18px;
  margin: 0;
  line-height: 26px;
  letter-spacing: 0;
}
.sidebar__post .sidebar__post-list .sidebar__post-content h3 a {
  color: #0e2207;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  display: block;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
}
.sidebar__post .sidebar__post-list .sidebar__post-content-meta {
  font-size: 14px;
  font-weight: 500;
  color: #757873 !important;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.sidebar__post .sidebar__post-list .sidebar__post-content-meta i {
  color: var(--theme-color1);
  font-size: 14px;
  padding-right: 3px;
}

.sidebar__category {
  position: relative;
  display: block;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  border-radius: 10px;
  padding: 45px 30px 38px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__category {
    padding: 30px 15px 30px;
  }
}
.sidebar__category .sidebar__title {
  padding-left: 20px;
  margin-bottom: 9px;
}
.sidebar__category-list {
  margin: 0;
}
.sidebar__category-list li + li {
  margin-top: 4px;
}
.sidebar__category-list li a {
  color: #757873;
  font-size: 16px;
  position: relative;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  display: block;
  background: none;
  padding: 12px 20px;
  font-weight: 500;
  border-radius: 10px;
}
.sidebar__category-list li a:hover {
  background-color: rgb(255, 255, 255);
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  color: #0e2207;
  text-shadow: 1px 0 0 rgba(14, 34, 7, 0.5);
}
.sidebar__category-list li a:hover span {
  color: #ffcd1e;
  -webkit-transform: translateY(-50%) scale(1);
  transform: translateY(-50%) scale(1);
}
.sidebar__category-list li a span {
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%) scale(0);
  transform: translateY(-50%) scale(0);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  color: var(--theme-color2);
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 16px;
}
.sidebar__category-list li.active a {
  background-color: rgb(255, 255, 255);
  -webkit-box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
          box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  color: #0e2207;
  border-radius: 10px;
  text-shadow: 1px 0 0 rgba(14, 34, 7, 0.5);
}
.sidebar__category-list li.active a span {
  -webkit-transform: translateY(-50%) scale(1);
  transform: translateY(-50%) scale(1);
  color: #ffcd1e;
}

.sidebar__tags {
  position: relative;
  display: block;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  padding: 46px 45px 50px;
  border-radius: 10px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__tags {
    padding: 30px;
  }
}
.sidebar__tags .sidebar__title {
  margin-left: 5px;
  margin-bottom: 25px;
}

.sidebar__tags-list {
  margin-top: -10px;
}
.sidebar__tags-list a {
  font-size: 14px;
  color: #0e2207;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
  background: #ffffff;
  display: inline-block;
  padding: 5px 28px 5px;
  margin-left: 5px;
  border-radius: 30px;
}
.sidebar__tags-list a:hover {
  background: var(--theme-color1);
  color: var(--theme-color-light);
}
.sidebar__tags-list a + a {
  margin-left: 5px;
  margin-top: 10px;
}

.sidebar__comments {
  position: relative;
  display: block;
  color: var(--theme-light-background-text-color);
  background-color: var(--theme-light-background);
  padding: 46px 50px 43px;
  border-radius: 10px;
  overflow: hidden;
  z-index: 1;
}
@media only screen and (max-width: 767px) {
  .sidebar__comments {
    padding: 30px;
  }
}
.sidebar__comments .sidebar__title {
  margin-bottom: 25px;
}

.sidebar__comments-list {
  position: relative;
  display: block;
}
.sidebar__comments-list li {
  position: relative;
  display: block;
  padding-left: 65px;
}
.sidebar__comments-list li:hover .sidebar__comments-icon {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}
.sidebar__comments-list li + li {
  margin-top: 23px;
}

.sidebar__comments-icon {
  height: 45px;
  width: 45px;
  background-color: var(--theme-color1);
  border-radius: 50%;
  font-size: 15px;
  color: var(--theme-color-light);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.sidebar__comments-text-box p {
  font-size: 15px;
  margin: 0;
  line-height: 26px;
  font-weight: 500;
}
.sidebar__comments-text-box p span {
  color: #0e2207;
}
.sidebar__comments-text-box h5 {
  font-size: 15px;
  margin: 0;
  line-height: 26px;
  color: #757873;
  font-weight: 500;
  letter-spacing: 0;
}

/*** 

====================================================================
    Comments
====================================================================

***/
.comment-one .comment-one__title {
  margin-bottom: 30px;
}
.comment-one .comment-one__single {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-bottom: 1px solid #ece9e0;
  padding-bottom: 60px;
  margin-bottom: 60px;
  -webkit-box-align: top;
      -ms-flex-align: top;
          align-items: top;
}
@media only screen and (max-width: 767px) {
  .comment-one .comment-one__single {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}
.comment-one .comment-one__content {
  position: relative;
  margin-left: 45px;
}
@media only screen and (max-width: 767px) {
  .comment-one .comment-one__content {
    margin-top: 20px;
    margin-left: 0;
  }
}
.comment-one .comment-one__content h3 {
  margin: 0;
  font-size: 20px;
  color: var(--theme-black);
  margin-bottom: 24px;
}
.comment-one .comment-one__content p {
  font-size: 16px;
  font-weight: 500;
}
.comment-one .comment-one__btn {
  padding: 5px 30px;
  position: absolute;
  top: 0;
  right: 0;
  font-size: 14px;
}
.comment-one .comment-one__image {
  position: relative;
  display: block;
  border-radius: 50%;
  -webkit-box-flex: 150px;
      -ms-flex: 150px 0 0px;
          flex: 150px 0 0;
}
.comment-one .comment-one__image img {
  border-radius: 50%;
}

.comment-form .comment-form__title {
  margin-top: -7px;
}
.comment-form .form-control::-webkit-input-placeholder {
  color: var(--text-color);
}
.comment-form .form-control::-moz-placeholder {
  color: var(--text-color);
}
.comment-form .form-control:-ms-input-placeholder {
  color: var(--text-color);
}
.comment-form .form-control::-ms-input-placeholder {
  color: var(--text-color);
}
.comment-form .form-control::placeholder {
  color: var(--text-color);
}

/*--------------------------------------------------------------
# Team Details
--------------------------------------------------------------*/
.team-details {
  position: relative;
  display: block;
}

.team-details__top {
  padding: 0 0 120px;
}

.team-details-shape-1 {
  position: absolute;
  bottom: -270px;
  right: 0;
  opacity: 0.5;
  z-index: 2;
}
.team-details-shape-1 img {
  width: auto;
}

.team-details__top-left {
  position: relative;
  display: block;
  margin-right: 20px;
}

.team-details__top-img {
  position: relative;
  display: block;
  border-radius: 30px;
}
.team-details__top-img img {
  width: 100%;
  border-radius: 30px;
}

.team-details__big-text {
  font-size: 80px;
  line-height: 80px;
  text-transform: uppercase;
  color: #eef0f6;
  letter-spacing: 0.35em;
  font-weight: 400;
  position: absolute;
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
  top: 209px;
  left: -325px;
}

.team-details__top-right {
  position: relative;
  display: block;
  margin-left: 50px;
}
@media only screen and (max-width: 991px) {
  .team-details__top-right {
    margin-top: 70px;
    margin-left: 0;
  }
}

.team-details__top-content {
  position: relative;
  display: block;
  margin-top: -11px;
}

.team-details__top-name {
  font-size: 40px;
  font-weight: 700;
  line-height: 50px;
  margin-bottom: 3px;
}

.team-details__top-title {
  font-size: 16px;
  color: var(--theme-color1);
}

.team-details__social {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 28px;
}
.team-details__social a {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: var(--theme-color1);
  border-radius: 50%;
  color: var(--theme-color-light);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 15px;
  height: 40px;
  justify-content: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  position: relative;
  text-align: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  width: 40px;
}
.team-details__social a:hover {
  background-color: var(--theme-color2);
  color: var(--theme-color-light);
}
.team-details__social a + a {
  margin-left: 10px;
}

.team-details__top-text-1 {
  font-size: 30px;
  color: var(--theme-color1);
  line-height: 45px;
  font-weight: 400;
  margin-bottom: 30px;
}

.team-details__top-text-2 {
  padding-top: 23px;
  padding-bottom: 35px;
}

.team-details__bottom {
  position: relative;
  display: block;
  border-top: 1px solid #e4e5ea;
  padding-top: 110px;
}

.team-details__bottom-left {
  position: relative;
  display: block;
  margin-right: 70px;
}

.team-details__bottom-left-title {
  font-size: 36px;
  font-weight: 700;
  line-height: 46px;
}

.team-details__bottom-left-text {
  padding-top: 30px;
}

.team-details__bottom-right {
  position: relative;
  display: block;
  margin-left: 70px;
  margin-top: 1px;
}
@media only screen and (max-width: 991px) {
  .team-details__bottom-right {
    margin-left: 0;
  }
}

.team-details__progress {
  position: relative;
  display: block;
  width: 100%;
}
.team-details__progress .bar {
  position: relative;
  width: 100%;
  height: 13px;
  background-color: #eef0f6;
  border-radius: 7px;
  margin-bottom: 22px;
}
.team-details__progress .bar-inner {
  position: relative;
  display: block;
  width: 0px;
  height: 13px;
  border-radius: 7px;
  background-color: var(--theme-color1);
  -webkit-transition: all 1500ms ease;
  transition: all 1500ms ease;
}
.team-details__progress .count-text {
  position: absolute;
  right: 0px;
  bottom: 21px;
  color: var(--theme-color2);
  line-height: 24px;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
  opacity: 0;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.team-details__progress .bar-inner.counted .count-text {
  opacity: 1;
}
.team-details__progress .bar.marb-0 {
  margin-bottom: 0;
}

.team-details__progress-single {
  position: relative;
  display: block;
}

.team-details__progress-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 28px;
  color: var(--theme-color2);
  margin-bottom: 6px;
}

.team-contact-form {
  background-color: #eef0f6;
}
.team-contact-form input[type=text],
.team-contact-form input[type=email] {
  background-color: #fff;
}
.team-contact-form textarea {
  background-color: #fff;
  height: 180px;
}

/***

====================================================================
        Contact
====================================================================

***/
.contact-details__info {
  position: relative;
  display: block;
  margin-top: 41px;
}
.contact-details__info li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.contact-details__info li .icon {
  height: 80px;
  width: 80px;
  background-color: var(--theme-color1);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.contact-details__info li .icon span {
  color: var(--theme-color-light);
  font-size: 25px;
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.contact-details__info li:hover .icon {
  background-color: var(--theme-color2);
}
.contact-details__info li:hover .icon span {
  color: var(--theme-color-light);
}
.contact-details__info li:hover .text a {
  color: var(--notech-base);
}
.contact-details__info li:hover .text a span {
  color: var(--notech-gray);
}
.contact-details__info li .text {
  margin-left: 30px;
}
.contact-details__info li .text p {
  font-size: 14px;
  line-height: 24px;
}
.contact-details__info li .text a {
  font-size: 18px;
  color: var(--notech-black);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
}
.contact-details__info li .text span {
  font-size: 20px;
  color: var(--notech-black);
}
.contact-details__info li + li {
  margin-top: 19px;
}

.map {
  height: 450px;
}