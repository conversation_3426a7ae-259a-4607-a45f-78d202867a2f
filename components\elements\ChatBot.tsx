"use client"
import { useState, useEffect, useRef } from "react"
import type React from "react"
import Link from "next/link"
import Image from "next/image" // Add Image import
import axios from "axios"
import { useMutation } from "@tanstack/react-query"
import { MessageSquare, X, Send, ChevronDown, ChevronUp } from "lucide-react"
import { useStore } from "@/store/chatStore"

interface Message {
  sender: "user" | "bot"
  text: string
}

interface AIMessage {
  role: "user" | "assistant" | "system";
  content: string;
}

// Add UserInfo interface
interface UserInfo {
  id?: string;
  name?: string;
  email?: string;
  phone?: string;
}

// Update chatbotStyles to position the button on the right
const chatbotStyles = {
  chatButton: "position-fixed bottom-0 end-0 m-4 rounded-circle d-flex align-items-center justify-content-center shadow-lg",
  chatWindow: "position-fixed bottom-10 end-0 mb-20 me-4 bg-white rounded shadow-lg border overflow-hidden",
  chatHeader: "p-3 d-flex align-items-center gap-3 text-white",
  chatBody: "overflow-auto p-3 d-flex flex-column gap-3 bg-light",
  userMessage: "text-white rounded p-3 ms-auto mb-2 shadow-sm word-break-word",
  botMessage: "bg-white text-dark rounded p-3 me-auto mb-2 shadow-sm border word-break-word",
  inputArea: "d-flex border-top p-2 bg-white",
  inputField: "form-control border-0 shadow-none",
  sendButton: "btn ms-2 d-flex align-items-center justify-content-center",
  submitButton: "btn w-100"
}

// Add a utility function to parse text and convert URLs to Link components
const renderMessageWithLinks = (text: string, textColor?: string) => {
  // Regular URL pattern
  const urlPattern = /(https?:\/\/[^\s]+)/g;

  // Split the message by newlines to preserve formatting
  const parts = text.split('\n');

  return parts.map((part, i) => {
    // Check if this part contains a URL
    const matches = part.match(urlPattern);

    if (matches) {
      const elements: React.ReactNode[] = [];
      let lastIndex = 0;

      // Process each URL in this part
      matches.forEach((match, j) => {
        const index = part.indexOf(match, lastIndex);

        // Add text before the URL
        if (index > lastIndex) {
          elements.push(
            <span key={`text-${i}-${j}`} style={{ color: textColor }}>
              {part.substring(lastIndex, index)}
            </span>
          );
        }

        // Add the URL as a link - remove target="_blank" for booking links
        const isBookingLink = match.includes("/booking");
        elements.push(
          <Link
            key={`link-${i}-${j}`}
            href={match}
            target={isBookingLink ? "_self" : "_blank"}
            rel="noopener noreferrer"
            style={{
              color: textColor || '#c29a46',
              textDecoration: 'underline',
              wordBreak: 'break-all'
            }}
          >
            {match}
          </Link>
        );

        lastIndex = index + match.length;
      });

      // Add any remaining text
      if (lastIndex < part.length) {
        elements.push(
          <span key={`text-${i}-end`} style={{ color: textColor }}>
            {part.substring(lastIndex)}
          </span>
        );
      }

      return <p key={i} style={{ color: textColor, width: '100%' }}>{elements}</p>;
    }

    // Return regular text
    return <p key={i} style={{ color: textColor, width: '100%' }}>{part}</p>;
  });
};

// Add this function to save messages to the database
const saveMessageToDatabase = async (content: string, sender: 'user' | 'bot', userInfo: UserInfo) => {
  if (!userInfo.id) return; // Skip if no user ID

  try {
    await axios.post("/api/chat-messages", {
      content,
      sender,
      chatUserId: userInfo.id
    });
  } catch (error) {
    console.error("Error saving message:", error);
    // Continue even if saving fails - don't block the UI
  }
};

// Add this function to load previous messages for returning users
const loadPreviousMessages = async (userId: string, addMessage: (message: Message) => void) => {
  try {
    const response = await axios.get(`/api/chat-messages?chatUserId=${userId}`);

    if (response.data.success && response.data.data.length > 0) {
      // Convert database messages to the format used in the UI
      const previousMessages = response.data.data.map((msg: any) => ({
        sender: msg.sender as 'user' | 'bot',
        text: msg.content
      }));

      // Replace current messages with loaded ones
      previousMessages.forEach((msg: Message) => {
        addMessage(msg);
      });

      return true;
    }

    return false;
  } catch (error) {
    console.error("Error loading previous messages:", error);
    return false;
  }
};

const ChatBot = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [userInput, setUserInput] = useState("")
  const chatRef = useRef<HTMLDivElement>(null)
  const firstVisitRef = useRef(false)
  const chatWindowRef = useRef<HTMLDivElement>(null)
  const chatButtonRef = useRef<HTMLButtonElement>(null)
  const [isTyping, setIsTyping] = useState(false)
  const [showNotification, setShowNotification] = useState(true)

  // Get everything from the store
  const {
    messages,
    userInfo,
    addMessage,
    setUserInfo,
    showForm,
    setShowForm,
    initialized,
    setInitialized
  } = useStore()

  // Hide notification when chat is opened or when user has provided info
  useEffect(() => {
    if (isOpen || (userInfo && userInfo.id)) {
      setShowNotification(false)
    }
  }, [isOpen, userInfo])

  const sendUserData = async (data: any) => {
    const response = await axios.post("/api/chat-users", data);
    return response.data;
  }

  const mutation = useMutation({
    mutationFn: sendUserData,
    onSuccess: (data) => {
      // Update user info with the ID from the response
      if (data.user && data.user.id) {
        setUserInfo({
          ...userInfo,
          id: data.user.id
        });
      }

      setShowForm(false);
      const welcomeMessage = `Thanks ${userInfo.name}! How can I help you today?`;

      // Add welcome message to UI
      addMessage({
        sender: "bot",
        text: welcomeMessage
      });

      // Save welcome message to database
      saveMessageToDatabase(welcomeMessage, "bot", userInfo);
    },
  })

  // Only initialize on first visit, never on reload
  useEffect(() => {
    // Check if this is the first visit using a dedicated flag
    const isFirstVisit = localStorage.getItem('chat-first-visit') !== 'false';

    // Skip if not first visit
    if (!isFirstVisit) return;

    // Check if user ID exists in storage
    const storedUserInfo = localStorage.getItem('chat-storage');
    if (storedUserInfo) {
      try {
        const parsedStorage = JSON.parse(storedUserInfo);
        const storedId = parsedStorage?.state?.userInfo?.id;

        if (storedId) {
          // We have a returning user, load their messages
          loadPreviousMessages(storedId, addMessage);
          setShowForm(false); // Skip the form for returning users
        } else {
          // First visit - add welcome message with updated text
          addMessage({
            sender: "bot",
            text: "Hi, I'm Lina, a Sales Assistant from Lavish Shape! Please provide your information to get started.",
          });
        }
      } catch (e) {
        console.error("Error parsing stored user info:", e);
        // Fallback to new user flow with updated text
        addMessage({
          sender: "bot",
          text: "Hi, I'm Lina, a Sales Assistant from Lavish Shape! Please provide your information to get started.",
        });
      }
    } else {
      // First visit - add welcome message with updated text
      addMessage({
        sender: "bot",
        text: "Hi, I'm Lina, a Sales Assistant from Lavish Shape! Please provide your information to get started.",
      });
    }

    // Mark first visit as complete to prevent future welcome messages
    localStorage.setItem('chat-first-visit', 'false');
    setInitialized(true);
  }, [addMessage, setInitialized, setShowForm]);

  useEffect(() => {
    if (chatRef.current) {
      chatRef.current.scrollTop = chatRef.current.scrollHeight
    }
  }, [messages])

  const handleSubmitUserInfo = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);

    const name = formData.get("name") as string;
    const email = formData.get("email") as string;
    const phone = formData.get("phone") as string;

    const userDetails = { name, email, phone };
    setUserInfo({ ...userDetails, id: "" });

    // Send data to API
    mutation.mutate(userDetails);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen)
  }

  const handleSendMessage = async () => {
    if (userInput.trim()) {
      // Add user message to chat
      addMessage({ sender: "user", text: userInput });

      // Save user message to database
      saveMessageToDatabase(userInput, "user", userInfo);

      // Clear input field immediately
      setUserInput("");

      // Show typing indicator
      setIsTyping(true);

      try {
        // Send to AI backend
        const response = await axios.post("/api/ai-chat", {
          message: userInput,
          userId: userInfo.id, // Make sure to pass the user ID, not just email
          history: messages.map((msg: { sender: string; text: any }) => ({
            role: msg.sender === "user" ? "user" : "assistant",
            content: msg.text
          }))
        });

        const aiResponse = response.data.message;
        const includesBookingLink = response.data.includesBookingLink;

        // Add AI response to chat
        addMessage({
          sender: "bot",
          text: aiResponse
        });

        // Save AI response to database
        saveMessageToDatabase(aiResponse, "bot", userInfo);

        // If booking link is included, track click events
        if (includesBookingLink) {
          // Add click event listeners to booking links after rendering
          setTimeout(() => {
            const bookingLinks = document.querySelectorAll('a[href*="/booking"]');
            bookingLinks.forEach(link => {
              link.addEventListener('click', async () => {
                try {
                  // Record booking link click
                  await axios.post("/api/booking-link-click", {
                    userId: userInfo.id,
                    email: userInfo.email
                  });
                  console.log("Booking link click recorded");
                } catch (error) {
                  console.error("Error recording booking link click:", error);
                }
              });
            });
          }, 100); // Small delay to ensure DOM is updated
        }

      } catch (error) {
        console.error("AI chat error:", error);
        const errorMessage = "Sorry, I'm having trouble connecting to my brain. Please try again later.";

        addMessage({
          sender: "bot",
          text: errorMessage
        });

        // Save error message to database
        saveMessageToDatabase(errorMessage, "bot", userInfo);
      } finally {
        setIsTyping(false);
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Add click outside handler to close chat
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        chatWindowRef.current &&
        chatButtonRef.current &&
        !chatWindowRef.current.contains(event.target as Node) &&
        !chatButtonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <>
      {/* Chat toggle button - fixed position, always visible */}
      {/* Chat toggle button - fixed position, always visible */}
      <button
        ref={chatButtonRef}
        onClick={toggleChat}
        className={chatbotStyles.chatButton}
        style={{
          width: "56px",
          height: "56px",
          zIndex: 99,
          padding: 0,
          border: "none",
          overflow: "hidden",
          borderRadius: "50%",
          right: "0", // Position at right side
          left: "auto" // Override any left positioning
        }}
        aria-label="Toggle chat"
      >
        {isOpen ? (
          <div style={{
            width: "100%",
            height: "100%",
            backgroundColor: "#c29a46",
            display: "flex",
            alignItems: "center",
            justifyContent: "center"
          }}>
            <X style={{ width: "24px", height: "24px", color: "white" }} />
          </div>
        ) : (
          <div style={{
            width: "100%",
            height: "100%",
            overflow: "hidden"
          }}>
            <Image
              src="/images/team/assistant.jpg"
              alt="Lina from Lavish Shape"
              width={56}
              height={56}
              style={{ objectFit: "cover", width: "100%", height: "100%" }}
            />
          </div>
        )}
      </button>

      {/* Attention-grabbing notification bubble */}
      {showNotification && !isOpen && (
        <div
          style={{
            position: "fixed",
            bottom: "85px",
            right: "70px",
            backgroundColor: "#c29a46",
            color: "white",
            padding: "8px 14px",
            borderRadius: "16px",
            boxShadow: "0 3px 8px rgba(0,0,0,0.12)",
            zIndex: 98,
            maxWidth: "220px",
            animation: "pulse 2s infinite",
            display: "flex",
            alignItems: "start",
            justifyContent: "start",
            // flexDirection: "column"
          }}
        >
          <span style={{ fontSize: "13.5px", fontWeight: "400" }}>Bonjour! Je suis là pour vous assiter en cas de  besoin.</span>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowNotification(false);
            }}
            style={{
              background: "none",
              border: "none",
              color: "white",
              marginLeft: "2px",
              cursor: "pointer",
              padding: "2px",
              display: "flex",
              width: "10%",
              paddingTop: '7px'
            }}
            aria-label="Close notification"
          >
            <X size={12} />
          </button>
        </div>
      )}

      {/* Add keyframe animation to the global styles */}
      <style jsx global>{`
        @keyframes pulse {
          0% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
          100% {
            transform: scale(1);
          }
        }
      `}</style>

      {/* Chat window - increased width and responsive sizing */}
      <div
        ref={chatWindowRef}
        className={chatbotStyles.chatWindow}
        style={{
          width: "min(380px, 90vw)", // Wider but responsive
          maxHeight: "80vh",
          zIndex: 99999,
          opacity: isOpen ? 1 : 0,
          visibility: isOpen ? 'visible' : 'hidden',
          transition: "opacity 0.3s ease, visibility 0.3s ease",
          bottom: "85px",
          right: "0", // Position at right side
          left: "auto" // Override any left positioning
        }}
      >
        {/* Chat header with updated company name and brand color */}
        <div className={chatbotStyles.chatHeader} style={{
          backgroundColor: "#c29a46",
          padding: "10px 15px",
          display: "flex",
          alignItems: "center",
          gap: "12px"
        }}>
          <div style={{
            width: "40px",
            height: "40px",
            flexShrink: 0,
            borderRadius: "50%",
            overflow: "hidden"
          }}>
            <Image
              src="/images/team/assistant.jpg"
              alt="Lina from Lavish Shape"
              width={40}
              height={40}
              style={{ objectFit: "cover" }}
            />
          </div>
          <div style={{ overflow: "hidden" }}>
            <h3 style={{
              fontWeight: "bold",
              fontSize: "18px",
              margin: 0,
              padding: 0,
              color: "white",
              lineHeight: 1.2
            }}>Lina - Assistante</h3>
            <p style={{
              fontSize: "12px",
              margin: 0,
              padding: 0,
              marginTop: "-2px",
              color: "white",
              lineHeight: 1.2
            }}>Lavish Shape Glow Med & Spa</p>
          </div>
        </div>

        {/* Chat messages */}
        <div
          ref={chatRef}
          className={chatbotStyles.chatBody}
          style={{ height: "350px", scrollBehavior: "smooth" }}
        >
          {messages.map((msg: any, index: any) => (
            <div key={index} className={`d-flex ${msg.sender === "user" ? "justify-content-end" : "justify-content-start"}`}>
              {msg.sender === "bot" && (
                <div className="me-2 align-self-end mb-1" style={{
                  width: "32px",
                  height: "32px",
                  borderRadius: "50%",
                  overflow: "hidden"
                }}>
                  <Image
                    src="/images/team/assistant.jpg"
                    alt="Lina from Lavish Shape"
                    width={32}
                    height={32}
                    style={{ objectFit: "cover" }}
                  />
                </div>
              )}
              <div
                className={msg.sender === "user" ? chatbotStyles.userMessage : chatbotStyles.botMessage}
                style={{
                  maxWidth: "75%",
                  backgroundColor: msg.sender === "user" ? "#c29a46" : "#ffffff",
                  borderRadius: "12px",
                  boxShadow: "0 1px 2px rgba(0,0,0,0.1)"
                }}
              >
                {renderMessageWithLinks(msg.text, msg.sender === "user" ? "#ffffff" : "inherit")}
              </div>
              {msg.sender === "user" && (
                <div className="bg-light rounded-circle border d-flex align-items-center justify-content-center ms-2 align-self-end mb-1"
                  style={{
                    width: "32px",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center"
                  }}
                >
                  <span style={{
                    textAlign: "center",
                    lineHeight: "1",
                    fontSize: "14px",
                    color: "#666",
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center"
                  }}>
                    {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : "Y"}
                  </span>
                </div>
              )}
            </div>
          ))}
          {isTyping && (
            <div className="d-flex justify-content-start">
              <div className="me-2 align-self-end mb-1" style={{
                width: "32px",
                height: "32px",
                borderRadius: "50%",
                overflow: "hidden"
              }}>
                <Image
                  src="/images/team/assistant.jpg"
                  alt="Lina from Lavish Shape"
                  width={32}
                  height={32}
                  style={{ objectFit: "cover" }}
                />
              </div>
              <div className={chatbotStyles.botMessage} style={{ maxWidth: "75%" }}>
                <p>Typing...</p>
              </div>
            </div>
          )}
        </div>

        {/* User info form */}
        {showForm && (
          <div className="p-3 border-top bg-white">
            <form onSubmit={handleSubmitUserInfo} className="d-flex flex-column gap-2">
              <input
                type="text"
                name="name"
                placeholder="Your Name"
                required
                className="form-control"
                style={{
                  border: '1px solid #ccc',
                  height: '16px'
                }}
              />
              <input
                type="email"
                name="email"
                placeholder="Your Email"
                required
                className="form-control"
                style={{
                  border: '1px solid #ccc',
                  height: '16px'
                }}
              />
              <input
                type="tel"
                name="phone"
                placeholder="Your Phone Number"
                className="form-control"
                style={{
                  border: '1px solid #ccc',
                  height: '16px'
                }}
              />
              <button
                type="submit"
                className={chatbotStyles.submitButton}
                style={{ backgroundColor: "#c29a46", color: "white" }}
                disabled={mutation.isPending}
              >
                {mutation.isPending ? "Submitting..." : "Start Chat"}
              </button>
            </form>
          </div>
        )}

        {/* Input area */}
        {!showForm && (
          <div className={chatbotStyles.inputArea}>
            <input
              type="text"
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type your message..."
              className={chatbotStyles.inputField}
            />
            <button
              onClick={handleSendMessage}
              className={chatbotStyles.sendButton}
              style={{
                width: "40px",
                height: "40px",
                backgroundColor: "#c29a46",
                color: "white"
              }}
            >
              <Send style={{ width: "20px", height: "20px" }} />
            </button>
          </div>
        )}
      </div>
    </>
  )
}

export default ChatBot
