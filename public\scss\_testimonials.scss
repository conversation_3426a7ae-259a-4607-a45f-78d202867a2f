/*** 

====================================================================
	Testimonial Section
====================================================================

***/

.testimonial-section {
    padding: 40px 0 200px;
    position: relative;
    @include media-breakpoint-down(lg){
        padding: 50px 0 100px;
    }
    &.style-two {
        padding: 130px 0 300px;
        position: relative;
        @include media-breakpoint-down(lg){
            padding: 100px 0;
        }
    }
    &.style-three {
        padding: 200px 0 185px;
        position: relative;
        .bg-image {
            pointer-events: none;
            z-index: -2;
        }
        .instagram1-7 {
            background-image: url("../images/resource/instagram1-7.png");
            background-size: cover;
            position: absolute;
            width: 166px;
            height: 298px;
            top: 270px;
            left: 21px;
            @include media-breakpoint-down(lg){
                display: none;
            }
        }
        .testimonial-pattrn1-2 {
            background-image: url(../images/resource/testimonial-pattrn1-2.png);
            background-size: cover;
            position: absolute;
            width: 351px;
            height: 293px;
            top: 320px;
            right: 71px;
            @media (max-width: 1499.98px) {
                display: none;
            }
        }
    }
    .testimonial-pattrn1-1 {
        background-image: url(../images/resource/testimonial-pattrn1-1.png);
        position: absolute;
        width: 205px;
        height: 263px;
        left: 54px;
        top: 54px;
        @include media-breakpoint-down(xxl){
            display: none;
        }
    }
    .default-dots{
        .owl-dots {
            left: 0;
            position: absolute;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            bottom: -44px;
            .owl-dot {
                height: 12px;
                width: 12px;
                margin: 0 8.5px;
                background-color: transparent;
                border: 1px solid #707070;
                border-radius: 50%;
                @include transition(all 300ms ease);
                &.active {
                    width: 15px;
                    height: 15px;
                    background-color: var(--theme-color1);
                    border-color: var(--theme-color1);
                    @include transition(all 300ms ease);
                }
            }
        }
    }
    .carousel-outer {
        position: relative;
        .image-box {
            .image {
                border-radius: 50%;
                margin-bottom: 0;
                position: absolute;
                img {
                    border-radius: 50%;
                }
            }
            .client1 {
                top: -128px;
                left: -153px;
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
            .client2 {
                bottom: 26px;
                left: -310px;
                @include media-breakpoint-down(xxl){
                    left: -160px;
                }
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
            .client3 {
                bottom: -181px;
                left: -118px;
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
            .client4 {
                bottom: -120px;
                right: -210px;
                @include media-breakpoint-down(xxl){
                    right: -130px;
                }
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
            .client5 {
                top: -98px;
                right: -361px;
                @include media-breakpoint-down(xxl){
                    right: -150px;
                }
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
        }
    }
}

.testimonial-block {
    .inner-box {
        .rating {
            margin-bottom: 25px;
            .icon {
                color: var(--theme-color1);
                font-size: 25px;
                margin-right: 4px;
                @include media-breakpoint-down(sm){
                    font-size: 22px;
                }
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .text {
            font-size: 30px;
            font-weight: 500;
            font-style: italic;
            font-family: var(--title-font);
            line-height: 36.33px;
            @include media-breakpoint-down(sm){
                font-size: 22px;
            }
        }
        .info-box {
            align-items: center;
            display: flex;
            justify-content: center;
            margin-top: 30px;
            .name {
                font-weight: 500;
                margin-bottom: 0;
                margin-right: 6px;
                @include media-breakpoint-down(sm){
                    font-size: 25px;
                }
            }
            .designation {
                display: block;
                margin-bottom: -5px;
                @include media-breakpoint-down(sm){
                    font-size: 15px;
                }
            }
        }
    }
}

/*** 

====================================================================
    Testimonial Style
====================================================================

***/

.testimonial-style-two {
    .instagram1-7 {
        background-image: url(../images/resource/instagram1-7.png);
        position: absolute;
        width: 201px;
        height: 360px;
        bottom: 0;
        left: 50px;
        @include media-breakpoint-down(xxl){
            display: none;
        }
    }
    .testimonial-pattrn1-2 {
        background-image: url(../images/resource/testimonial-pattrn1-2.png);
        position: absolute;
        width: 472px;
        height: 401px;
        bottom: 80px;
        right: 0;
        @include media-breakpoint-down(xxl){
            display: none;
        }
    }
}


/*** 

====================================================================
    Testimonial Style Three
====================================================================

***/

.testimonial-section-three{
    background-image: url(../images/background/testimonial-bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    .image-1{
        left: 50px;
        max-width: 320px;
        position: absolute;
        top: 50px;
        @include media-breakpoint-down(md){
            display: none;
        }
    }
}

//Testimonial Block Three
.testimonial-block-three{
    position: relative;
    margin-bottom: 30px;
    .inner-box{
        background-color: var(--theme-color-light);
        border: 1px solid #F4E8E1;
        border-radius: 10px;
        overflow: hidden;
        padding: 38px 30px 30px 180px;
        position: relative;
        @include media-breakpoint-down(lg){
            padding: 38px 30px 30px;
        }
        .img-1{
            left: 0;
            position: absolute;
            top: 0;
            width: auto !important;
        }
        .img-2{
            bottom: 0;
            position: absolute;
            right: 0;
            width: auto !important;
        }
        .icon{
            align-items: center;
            border-radius: 12px;
            bottom: 100px;
            color: transparent;
            display: flex;
            font-size: 100px;
            height: 21px;
            justify-content: center;
            left: 80px;
            position: absolute;
            transform: rotate(180deg);
            top: auto;
            width: 21px;
            -webkit-text-fill-color: white;
            -webkit-text-stroke: 3px #F9F3F0;
        }
        &:hover{
            .thumb{
                img{
                    transform: scale(-1) rotate(180deg);                    
                }
            }
        }
    }
    .thumb{
        height: 100px;
        left: 40px;
        position: absolute;
        top: 40px;
        width: 100px;
        @include media-breakpoint-down(lg){
            left: 0;
            margin-bottom: 30px;
            position: relative;
            top: 0;
        }
        img{border-radius: 20px;
            transition: all 400ms ease;}
    }
    .info-box{
        margin-bottom: 20px;
        position: relative;
        text-align: left;
        z-index: 2;
        .text{
            color: #6E6E6E;
            font-family: 'Cormorant';
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 30px;
            margin-bottom: 15px;
        }
        .name{
            margin-bottom: 0px;
        }
        .designation {
            color: #D0AC3B;
            display: block;
            margin-bottom: 8px;
        }
        .rating{
            color: #D0AC3B;
            font-size: var(--body-font-size);
            line-height: 20px;
            margin-bottom: 20px;
            position: relative;
            z-index: 2;
            i{margin-right: 4px;}
        }        
    }

}