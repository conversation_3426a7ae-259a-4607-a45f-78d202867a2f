/***

==================================================================
    Video Section
==================================================================

***/

.video-section {
    position: relative;
    padding: 182px 0 173px;
    @include media-breakpoint-down(md){
        padding: 100px 0 140px;
    }
    .sec-title {
        @include media-breakpoint-down(lg){
            text-align: center;
        }
        h2 {
            br {
                display: none;
                @include media-breakpoint-down(lg){
                    display: block;
                }
            }
            @include media-breakpoint-down(xxl){
                font-size: 50px;
            }
            @include media-breakpoint-down(xl){
                font-size: 45px;
            }
            @include media-breakpoint-down(sm){
                font-size: 33px;
            }
        }
        .pricing-btn {
            padding: 15px 32px 15px 32px;
            margin-top: 39px;
            @include media-breakpoint-down(sm){
                padding: 13px 25px;
                margin-top: 26px;
            }
        }
    }
    .outer-box{
        align-items: center;
        display: flex;
        top: 36px;
        position: relative;
        justify-content: flex-end;
        @include media-breakpoint-down(lg){
            justify-content: center;
            margin-top: 100px;
            top: 0;
        }
        @include media-breakpoint-down(sm){
            margin-top: 70px;
        }
        h4 {
            font-weight: 500;
            margin-bottom: 0;
            margin-right: 51px;
            position: relative;
            &:before {
                background-image: url(../images/icons/icon-arrow1.png);
                content: "";
                position: absolute;
                bottom: -35px;
                right: -28px;
                width: 73px;
                height: 42px;
            }
        }
        .play-now {
            border-radius: 50%;
            position: relative;
            display: inline-block;
            &:after {
                content: "";
                border: 1px dashed #D0AC3B;
                border-radius: 50%;
                bottom: 0;
                top: -8px;
                left: -8px;
                right: 0;
                position: absolute;
                width: 100px;
                height: 100px;
            }
            &::before {
                display: none;
            }
            &:hover {
                background-color: var(--theme-color1);
                border-color: var(--theme-color1);
                color: var(--theme-color-light);
                .icon {
                    color: var(--theme-color-light);
                }
            }
            .icon {
                background-color: var(--theme-color1);
                color: var(--theme-color-light);
                font-size: 24px;
                height: 84px;
                width: 84px;
                line-height: 84px;
            }
        }
    }
}


/***

==================================================================
    Video Section Two
==================================================================

***/

.video-section-two {
    position: relative;
    padding: 99px 0 252px;
    @include media-breakpoint-down(md){
        padding: 100px 0;
    }
    &:before {
        background-color: rgba(var(--theme-color1-rgb), .9);
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 1;
        -webkit-transition: all 300ms ease;
        transition: all 300ms ease;
    }
    .content {
        text-align: center;
        position: relative;
        z-index: 1;
        .play-now {
            border-radius: 50%;
            position: relative;
            display: inline-block;
            &::before {
                display: none;
            }
            &:hover {
                .icon {
                    color: var(--theme-color1);
                }
            }
            .icon {
                background-color: var(--theme-color-light);
                color: var(--theme-color1);
                font-size: 24px;
                height: 90px;
                width: 90px;
                line-height: 90px;
            }
        }
        h3 {
            color: var(--theme-color-light);
            font-weight: 600;
            line-height: 48px;
            margin-top: 15px;
            br {
                @include media-breakpoint-down(lg){
                    display: none;
                }
            }
        }
    }
}


