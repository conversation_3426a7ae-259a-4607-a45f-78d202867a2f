.theme-btn{
    display:inline-flex;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
    transition:all 0.3s ease;
    @include title-font;
    .btn-title {
        position: relative;
        display: flex;
        align-items: center;
    }
}

/*Btn Style One*/
.btn-style-one {
    position: relative;
    font-size: 12px;
    line-height: 15px;
    padding: 23px 53px;
    font-family: var(--text-font);
    font-weight: 600;
    overflow: hidden;
    color: var(--theme-color-light);
    background: var(--theme-color1);
    border-radius: 3px;
    border: none;
    letter-spacing: 1px;
    text-transform: uppercase;
    z-index: 0;
    &:before {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        height: 100%;
        width: 100%;
        content: "";
        background-color: var(--theme-color-dark);
        border-radius: 0;
        transform: scale(0, 1);
        transform-origin: top right;
        transition: -webkit-transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000), -webkit-transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        z-index: -1;
    }
    &:hover:before {
        transform: scale(1, 1);
        transform-origin: bottom left;
    }
    &:hover {
        color: var(--theme-color-light);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.10);
    }
    &.dark-bg{
        background-color: var(--theme-color-dark);
        &:hover{color: var(--theme-color-light);}
        &::before{background-color: var(--theme-color1);}
    }
    &.light-bg{
        background-color: var(--theme-color-light);
        color: var(--theme-color-dark);
        &:hover{color: var(--theme-color-light);}
        &::before{background-color: var(--theme-color-dark);}
    }
    &.hover-light{
        &:hover{color: var(--theme-color2);}
        &:before {background-color: var(--theme-color-light);}
    }
    &.hover-dark{
        &:hover{color: var(--theme-color-light);}
        &:before {background-color: var(--theme-color-dark);}
    }
}

/*Btn Style Two*/
.btn-style-two {
    background: var(--theme-color1);
    border-radius: 3px;
    border: none;
    color: var(--theme-color-light);
    font-family: var(--text-font);
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    line-height: 15.12px;
    overflow: hidden;
    padding: 22px 37px 22px 36px;
    position: relative;
    text-transform: uppercase;
    z-index: 0;
    &:before {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        content: "";
        background-color: var(--theme-color-dark);
        border-radius: 0;
        transform: scale(0, 1);
        transform-origin: top right;
        transition: -webkit-transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000), -webkit-transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        z-index: -1;
    }
    &:hover:before {
        transform: scale(1, 1);
        transform-origin: bottom left;
    }
    &:hover {
        color: var(--theme-color-light);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.10);
    }
    &.dark-bg{
        background-color: var(--theme-color-dark);
        &:hover{color: var(--theme-color-light);}
        &::before{background-color: var(--theme-color1);}
    }
    &.light-bg{
        background-color: var(--theme-color-light);
        border: 1px solid var(--theme-color-light);
        color: var(--theme-color1);
        &:hover{color: var(--theme-color-light);}
        &::before{background-color: var(--theme-color-dark);}
    }
    &.transparent-bg{
        background-color: transparent;
        border: 1px solid var(--theme-color-light);
        color: var(--theme-color-light);
        &:hover{color: var(--theme-color-light);}
        &::before{background-color: var(--theme-color1);}
    }
    &.hover-light{
        &:hover{color: var(--theme-color2);}
        &:before {background-color: var(--theme-color-light);}
    }
    &.hover-dark{
        &:hover{color: var(--theme-color-light);}
        &:before {background-color: var(--theme-color-dark);}
    }
}

/*Btn Style One*/
.btn-style-transparent {
    background: transparent;
    border-radius: 3px;
    border: 1px solid var(--theme-color1);
    color: var(--theme-color1);
    font-family: var(--text-font);
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    line-height: 15px;
    overflow: hidden;
    padding: 23px 53px;
    position: relative;
    text-transform: uppercase;
    z-index: 0;
    &:before {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        height: 100%;
        width: 100%;
        content: "";
        background-color: var(--theme-color-dark);
        border-radius: 0;
        transform: scale(0, 1);
        transform-origin: top right;
        transition: -webkit-transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition: transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000), -webkit-transform 500ms cubic-bezier(0.860, 0.000, 0.070, 1.000);
        transition-timing-function: cubic-bezier(0.860, 0.000, 0.070, 1.000);
        z-index: -1;
    }
    &:hover:before {
        transform: scale(1, 1);
        transform-origin: bottom left;
    }
    &:hover {
        color: var(--theme-color-light);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.10);
    }
}
