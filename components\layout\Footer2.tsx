'use client'
import Link from "next/link"
import Image from "next/image"

export default function Footer2() {
  return (
    <footer className="main-footer footer-style-two">
      <div className="bg bg-image" style={{ backgroundImage: 'url(/images/background/bg-footer1.jpg)' }}></div>

      <div className="widgets-section">
        <div className="footer1-1 bounce-x"></div>
        <div className="footer-pattrn1 bounce-y"></div>
        <div className="auto-container">
          <div className="row">
            {/* Logo + About */}
            <div className="footer-column col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div className="footer-widget about-widget">
                <div className="logo">
                  <Link href="/" className="d-block position-relative" style={{ width: "120px", height: "70px" }}>
                    <Image
                      src="/images/logo.png"
                      alt="Logo Lavish Shape"
                      fill
                      style={{ objectFit: "contain" }}
                      priority
                    />
                  </Link>
                </div>
                <div className="text">
                  Lavish Shape & Glow Med Spa vous propose des soins esthétiques de qualité dans un espace calme et raffiné à Bonapriso.
                </div>
                <div className="subscribe-form">
                  <form method="post" action="#">
                    <div className="form-group">
                      {/* <input type="email" name="email" className="email" placeholder="Votre e-mail" /> */}
                      {/* <button type="button" className="theme-btn">
                        <span className="btn-title">
                          <i className="fa-sharp fa-thin fa-paper-plane"></i>
                        </span>
                      </button> */}
                    </div>
                  </form>
                </div>
                <ul className="social-icon">
                  <li><Link href="#"><i className="icon fab fa-facebook-f"></i></Link></li>
                  <li><Link href="#"><i className="icon fab fa-instagram"></i></Link></li>
                  <li><Link href="#"><i className="icon fab fa-tiktok"></i></Link></li>
                </ul>
              </div>
            </div>

            {/* Useful Links */}
            <div className="footer-column col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div className="footer-widget links-widget">
                <h3 className="widget-title">Liens utiles</h3>
                <ul className="user-links">
                  <li><Link href="/about">À propos</Link></li>
                  <li><Link href="/pricing">Tarifs</Link></li>
                  <li><Link href="/shop">Boutique</Link></li>
                  <li><Link href="/contact">Contact</Link></li>
                </ul>
              </div>
            </div>

            {/* Schedule */}
            <div className="footer-column col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div className="footer-widget timetable-widget">
                <h3 className="widget-title">Horaires</h3>
                <ul className="timetable">
                  <li>Mardi - Samedi : <span>09h00 - 20h00</span></li>
                  <li>Dimanche : <span>12h00 - 20h00</span></li>
                  <li>Lundi : <span>Fermé</span></li>
                </ul>
              </div>
            </div>

            {/* Contact Info */}
            <div className="footer-column col-xl-3 col-lg-6 col-md-6 col-sm-6">
              <div className="footer-widget contacts-widget">
                <h3 className="widget-title">Contact</h3>
                <div className="text">
                  Boulevard de la Liberté, Bonapriso <br />Douala, Cameroun
                </div>
                <ul className="contact-info">
                  <li><Link className="text-style-two" href="tel:+237699123456">+237 699 123 456</Link></li>
                  <li><Link className="text-style-one" href="mailto:<EMAIL>"><EMAIL></Link></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Copyright */}
      <div className="footer-bottom">
        <div className="auto-container">
          <div className="inner-container">
            <div className="copyright-text">
              &copy; {new Date().getFullYear()} Lavish Shape. Site créé par <Link href="https://agency.briandave.dev">Brian Dave</Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
