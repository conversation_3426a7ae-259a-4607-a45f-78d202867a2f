import BookingPage from '@/components/elements/BookingPage';
import Link from 'next/link';

const Contact = () => {
  return (
    <>
      <section className="contact-details pt-110 pb-60">
        <div className="container">
          {/* Calendar full width */}
          <div className="row mb-5">
            <div className="col-12">
              <BookingPage />
            </div>
          </div>

          {/* Contact Info + Map */}
          <div className="row gy-5 align-items-start">
            {/* Contact Info */}
            <div className="col-lg-6">
              <div className="contact-details__right">
                <div className="sec-title mb-30">
                  <span className="sub-title">Besoin d'aide ?</span>
                  <h2>Contactez-nous</h2>
                  <div className="text">
                    Nous sommes ravis de vous accompagner dans votre parcours bien-être.
                  </div>
                </div>
                <ul className="list-unstyled contact-details__info">
                  <li className="d-flex mb-4">
                    <div className="icon me-3">
                      <span className="lnr-icon-phone-plus"></span>
                    </div>
                    <div className="text">
                      <h6>Appelez-nous</h6>
                      <Link href="tel:+237656214141"><span>WhatsApp</span> +237 656 21 41 41</Link>
                    </div>
                  </li>
                  <li className="d-flex mb-4">
                    <div className="icon me-3">
                      <span className="lnr-icon-envelope1"></span>
                    </div>
                    <div className="text">
                      <h6>Envoyez-nous un mail</h6>
                      <Link href="mailto:<EMAIL>"><EMAIL></Link>
                    </div>
                  </li>
                  <li className="d-flex">
                    <div className="icon me-3">
                      <span className="lnr-icon-location"></span>
                    </div>
                    <div className="text">
                      <h6>Visitez-nous</h6>
                      <span>Bonapriso, Douala, Cameroun</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>

            {/* Map */}
            <div className="col-lg-6">
              <div className="mapouter rounded overflow-hidden shadow-sm">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3979.9896934510302!2d9.695416374734707!3d4.022505895951247!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x106113a8a53439cf%3A0x15eaf3bde7eca026!2sLAVISH%20SHAPE%20%26%20GLOW%20MED%20SPA!5e0!3m2!1sfr!2scm!4v1752373080715!5m2!1sfr!2scm"
                  width="100%"
                  height="500"
                  style={{ border: "none" }}
                  allowFullScreen
                  loading="lazy"
                ></iframe>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Contact;
