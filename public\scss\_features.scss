/*** 

====================================================================
    Features Section
====================================================================

***/

.features-section {
  padding: 0 0 80px;
  .content-column {
    .inner-column {
      position: relative;
      padding-top: 84px;
      @include media-breakpoint-down(xxl){
        padding-top: 50px;
      }
      .sec-title {
        .text {
          margin-top: 11px;
          margin-bottom: 35px;
        }
      }
      .bg-image {
        position: absolute;
        width: 948px;
        height: 600px;
        top: 0;
        right: calc(100% + 126px);
        @include media-breakpoint-down(lg){
          display: none;
        }
      }
    }
  }
}

.list-style-two {
  .feature-block {
    .inner-box {
      position: relative;
      margin-bottom: 35px;
      &:hover {
        .icon {
          transform: rotateY(180deg) !important;
        }
      }
      .icon {
        position: absolute;
        margin-bottom: 0;
        top: -3px;
        @include transition(all 300ms ease);
      }
      .title {
        font-size: 22px;
        font-weight: 500;
        margin-bottom: 0;
        margin-left: 35px;
      }
    }
  }
}

/*** 

====================================================================
    Features Section Two
====================================================================

***/

.features-section-two {
  padding: 120px 0;
  @include media-breakpoint-down(md){
    padding: 90px 0;
  }
  .sec-title {
    margin-bottom: 125px;
    @include media-breakpoint-down(md){
      text-align: center;
      margin-bottom: 90px;
    }
    .text {
      margin-top: 65px;
    }
  }
}

.feature-block-two {
  .inner-box {
    text-align: center;
    position: relative;
    @include media-breakpoint-down(lg){
      margin-bottom: 30px;
    }

    &:hover {
      .icon {
        transform: scale(-1) rotate(180deg);
      }
    }
    .icon {
      color: var(--theme-color1);
      font-size: 110px;
      min-height: 164px;
      display: flex;
      justify-content: center;
      align-items: center;
      @include transition(all 300ms ease);
    }
    .title {
      font-size: 32px;
      font-weight: 400;
      margin-bottom: 21px;
      margin-top: 30px;
      &:hover {
        color: var(--theme-color1);
      }
    }
    .text {
      margin: 0 30px;
      @include transition(all 300ms ease);
      @include media-breakpoint-down(xl){
        margin: 0;
      }
    }
  }
}