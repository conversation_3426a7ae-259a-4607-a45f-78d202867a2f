/* Purerelax | Spa & Beauty HTML Template

Template Name: Purerelax
Version: 1.0.0
License: copyright commercial

/************ TABLE OF CONTENTS ***************
1.  Fonts
2.  Reset
3.  Global Settings
4.  Main Header / TWo / Three
5.  <PERSON>y Header 
6.  Mobile Menu
7.  Hidden Sidebar style
8.  Section Title
9.  Banner Section / Two / Three
10. Project Section / Two / Three
11. Features Section / Two / Three
12. About Section / Two / Three
13. Services Section / Two / Three
14. Video Section / Two
15. Contact Section
16. FAQ's Section / Two
17. Fun Fact Section / Two
18. Testimonial Section / Two
19. Process Section / Two
20. Team Section
21. Why Choose Us / Two / Three
22. News Section
23. Main Footer
**********************************************/

@import "scss-bootstrap/functions";
@import "scss-bootstrap/variables";
@import "scss-bootstrap/mixins";

@import "common/root.scss";
@import "common/mixing.scss";
@import "common/loader.scss";

@import url('https://fonts.googleapis.com/css2?family=Alex+Brush&family=Cormorant:ital,wght@0,300;0,400;0,500;0,600;0,700;1,600&family=Plus+Jakarta+Sans:ital,wght@0,400;0,500;0,600;0,700;0,800;1,400;1,500;1,600;1,700&display=swap');

//@import url('animate.css');
//@import url('owl.css');
@import url('swiper.min.css');
//@import url('jquery.fancybox.min.css');
//@import url('jquery-ui.css');
@import url('linear.css');
@import url('select2.min.css');
@import url('fontawesome.css');
@import url('flaticon-set-logistics.css');
@import url('tm-bs-mp.css');
@import url('tm-utility-classes.css');


// import SCSS components one by one
@import "reset.scss";
@import "button.scss";
@import "animate-icon.scss";
@import "search-popup.scss";
@import "header.scss";
@import "instagram.scss";
@import "mobile-menu.scss";
@import "gallery.scss";
@import "section-title.scss";
@import "banner.scss";
@import "projects.scss";
@import "pricing.scss";
@import "features.scss";
@import "about.scss";
@import "marquee.scss";
@import "main-slider.scss";
@import "services.scss";
@import "video.scss";
@import "contact.scss";
@import "faqs.scss";
@import "funfact.scss";
@import "testimonials.scss";
@import "process.scss";
@import "product.scss";
@import "team.scss";
@import "clients.scss";
@import "why-choose-us.scss";
@import "blog.scss";
@import "footer.scss";
@import "modal-video.scss";


@import "layout4.scss";


@import "shop/loader.scss";

//inner
@import "inner/page-title.scss";
@import "inner/404.scss";
@import "inner/service-details.scss";
@import "inner/news-details.scss";
@import "inner/news-sidebar.scss";
@import "inner/news-comment.scss";
@import "inner/team-details.scss";
@import "inner/contact-page.scss";