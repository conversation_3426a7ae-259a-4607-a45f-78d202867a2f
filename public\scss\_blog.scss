/*** 

====================================================================
    Blog Section
====================================================================

***/

.blog-section {
    position: relative;
    padding: 40px 0 145px;
    @include media-breakpoint-down(md){
        padding: 0 0 160px;
    }
    .sec-title {
        margin-bottom: 50px;
    }
    .default-dots{
        .owl-dots {
            left: 0;
            position: absolute;
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            bottom: -75px;
            .owl-dot {
                height: 12px;
                width: 12px;
                margin: 0 8.5px;
                background-color: transparent;
                border: 1px solid #707070;
                border-radius: 50%;
                @include transition(all 300ms ease);
                &.active {
                    width: 15px;
                    height: 15px;
                    background-color: var(--theme-color1);
                    border-color: var(--theme-color1);
                    @include transition(all 300ms ease);
                }
            }
        }
    }
}

.blog-block {
    margin-bottom: 40px;
    .inner-box {
        position: relative;
        @include transition(all 300ms ease);
        &:hover {
            .image-box {
                .image {
                    img {
                        &:first-child{
                            transform: translateX(0) scaleX(1);
                            opacity: 1;
                            filter: blur(0);
                        }
                        &:nth-child(2){
                            transform: translateX(-50%) scaleX(2);
                            opacity: 0;
                            filter: blur(10px);
                        }
                    }
                }
            }
        }
    }
    .image-box {
        position: relative;
        .image {
            position: relative;
            overflow: hidden;
            margin-bottom: 0;
            img {
                position:relative;
                width:100%;
                display:block;
                transition:all 500ms ease;
                &:first-child{
                    position:absolute;
                    left:0px;
                    top:0px;
                    right:0px;
                    bottom:0px;
                    z-index:1;
                    transform: translateX(50%) scaleX(2);
                    opacity: 0;
                    filter: blur(10px);
                }
            }
        }
        .date{
            position: absolute;
            left: 30px;
            bottom: 30px;
            width: 72px;
            display: block;
            z-index: 3;
            strong {
                position: relative;
                display: block;
                width: 72px;
                font-size: 30px;
                font-weight: 700;
                font-family: var(--title-font);
                padding: 5px 0 13px;
                line-height: 1;
                color: var(--theme-color-light);
                background-color: var(--theme-color1);
                text-align: center;
                span {
                    display: block;
                    font-size: 14px;
                    font-family: var(--text-font);
                    margin-top: 4px;
                }
            }
        }
    }
    .content-box {
        margin-top: 18px;
        .post-meta {
            display: flex;
            align-items: center;
            margin-left: 53px;
            margin-bottom: 19px;          
            li {
                position: relative;
                font-family: var(--title-font);
                font-size: 22px;
                font-weight: 500;
            }
            .categories {
                &:hover {
                    a {
                        color: var(--theme-color1);
                    }
                }
                &:before {
                    content: "";
                    position: absolute;
                    border-bottom: 1px solid #1C1A1D;
                    width: 43px;
                    left: -53px;
                    bottom: 3px;
                }
                a {
                    color: #092C4C;
                }
            }
            .date {
                color: #707070;
                margin-left: 36px;
                &:before {
                    bottom: 10px;
                    bottom: 13px;
                    border-radius: 50%;
                    background-color: #D25239;
                    content: "";
                    position: absolute;
                    width: 3px;
                    height: 3px;
                    left: -20px;
                }
            }
        }
        .title {
            color: #092C4C;
            font-weight: 500;
            margin-bottom: 20px;
            @include media-breakpoint-down(sm){
                font-size: 26px;
                margin-bottom: 20px;
            }
            &:hover {
                color: var(--theme-color1);
            }
        }
        .read-more{
            color: var(--theme-color-dark);
            font-size: 22px;
            font-weight: 500;
            line-height: 1;
            margin-bottom: 0;
            position: relative;
            font-family: var(--title-font);
            @include transition(all 300ms ease);
            &:before {
                background-color: var(--theme-color-dark);
                content: "";
                height: 1px;
                position: absolute;
                top: 50%;
                right: -60px;
                bottom: 0;
                width: 51px;
            }
            &:after {
                background-color: var(--theme-color1);
                content: "";
                height: 1px;
                position: absolute;
                top: 50%;
                right: -9px;
                bottom: 0;
                width: 0;
                @include transition(all 300ms ease);
            }
            &:hover {
                color: var(--theme-color1);
                &:after {
                    right: -60px;
                    width: 51px;
                }
                .icon {
                    color: var(--theme-color1);
                }
            }
            .icon {
                color: var(--theme-color-dark);
                font-size: 12px;
                position: absolute;
                bottom: 6px;
                right: -62px;
                @include transition(all 500ms ease);
            }
        }
    }
}


/*** 

====================================================================
    Blog Section Two
====================================================================

***/

.blog-section-two {
    position: relative;
    padding: 122px 0 95px;
    @include media-breakpoint-down(md){
        padding: 50px 0 70px;
    }
    .sec-title {
        margin-bottom: 50px;
    }
}