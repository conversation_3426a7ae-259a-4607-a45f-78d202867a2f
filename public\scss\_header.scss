
/*** 

====================================================================
Main Header
====================================================================

***/

.main-header {
  position: relative;
  width: 100%;
  z-index: 999;
}

.header-top {
  position: relative;
  display: flex;
  .inner-container{
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .top-left { 
    position: relative;
    display: flex;
    align-items: center;
  }
  .top-center {
    @include media-breakpoint-down(xxl){
      display: none;
    }
  }
  .top-right {
    position: relative;
    display: flex;
    align-items: center;
  }
  .useful-links{
    display: flex;
    align-items: center;
    padding: 10px 0;
    li{
      position: relative;
      margin-right: 30px;
      font-size: 12px;
      font-weight: 500;
      color: #bdbdbd;
      line-height: 20px;
      @include title-font;
      a{
        color: #bdbdbd;
        @include transition(all 300ms ease);
        &:hover{
          color: var(--theme-color1);
        }
      }
    }
  }
}

.main-header {
  .logo {
    position: relative;
    display: block;
    img {
      max-width: 100%;
      height: auto;
    }
  }
  .main-box {
    position: relative;
    left: 0px;
    top: 0px;
    display: flex;
    align-items: center;
    @include transition(all 300ms ease);
    @media (max-width: 1023.98px) {
      padding: 20px 0;
    }
    .nav-outer {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      @media (max-width: 1023.98px) {
        justify-content: end;
      }
    }
  }
}

.main-menu {
  position:relative;
  @include for-md {
    display: none;
  }
}

.main-menu .navbar-header{
  display: none;
}

.main-menu .navbar-collapse{
  padding:0px;    
}

.main-menu .navigation{
  position:relative;
  margin: 0 0 0 150px;
  top: 0;
  @media (max-width: 1649.98px) {
    margin: 0 0 0 100px;
  }
  @include media-breakpoint-down(xxl){
    margin: 0 0 0 70px;
  }
  @media (max-width: 1182.98px) {
    margin: 0 0 0 44px;
  }
}

.main-menu .navigation > li{
  position:relative;
  float:left;
  padding: 42.5px 0;
  margin-right: 60px;
  transition:all 300ms ease;
  @media (max-width: 1649.98px) {
    margin-right: 70px;
  }
  @include media-breakpoint-down(xxl){
    margin-right: 40px;
  }
  @media (max-width: 1084.98px) {
    margin-right: 32px;
  }
  &:hover:before,
  &.current:before {
    color: var(--theme-color1);
    left: -0;
    width: 100%;
  }
  &:last-child {
    margin-right: 0;
    &:after {
      display: none;
    }
  }
  >a{
    position: relative;
    display: block;
    text-align: center;
    opacity: 1;
    color: var(--theme-color-dark);
    font-size: 18px;
    line-height: 22.68px;
    font-weight: 500;
    padding: 0;
    display: flex;
    @include transition(all 300ms ease);
    &:hover {
      color: var(--theme-color1);
    }
    .icon {
      position: relative;
      font-size: 20px;
      line-height: 24px;
      margin-left: 10px;
    }
  }
  &.dropdown>a {
    padding-right: 14px;
    margin-right: -14px;
  }
  &.dropdown>a:after {
    content: "\f107";
    display: block;
    font-family: "Font Awesome 6 Pro";
    font-size: 12px;
    font-weight: 900;
    height: 20px;
    line-height: 24px;
    margin-top: -2px;
    position: absolute;
    right: 0;
    top: 50%;
    width: 10px;
    z-index: 5;
    transform: translateY(-50%);
  }
  &.dropdown:hover>ul {
    visibility: visible;
    opacity: 1;
    top: 100%;
    margin-top: 0;
    @include transition(all 300ms ease);
  }
}

.main-menu .navigation > li > ul{
  position: absolute;
  left: 0px;
  top: 100%;
  width: 220px;
  z-index: 100;
  padding: 10px 0 0;
  background-color: #ffffff;
  margin-top: 30px;
  opacity: 0;
  display: none;
  box-shadow: 0 0 3px rgb(0 0 0 / 10%);
  box-shadow: 2px 2px 5px 1px rgb(0 0 0 / 5%), -2px 0px 5px 1px rgb(0 0 0 / 5%);
  &.from-right{
    left:auto;
    right:0px;  
  }
  >li{
    position:relative;
    width:100%;
    border-bottom: 1px solid #ebf1f5;
    &:last-child {
      border-bottom: none;
    }
    >a{
      position:relative;
      display:block;
      padding:10px 0px;
      line-height:29px;
      font-weight:400;
      font-size:16px;
      color:var(--theme-color-dark);
      text-align:left;
      margin: 0 30px;
      text-transform:capitalize;
      transition:all 200ms ease;
    }
    &:hover > a{
      color:var(--theme-color1);
    }
    &.dropdown > a:after{
      font-family: 'Font Awesome 6 Pro';
      content: "\f105";
      position:absolute;
      right:0;
      top:11px;
      display:block;
      line-height:24px;
      font-size:11px;
      font-weight:900;
      z-index:5;  
    }
    &.dropdown:hover>ul {
      visibility: visible;
      opacity: 1;
      top: 0px;
      margin-top: 20px;
      @include transition(all 300ms ease);
    }
  }
}

.main-menu .navigation > li > ul > li > ul{
  position:absolute;
  left:100%;
  top:0px;
  width:220px;
  z-index:100;
  display:none;
  background-color:var(--theme-color-light);
  opacity:0;
  padding: 10px 0 0;
  margin-top: 10px;
  transform:translateY(-30px);
  box-shadow:2px 2px 5px 1px rgba(0,0,0,0.05),-2px 0px 5px 1px rgba(0,0,0,0.05);
  > li{
    position:relative;
    border-bottom: 1px solid #ebf1f5;
    width:100%;
    &:last-child{
      border-bottom:none; 
    }
    > a{
      position:relative;
      display:block;
      padding:10px 0;
      line-height:24px;
      font-weight:400;
      font-size:16px;
      color:var(--theme-color-dark);
      text-align:left;
      margin: 0 30px;
      text-transform:capitalize;
      transition:all 300ms ease;
      &:hover{
        color:var(--theme-color1);
      }
    }
  }
}

.main-menu .navigation li.dropdown .dropdown-btn{
  position:absolute;
  right:10px;
  top:8px;
  width:34px;
  height:30px;
  border:1px solid var(--theme-color-light);
  text-align:center;
  font-size:16px;
  line-height:26px;
  color:var(--theme-color-light);
  cursor:pointer;
  z-index:5;
  display:none;
}

.main-header .outer-box {
  max-width: 1650px;
  margin: 0 auto;
  padding: 0 15px 1px;
  @media (max-width: 1023.98px) {
    padding: 0;
  }
}


.main-header .ui-btn {
  position: relative;
  display: block;
  line-height: 30px;
  text-align: center;
  background: none;
  font-size: 22px;
  color: var(--theme-color-dark);
  margin-left: 15px;
  @include transition(all 300ms ease);
  &:hover {
    color: var(--theme-color1);
  }
}

.main-header .info-btn {
  position: relative;
  display: flex;
  justify-content: center;
  flex-direction: column;
  font-size: 15px;
  padding-left: 70px;
  color: var(--theme-color-dark);
  text-align: left;
  font-weight: 700;
  line-height: 20px;
  min-height: 62px;
  @include title-font;
  small {
    display: block;
    font-size: 12px;
    line-height: 1em;
    font-weight: 400;
    color:#868686;
    margin-bottom: 0px;
  }
  i {
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -24px;
    line-height: 48px;
    height: 48px;
    width: 48px;
    text-align: center;
    border-radius: 50%;
    font-size: 22px;
    color: #fff;
    background-color:var(--theme-color1);
    &:after{
      position: absolute;
      top: -7px;
      right: -7px;
      bottom: -7px;
      left: -7px;
      background-color: var(--theme-color1);
      opacity: .10;
      border-radius: 50%;
      content: "";
    }
  }
  &:hover{
    color: var(--theme-color1);
  }
}

.contact-list{
  position: relative;
  display: flex;
  align-items: center;
  li{
    position: relative;
    padding-left: 65px;
    margin-left: 35px;
    .icon{
      position: absolute;
      left: 0;
      top: 0;
      height: 50px;
      width: 50px;
      background-color: rgba(255, 255, 255, .05);
      @include flex-center;
      font-size: 20px;
      color: var(--theme-color1);
    }
    .title{
      font-size: 16px;
      line-height: 20px;
      color: #ffffff;
      font-weight: 600;
      @include text-font;
      margin-bottom: 2px;
    }
    .text{
      font-size: 16px;
      line-height: 24px;
      color: #cecece;
      a{
        color: inherit;
      }
    }
  }
}

.main-header .cart-btn{
  position: relative;
  margin: 0;
  color: var(--theme-color-light);
  margin-right: 30px;
  text-align: center;
  @include media-breakpoint-down(xl){
    display: none;
  }
  &:before {
    background: #e5e5e5;
    content: "";
    position: absolute;
    right: -38px;
    width: 1px;
    height: 80%;
    top: 3px;
  }
  &:hover{
    color: var(--theme-color1);
  }
  .count{
    position: absolute;
    top: 1px;
    right: -8px;
    height: 21px;
    width: 21px;
    line-height: 21px;
    font-size: 12px;
    border-radius: 50%;
    color: #ffffff;
    background-color: var(--theme-color1);
    text-align: center;
  }
}

.header-search {
  position: relative;
  width: 160px;
  .form-group {
    position: relative;
    input:not([type=submit]) {}
    .icon {
      position: absolute;
      top: 0px;
      right: 0;
      @include flex-center;
      line-height: 20px;
      font-size: 20px;
      padding: 5px 0;
      color: rgba(255, 255, 255, .6);
      background: none;
    }
  }
}

.main-header .search-btn {
  display: flex;
  padding-left: 72px;
  padding-right: 30px;
  text-align: center;
  @media (max-width: 1699.98px) {
    padding-right: 0;
  }
  &:hover {
    color: var(--theme-color1);
    strong {
      color: var(--theme-color1);
    }
  }
}

/*** 

====================================================================
    Header Style
====================================================================

***/

.main-header {
  background-color: #F9F6F1;
  position: relative;
  .header-top {
    padding: 6.5px 0;
    @media (max-width: 1023.98px) {
      display: none;
    }
    .inner-container {
      .list-style-one {
        justify-content: center;
        i {
          color: var(--theme-color1);
          font-size: 12px;
        }
        &.style-two{
          li{
            margin-right: 30px;
            color: var(--theme-color2);
            &:last-child {
              margin-right: 45px !important;
            }
            a {
              color: var(--theme-color2);
              &:hover {
                color: var(--theme-color1);
              }
            }
          }
        }
      }
      .social-icon-one {
        li {
          position: relative;
          margin: 0 16px 0 0;
          &:last-child {
            margin-right: 0;
          }
          a {
            font-size: 15px;
            color: var(--theme-color2);
            &:hover {
              color: var(--theme-color1);
            }
          }
        }
      }

      .top-right{
        display: flex;
        justify-content: flex-end;
        .list-style-two {
          li {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 0;
            padding-left: 0;
            margin-right: 40px;
            a {
              color: var(--theme-color-dark);
              font-weight: 400;
              &.active {
                color: var(--theme-color1);
              }
              &:hover {
                color: var(--theme-color1);
              }
            }
          }
        }
      }

    }
    &.light {
      .inner-container{
        .list-style-one {
          li {
            color: var(--theme-color-light);
            a {
              color: var(--theme-color-light);
              &:hover {
                color: var(--theme-color1);
              }
            }
          }
          i {
            color: var(--theme-color-light);
          }
          &.style-two{
            li{
              color: var(--theme-color-light);
              a {
                color: var(--theme-color-light);
                &:hover {
                  color: var(--theme-color1);
                }
              }
            }
          }
        }
        .social-icon-one {
          li {
            &:before {
              background-color: rgba(255, 255, 255, .1);
            }
            &:last-child {
              &:after {
                background-color: rgba(255, 255, 255, .1);
              }
            }
            a {
              color: var(--theme-color-light);
              &:hover {
                color: var(--theme-color1);
              }
            }
          }
        }

      }
    }

  }

  .header-lower {
    background-color: var(--theme-color-light);
    border-radius: 10px 10px 0 0;
    padding: 0 40px;
    @include media-breakpoint-down(xxl){
      padding: 0 30px;
    }
    @include media-breakpoint-down(xl){
      padding: 0 15px;
    }
    .logo-box{
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 194px;
      height: 48px;
    }
    .outer-box{
      display: flex;
      align-items: center;
      position: relative;
      padding-left: 30px;
      padding-right: 0;
      @include media-breakpoint-down(xxl){
        padding-left: 9px;
        padding-right: 0;
      }
      @media (max-width: 1023.98px) {
        padding-left: 0;
      }
      &:before {
        content: "";
        position: absolute;
        border: 1px solid #F9F6F1;
        left: 0;
        height: 108px;
        @media (max-width: 1023.98px) {
          display: none;
        }
      }
      .theme-btn {
        @include media-breakpoint-down(xxl){
          padding: 17px 27px;
        }
        @media (max-width: 1023.98px) {
          display: none;
        }
      }
      .ui-btn {
        width: 30px;
        height: 30px;
        line-height: 30px;
        margin-right: 25px;
        padding: 0;
        color: var(--theme-color-dark);
        @include media-breakpoint-down(xxl){
          margin-right: 14px;
        }
        @include media-breakpoint-down(sm){
          margin-right: 0;
          margin-left: 0;
        }
        &:hover {
          color: var(--theme-color1);
        }
        i {
          font-weight: 500;
        }
      }
      .btn-box {
        @include media-breakpoint-down(md){
          display: none;
        }
      }
    }
    &.light {
      .main-menu {
        .navigation {
          > li > a {
            color: var(--theme-color-light);
          }
        }
      }
      .outer-box{
        .ui-btn {
          color: var(--theme-color-light);
          &:hover {
            color: var(--theme-color1);
          }
          &.cart-btn {
            &:before {
              background-color: rgba(255, 255, 255, .1);
            }
          }
        }
        .call-btn {
          a {
            color: var(--theme-color-light);
            strong {
              color: var(--theme-color-light);
            }
            &:hover {
              color: var(--theme-color1);
              strong {
                color: var(--theme-color1);
              }
            }
          }
        }
      }
    }
  }
}

.header-style-three {
  background-color: #f4ecdf;
}

.header-style-two {
  padding: 0;
  .header-lower {
    padding: 0;
    .main-menu {
      .navigation {
        margin: 0 0 0 80px;
      }
    }
    .main-box {
      max-width: 1650px;
      padding: 0 10px;
      margin: 0 auto;
      .outer-box {
        &:before {
          content: "";
          display: none;
        }
        .mobile-nav-toggler {
          display: block;
          background-color: var(--theme-color-dark);
          padding: 43.5px 0;
          width: 100px;
          margin: 0;
          font-size: 39px;
          text-align: center;
          &:hover {
            span {
              color: var(--theme-color1);
            }
          }
          span {
            color: var(--theme-color-light);
            @include transition(all 300ms ease);
          }
        }
        .cart-btn {
          background-color: var(--theme-color1);
          padding: 53.999px 0;
          width: 100px;
          line-height: 7px;
          margin: 0;
          @include transition(all 300ms ease);
          &:hover {
            i {
              color: var(--theme-color-dark);
            }
            .count {
              background-color: var(--theme-color-light);
              color: var(--theme-color-dark);
            }
          }
          &:before {
            content: "";
            display: none;
          }
          &:after {
            content: "";
            display: none;
          }
          i {
            color: var(--theme-color-light);
            font-size: 32px;
            font-weight: 300;
            @include transition(all 300ms ease);
          }
          .count {
            background-color: var(--theme-color-dark);
            top: 34px;
            right: 23px;
            width: 22px;
            line-height: 22px;
            font-size: 10px;
            font-weight: 500;
            @include transition(all 300ms ease);
          }
        }
      }
      .nav-outer {
        .mobile-nav-toggler {
          display: none;
        }
      }
    }
  }
}


/***

====================================================================
    Sticky Header
====================================================================

***/

.sticky-header {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  left: 0px;
  top: 0px;
  width: 100%;
  padding: 0px 0px;
  z-index: 99999;
  background: #ffffff;
  -webkit-box-shadow: 0 0 20px rgba(0,0,0,.05);
  box-shadow: 0 0 20px rgba(0,0,0,.05);
}

.sticky-header.fixed-header{
  opacity:1;
  z-index: 9999;
  visibility:visible;
}

.sticky-header{
  .logo {
    padding: 19px 0 20px;
    img {
      max-height: 40px;
    }
  }
  .nav-outer {
    position: relative;
    background: none;
  }
  .inner-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}


.sticky-header .main-menu .navigation > li{
  margin: 0;
  margin-left: 60px;
  padding: 20px 0;
}
.sticky-header .main-menu .navigation.onepage-nav > li{
  margin-left: 35px;
}
.header-style-one.header-style-home5 .sticky-header .main-menu .navigation.onepage-nav > li{
  margin-right: 10px;
}
@media (max-width: 1499.98px) {
  .main-menu .navigation.onepage-nav > li,
  .sticky-header .main-menu .navigation.onepage-nav > li{
    margin-right: 28px;
  }  
}

.sticky-header .main-menu .navigation > li.current > a, 
.sticky-header .main-menu .navigation > li:hover > a{
  color: var(--theme-color-dark);
}

.sticky-header .outer-box,
.sticky-header .navbar-header {
  display: none;
}

.sticky-header .mobile-nav-toggler {
  color: var(--theme-color-dark);
}


@media only screen and (min-width: 768px){
	.main-menu .navigation > li > ul,
	.main-menu .navigation > li > ul > li > ul{
		display:block !important;
		visibility:hidden;
		opacity:0;
	}
}