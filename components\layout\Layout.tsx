'use client'

import { useEffect, useState, ReactNode } from "react"
import BackToTop from '../elements/BackToTop'
import Footer1 from './Footer1'
import Footer2 from './Footer2'
import Header1 from "./Header1"
import Header2 from './Header2'
import Header3 from './Header3'
import Header4 from './Header4'
import Header5 from './Header5'
import PageHead from './PageHead'

interface LayoutProps {
    headerStyle?: number;
    footerStyle?: number;
    headTitle?: string;
    breadcrumbTitle?: string;
    children?: ReactNode;
}

export default function Layout({
    headerStyle,
    footerStyle,
    headTitle,
    breadcrumbTitle,
    children
}: LayoutProps) {
    const [scroll, setScroll] = useState<boolean>(false)

    // Mobile Menu
    const [isMobileMenu, setMobileMenu] = useState<boolean>(false)
    const handleMobileMenu = (): void => {
        setMobileMenu(!isMobileMenu)
        !isMobileMenu ? document.body.classList.add("mobile-menu-visible") : document.body.classList.remove("mobile-menu-visible")
    }

    const [isSearch, setSearch] = useState<boolean>(false)
    const handleSearch = (): void => setSearch(!isSearch)

    useEffect(() => {
        const handleScroll = (): void => {
            const scrollCheck = window.scrollY > 100
            if (scrollCheck !== scroll) {
                setScroll(scrollCheck)
            }
        }

        document.addEventListener("scroll", handleScroll)

        // Cleanup event listener
        return () => {
            document.removeEventListener("scroll", handleScroll)
        }
    }, [scroll])

    return (
        <>
            <div className="page-wrapper" id="top">
                {!headerStyle && <Header1 scroll={scroll} isMobileMenu={isMobileMenu} handleMobileMenu={handleMobileMenu} isSearch={isSearch} handleSearch={handleSearch} />}
                {headerStyle === 1 && <Header1 scroll={scroll} isMobileMenu={isMobileMenu} handleMobileMenu={handleMobileMenu} isSearch={isSearch} handleSearch={handleSearch} />}
                {headerStyle === 2 && <Header2 scroll={scroll} isMobileMenu={isMobileMenu} handleMobileMenu={handleMobileMenu} isSearch={isSearch} handleSearch={handleSearch} />}
                {headerStyle === 3 && <Header3 scroll={scroll} isMobileMenu={isMobileMenu} handleMobileMenu={handleMobileMenu} isSearch={isSearch} handleSearch={handleSearch} />}
                {headerStyle === 4 && <Header4 scroll={scroll} isMobileMenu={isMobileMenu} handleMobileMenu={handleMobileMenu} isSearch={isSearch} handleSearch={handleSearch} />}
                {headerStyle === 5 && <Header5 scroll={scroll} isMobileMenu={isMobileMenu} handleMobileMenu={handleMobileMenu} isSearch={isSearch} handleSearch={handleSearch} />}

                <main className="main">
                    {children}
                </main>

                {!footerStyle && <Footer1 />}
                {footerStyle === 1 && <Footer1 />}
                {footerStyle === 2 && <Footer2 />}
            </div>
            <BackToTop />
        </>
    )
}