/***

==================================================================
    Gallery Section
==================================================================

***/

.gallery-section {
    padding: 24px 0 0;
    position: relative;
}


.gallery-block {
    .inner-box {
        .image-box {
            align-items: center;
            display: flex;
            position: relative;
            justify-content: center;
            margin-bottom: 24px;
            &:hover {
                .image {
                    &:before {
                        transform: scale(1);
                    }
                    a {
                        img {
                            transform: scale(1.15);
                        }
                    }
                }
                .icon {
                    transform: scale(1);
                }
            }
            .image {
                margin-bottom: 0;
                overflow: hidden;
                width: 100%;
                &:before {
                    background-color: #000;
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 0;
                    height: 100%;
                    width: 100%;
                    transform: scale(0);
                    pointer-events: none;
                    opacity: 20%;
                    z-index: 1;
                    @include transition(all 300ms ease);
                }
                a {
                    img {
                        width: 100%;
                        @include transition(all 300ms ease);
                    }
                }
            }
            .icon {
                background-color: var(--theme-color1);
                border-radius: 50%;
                color: var(--theme-color-light);
                position: absolute;
                font-size: 38px;
                width: 84px;
                height: 84px;
                line-height: 84px;
                transform: scale(0);
                text-align: center;
                z-index: 2;
                @include transition(all 300ms ease);
                &:hover {
                    background-color: var(--theme-color-light);
                    color: var(--theme-color1);
                }
            }
        }
    }
}


