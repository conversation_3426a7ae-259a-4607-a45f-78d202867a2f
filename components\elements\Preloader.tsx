'use client'
import { useEffect, useState } from 'react'

interface PreloaderProps {
  children: React.ReactNode
  minLoadTime?: number // Minimum time to show preloader (in ms)
  additionalDelay?: number // Additional delay after page load (in ms)
}

export default function Preloader({ 
  children, 
  minLoadTime = 1000,
  additionalDelay = 500 
}: PreloaderProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [fadeOut, setFadeOut] = useState(false)

  useEffect(() => {
    const startTime = Date.now()
    let isPageLoaded = false

    const handleLoad = () => {
      isPageLoaded = true
      checkIfReadyToHide()
    }

    const checkIfReadyToHide = () => {
      const elapsedTime = Date.now() - startTime
      const remainingTime = Math.max(0, minLoadTime - elapsedTime)

      setTimeout(() => {
        if (isPageLoaded) {
          // Start fade out animation
          setFadeOut(true)
          
          // Hide preloader after fade animation
          setTimeout(() => {
            setIsLoading(false)
          }, 300) // Match CSS transition duration
        }
      }, remainingTime + additionalDelay)
    }

    // Check if page is already loaded
    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
    }

    // Cleanup
    return () => {
      window.removeEventListener('load', handleLoad)
    }
  }, [minLoadTime, additionalDelay])

  if (isLoading) {
    return (
      <div className={`preloader-wrapper ${fadeOut ? 'fade-out' : ''}`}>
        <div className="preloader"></div>
      </div>
    )
  }

  return <>{children}</>
}