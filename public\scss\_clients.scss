/***

====================================================================
    Clients Section
====================================================================

***/

.clients-section{
    position: relative;
    padding: 100px 0 96px;
    .swiper-wrapper{
      padding-top: 50px;
    }
    .carousel-outer {
      margin: 0 -44px;
    }
    .clients-carousel{
      &:before{
        border: 1px solid #000;
        content: "";
        opacity: 10%;
        position: absolute;
        top: 10px;
        width: 100%;
      }
      .swiper-button-prev,
      .swiper-button-next{
        background-color: #F9F6F1;
        color: var(--theme-color-dark);
        font-size: 8px;
        height: 16px;
        line-height: 16px;
        opacity: 1;
        position: absolute;
        width: 16px;
        text-align: center;
        -webkit-transition: all 300ms ease;
        transition: all 300ms ease;
        &:after{
          font-size: 8px;
        }
      }
      .swiper-button-prev{
        left: 48.8%;
        right: auto;
        top: 25px;
        @media (max-width: 1199.98px) {
          left: 48%;
        }
      }
      .swiper-button-next{
        left: auto;
        right: 48.8%;
        top: 25px;
        @media (max-width: 1199.98px) {
          right: 48%;
        }
      }
    }
  }
  
  // Client Block
  .client-block {
    .inner-box {
      &:hover {
        .image-box {
          .image {
            img {
              opacity: .6;
            }
          }
        }
      }
      .image-box {
        .image {
          text-align: center;
          margin: 0;
          display: flex;
          justify-content: center;
          img {
            opacity: 1;
            width: auto;
            @include transition(all 300ms ease);
          }
        }
      }
    }
  }
  
  
  