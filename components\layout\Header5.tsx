'use client'
import Link from "next/link"
import Menu from "./Menu"
import MobileMenu from "./MobileMenu"
import Image from "next/image"
import LanguageSwitcher from "../elements/LanguageSwitcher"

interface Header5Props {
    scroll: boolean
    isMobileMenu: boolean
    handleMobileMenu: () => void
    isSearch: boolean
    handleSearch: () => void
}

export default function Header5({
    scroll,
    isSearch,
    handleSearch,
    isMobileMenu,
    handleMobileMenu,
}: Header5Props) {
    return (
        <header className={`main-header header-style-three ${isSearch ? "mobile-search-active" : ""}`}>
            <div className="outer-box">
                {/* Top bar */}
                <div className="header-top">
                    <div className="inner-container">
                        <div className="top-left">
                            <ul className="list-style-one">
                                <li><Link href="mailto:<EMAIL>"><EMAIL></Link></li>
                            </ul>
                        </div>
                        <div className="top-right">
                            <ul className="list-style-two">
                                <li>Mar – Sam : 09h00 – 20h00 • Dim : 12h00 – 20h00 • Lun : Fermé</li>
                            </ul>
                            <ul className="social-icon-one">
                                <li><Link href="#"><span className="icon fab fa-facebook"></span></Link></li>
                                <li><Link href="#"><span className="icon fab fa-instagram"></span></Link></li>
                                <li><Link href="#"><span className="icon fab fa-tiktok"></span></Link></li>
                                <li className="ms-3"><LanguageSwitcher /></li>
                            </ul>
                        </div>
                    </div>
                </div>

                {/* Main logo + menu */}
                <div className="header-lower">
                    <div className="main-box d-flex align-items-center justify-content-between">
                        <div className="logo-box">
                            <Link href="/" className="d-block position-relative" style={{ width: "140px", height: "90px", marginRight: "50px" }}>
                                <Image
                                    src="/images/logo.png"
                                    alt="Logo"
                                    fill
                                    style={{ objectFit: "contain" }}
                                    priority
                                    quality={10}
                                />
                            </Link>
                        </div>

                        <nav className="nav main-menu">
                            <Menu />
                        </nav>

                        <div className="d-flex align-items-center">
                            <div className="divider mx-3"></div>
                            <div className="btn-box d-none d-xl-block">
                                <Link href="/booking" className="theme-btn btn-style-two">
                                    <span className="btn-title">Réserver maintenant</span>
                                </Link>
                            </div>
                            <div className="mobile-nav-toggler ms-3" onClick={handleMobileMenu}>
                                <span className="icon lnr-icon-bars"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            <div className="mobile-menu">
                <div className="menu-backdrop" onClick={handleMobileMenu} />
                <nav className="menu-box">
                    <div className="upper-box d-flex align-items-center justify-content-between">
                        <Link href="/" className="d-block position-relative" style={{ width: "140px", height: "90px" }}>
                            <Image
                                src="/images/logo.png"
                                alt="Logo"
                                fill
                                style={{ objectFit: "contain" }}
                                priority
                                quality={10}
                            />
                        </Link>
                        <div className="d-flex align-items-center gap-3">
                            <LanguageSwitcher />
                            <div className="close-btn" onClick={handleMobileMenu}><i className="icon fa fa-times" /></div>
                        </div>
                    </div>
                    <MobileMenu />
                    <ul className="contact-list-one">
                        <li><div className="contact-info-box"><i className="icon lnr-icon-phone-handset"></i><span className="title">Appelez maintenant</span><Link href="tel:+237699123456">+237 699 123 456</Link></div></li>
                        <li><div className="contact-info-box"><i className="icon lnr-icon-envelope1"></i><span className="title">Envoyez un e‑mail</span><Link href="mailto:<EMAIL>"><EMAIL></Link></div></li>
                    </ul>
                    <ul className="social-links d-flex">
                        <li className="me-2"><Link href="#"><i className="fab fa-facebook" /></Link></li>
                        <li className="me-2"><Link href="#"><i className="fab fa-instagram" /></Link></li>
                        <li><Link href="#"><i className="fab fa-tiktok" /></Link></li>
                    </ul>
                </nav>
            </div>

            {/* Sticky header */}
            <div className={`sticky-header ${scroll ? "fixed-header animated slideInDown" : ""}`}>
                <div className="auto-container">
                    <div className="inner-container d-flex align-items-center justify-content-between">
                        <div className="logo-box">
                            <Link href="/" className="d-block position-relative" style={{ width: "120px", height: "80px" }}>
                                <Image
                                    src="/images/logo.png"
                                    alt="Logo"
                                    fill
                                    style={{ objectFit: "contain" }}
                                    priority
                                    quality={10}
                                />
                            </Link>
                        </div>
                        <nav className="main-menu">
                            <div className="navbar-collapse show collapse clearfix">
                                <Menu />
                            </div>
                        </nav>
                        <div className="d-flex align-items-center gap-3">
                            <LanguageSwitcher />
                            <div className="mobile-nav-toggler" onClick={handleMobileMenu}>
                                <span className="icon lnr-icon-bars" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    )
}
