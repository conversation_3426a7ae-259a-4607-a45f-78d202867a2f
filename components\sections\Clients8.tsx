"use client";

import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";

const swiperOptions = {
  modules: [Autoplay, Pagination, Navigation],
  slidesPerView: 6,
  spaceBetween: 30,
  navigation: true,
  autoplay: {
    delay: 3500,
    disableOnInteraction: false,
  },
  loop: true,
  breakpoints: {
    320: { slidesPerView: 1 },
    575: { slidesPerView: 1 },
    767: { slidesPerView: 2 },
    991: { slidesPerView: 3 },
    1199: { slidesPerView: 4 },
    1350: { slidesPerView: 6 },
  },
};

const clients = [
  { src: "/images/resource/client1-1.png", alt: "Client 1" },
  { src: "/images/resource/client1-2.png", alt: "Client 2" },
  { src: "/images/resource/client1-3.png", alt: "Client 3" },
  { src: "/images/resource/client1-4.png", alt: "Client 4" },
  { src: "/images/resource/client1-5.png", alt: "Client 5" },
  { src: "/images/resource/client1-1.png", alt: "Client 6" },
  { src: "/images/resource/client1-2.png", alt: "Client 7" },
];

const Clients8 = () => {
  return (
    <section className="clients-section pt-0 pb-0">
      <div className="auto-container">
        <Swiper {...swiperOptions} className="clients-carousel owl-carousel owl-theme">
          {clients.map((client, index) => (
            <SwiperSlide key={index} className="client-block">
              <div className="inner-box">
                <div className="image-box">
                  <figure className="image">
                    <Image
                      src={client.src}
                      alt={client.alt}
                      width={250}
                      height={120}
                      className="w-auto h-auto"
                    />
                  </figure>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
};

export default Clients8;
