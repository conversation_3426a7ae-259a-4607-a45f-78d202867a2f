
/*=== Default Form ===*/

@function form-control-height() {
  @return calc(2.25rem + 27px);
}
.form-control, .input-text {
    height: form-control-height();
    padding: 14px 30px;
    outline: 0;
    background-color: #f4f5f8;
    border: 1px solid #f4f5f8;
    color: #fff;
    font-size: 0.9rem;
    width: 100%;
    &::placeholder {
        color: #fff;
        opacity: 1;
    }
    &:-ms-input-placeholder {
        color: #fff;
    }
    &::-ms-input-placeholder {
        color: #fff;
    }
}
textarea {
    &.form-control {
        height: auto;
        padding-top: 15px;
        padding-bottom: 15px;
    }
}
