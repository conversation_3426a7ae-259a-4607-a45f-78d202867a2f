'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function Menu() {
  const pathname = usePathname()

  return (
    <ul className="navigation">
      <li className={pathname === '/' ? 'current' : ''}>
        <Link href="/">Accueil</Link>
      </li>

      <li className={pathname === '/about' ? 'current' : ''}>
        <Link href="/about">À propos</Link>
      </li>

      <li className={pathname === '/services' ? 'current' : ''}>
        <Link href="/services">Services</Link>
      </li>

      {/* <li className={`dropdown ${pathname.startsWith('/services') || pathname.startsWith('/service') ? 'current' : ''}`}>
        <Link href="/services">Services</Link>
        <ul>
          <li className={pathname === '/service1' ? 'current' : ''}>
            <Link href="/service1">Service 1</Link>
          </li>
          <li className={pathname === '/service-details' ? 'current' : ''}>
            <Link href="/service-details">Service 2</Link>
          </li>
        </ul>
      </li> */}

      <li className={pathname === '/gallery' ? 'current' : ''}>
        <Link href="/gallery">Galerie</Link>
      </li>

      <li className={pathname === '/blog' ? 'current' : ''}>
        <Link href="/blog">Blog</Link>
      </li>

      <li className={pathname === '/contact' ? 'current' : ''}>
        <Link href="/contact">Contact</Link>
      </li>
    </ul>
  )
}
