"use client";

import Link from "next/link";
import Image from "next/image";

const pricing = [
  {
    title: "Massage Relaxant du Visage",
    description: "Session de 10 à 100 minutes",
    price: "54 000 XAF",
    image: "/images/resource/price3-1.png",
    link: "/page-pricing",
  },
  {
    title: "Soin du Corps Complet",
    description: "Session de 20 à 120 minutes",
    price: "75 000 XAF",
    image: "/images/resource/price3-2.png",
    link: "/page-pricing",
  },
  {
    title: "Gommage Exfoliant",
    description: "Session de 15 à 60 minutes",
    price: "30 000 XAF",
    image: "/images/resource/price3-3.png",
    link: "/page-pricing",
  },
  {
    title: "Massage Thaï Traditionnel",
    description: "Session de 30 à 90 minutes",
    price: "65 000 XAF",
    image: "/images/resource/price3-4.png",
    link: "/page-pricing",
  },
  {
    title: "Soin Hydratant du Visage",
    description: "Session de 20 à 60 minutes",
    price: "40 000 XAF",
    image: "/images/resource/price3-5.png",
    link: "/page-pricing",
  },
  {
    title: "Modelage Amincissant",
    description: "Session de 30 à 90 minutes",
    price: "80 000 XAF",
    image: "/images/resource/price3-6.png",
    link: "/page-pricing",
  },
];

const Pricing8 = () => {
  return (
    <>
      <section className="pricing-section-three pull-up">
        <div className="outer-box">
          <div className="container pb-0 pt-0">
            <div className="sec-title text-center">
              <figure className="image">
                <Image src="/images/icons/icon1.png" alt="Icône" width={60} height={60} />
              </figure>
              <span className="sub-title">Tarifs</span>
              <h2 className="words-slide-up text-split">
                Nos Soins & Tarifications <br /> Chez Lavish Shape
              </h2>
            </div>

            <div className="row">
              {pricing.map((item, index) => (
                <div key={index} className="pricing-block-three col-lg-6">
                  <div className="inner-box">
                    <div className="image-box">
                      <figure className="image overlay-anim mb-0">
                        <Link href={item.link}>
                          <Image src={item.image} alt={item.title} width={600} height={400} />
                        </Link>
                      </figure>
                    </div>
                    <div className="content-box">
                      <div className="inner">
                        <h4 className="title">
                          <Link href={item.link}>{item.title}</Link>
                        </h4>
                        <div className="text">{item.description}</div>
                      </div>
                      <span className="price">{item.price}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center mt-4">
              <Link href="/booking" className="theme-btn btn-style-two">
                <span className="btn-title">Réserver Maintenant</span>
              </Link>
            </div>

          </div>
        </div>
      </section>
    </>
  );
};

export default Pricing8;
