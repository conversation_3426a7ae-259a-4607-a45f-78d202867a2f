import Link from "next/link";
import Image from "next/image";

const features = [
  {
    title: "Détente & Plaisir",
    icon: "/images/icons/icon-feature1.png",
    alt: "Relaxation",
    text: "There are many variations of passages of Lorem Ipsum available the majority have suffered alteration simply free text in some",
  },
  {
    title: "Soins aux Huiles",
    icon: "/images/icons/icon-feature2.png",
    alt: "Traitement aux huiles",
    text: "There are many variations of passages of Lorem Ipsum available the majority have suffered alteration simply free text in some",
  },
  {
    title: "Soins Corporels",
    icon: "/images/icons/icon-feature3.png",
    alt: "Soins corporels",
    text: "There are many variations of passages of Lorem Ipsum available the majority have suffered alteration simply free text in some",
  },
];

const Features8 = () => {
  return (
    <section className="features-section-two">
      <div className="container">
        <div className="outer-box">
          <div className="sec-title">
            <div className="row">
              <div className="col-xl-6">
                <figure className="image">
                  <Image src="/images/icons/icon1.png" alt="Icône" width={60} height={60} />
                </figure>
                <span className="sub-title">Produits Efficaces et Testés</span>
                <h2 className="words-slide-up text-split">Nos Atouts</h2>
              </div>
              <div className="col-xl-5 offset-xl-1">
                <div className="text">
                  Lorem ipsum dolor sit amet consectetur adipiscing elit phasellus porttitor elementum conubia ligula et arcu enim accumsan Ligula curabitur sollicitudin feugiat parturient urna odio hendrerit mollis.
                </div>
              </div>
            </div>
          </div>

          <div className="row">
            {features.map((feature, index) => (
              <div key={index} className="feature-block-two col-lg-4 col-md-6 col-sm-12">
                <div className="inner-box">
                  <figure className="icon mb-0">
                    <Image src={feature.icon} alt={feature.alt} width={120} height={120} />
                  </figure>
                  <h4 className="title">
                    {feature.title}
                  </h4>
                  <div className="text">{feature.text}</div>
                </div>
              </div>
            ))}
          </div>

        </div>
      </div>
    </section>
  );
};

export default Features8;
