/***

====================================================================
    Package Section
====================================================================

***/

.packages-section {
  padding: 93px 0 100px;
  position: relative;
  @include media-breakpoint-down(md){
    padding: 70px 0 100px;
  }
  .sec-title {
    margin-bottom: 86px;
    @include media-breakpoint-down(lg){
      margin-bottom: 50px;
    }
  }
  .package-pattrn2 {
    background-image: url(../images/resource/package-pattrn2.png);
    position: absolute;
    width: 369px;
    height: 530px;
    right: 0;
    top: 0;
    z-index: 1;
    pointer-events: none;
    @include media-breakpoint-down(xxl){
      display: none;
    }
  }
  .auto-container {
    max-width: 1780px;
  }

  .default-dots{
    .owl-dots {
      left: 0;
      position: absolute;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      bottom: -47px;
      .owl-dot {
        height: 12px;
        width: 12px;
        margin: 0 8.5px;
        background-color: transparent;
        border: 1px solid #707070;
        border-radius: 50%;
        @include transition(all 300ms ease);
        &.active {
          width: 15px;
          height: 15px;
          background-color: var(--theme-color1);
          border-color: var(--theme-color1);
          @include transition(all 300ms ease);
        }
      }
    }
  }
}

.package-block {
  .inner-box {
    position: relative;
    margin-bottom: 26px;
    &:hover {
      .image-box {
        .bg-image-two {
          transform: scale(1);
        }
      }
    }
    .image-box {
      margin-bottom: 56px;
      position: relative;
      .image {
        img {
          border-radius: 91% 61% 68% 84% / 73% 62% 89% 79%;
        }
      }
      .bg-image {
        bottom: auto;
        left: 0;
        top: 24px;
        right: auto;
        width: 100%;
        pointer-events: none;
        z-index: 2;
      }
      .bg-image-two {
        bottom: -13px;
        left: 113px;
        top: auto;
        right: auto;
        width: 161px;
        height: 273px;
        transform: scale(0);
        pointer-events: none;
        z-index: 2;
        @include transition(all 300ms ease);
      }
    }
  }
  .content-box {
    .name {
      color: var(--theme-color-dark);
      font-weight: 500;
      margin-bottom: 15px;
      &:hover {
        color: var(--theme-color1);
      }
    }
    .price {
      color: var(--theme-color1);
      font-size: 30px;
      font-weight: 500;
      font-family: var(--title-font);
    }
  }
}

/***

====================================================================
    Package Section Two
====================================================================

***/

.packages-section-two {
  padding: 120px 0 92px;
  position: relative;
  @include media-breakpoint-down(md){
    padding: 70px 0 100px;
  }
  .shape-2 {
    background-image: url(../images/icons/shape-2.png);
    position: absolute;
    width: 207px;
    height: 204px;
    bottom: -35px;
    left: 104px;
    z-index: 1;
    pointer-events: none;
    animation: fa-spin 40s infinite linear;
    @include media-breakpoint-down(xxl){
      display: none;
    }
  }
  .info-box {
    padding-top: 48px;
    padding-bottom: 40px;
    .text {
      color: var(--headings-color);
      font-size: 64px;
      font-weight: 600;
      font-family: var(--title-font);
      margin-bottom: 20px;
      display: inline-block;
      line-height: 1;
      margin-bottom: 34px;
      position: relative;
      &:after {
        background-color: var(--theme-color-dark);
        content: "";
        position: absolute;
        left: calc(100% + 10px);
        height: 2px;
        width: 128px;
        top: 41px;
      } 
    }
    .title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 29px;
    }
  }
}

.package-block-two {
  .inner-box {
    position: relative;
    margin-bottom: 26px;
    &:hover {
      .image-box {
        .bg-image-two {
          transform: scale(1);
        }
      }
      .content-box {
        &:before {
          height: calc(100% + 1px);
        }
        .name,
        .price {
          color: var(--theme-color-light);
        }
      }
    }
    .image-box {
      margin-bottom: 0;
      position: relative;
      .image {
        img {
          border-radius: 0;
          width: 100%;
        }
      }
    }
  }
  .content-box {
    border: 1px solid rgba(var(--theme-color-dark-rgb), .1);
    padding: 27px 0 35px;
    position: relative;
    z-index: 1;
    &:before {
      background-color: var(--theme-color1);
      content: "";
      position: absolute;
      width: 100%;
      height: 0;
      top: -1px;
      left: 0;
      z-index: 1;
      @include transition(all 300ms ease);
      z-index: -1;
    }
    .name {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 15px;
      &:hover {
        color: var(--theme-color1);
      }
    }
    .price {
      color: var(--theme-color1);
      font-size: 64px;
      font-weight: 600;
      font-family: var(--title-font);
      @include transition(all 300ms ease);
    }
  }
}

/***

====================================================================
    Package Section Three
====================================================================

***/

.packages-section-three {
  padding: 93px 0 100px;
  position: relative;
  @include media-breakpoint-down(md){
    padding: 70px 0 100px;
  }
  .outer-box {
    padding: 0 30px;
    @media (min-width: 1400px) {
      padding: 0 101px;
    }
  }
  &.home7-style{
    background-color: #F4ECDF;
  }
}

.package-block-three {
  .inner-box {
    background-color: #F4ECDF;
    position: relative;
    margin-bottom: 26px;
    text-align: center;
    &.style-two {
      background-color: #F2EFEA;
    }
    &:hover {
      .image-box {
        .bg-image-two {
          transform: scale(1);
        }
      }
      .content-box {
        .name {
          color: var(--theme-color-dark);
        }
      }
    }
    &:after {
      border: 1px dashed var(--theme-color1);
      content: "";
      position: absolute;
      top: 10px;
      left: 9px;
      right: 9px;
      bottom: 10px;
      z-index: 1;
      pointer-events: none;
      transition: all 300ms ease;
    }
    .image-box {
      margin-bottom: 0;
      position: relative;
      .image {
        img {
          border-radius: 0;
        }
      }
    }
  }
  .content-box {
    padding: 70px 50px;
    position: relative;
    z-index: 1;
    @include media-breakpoint-down(xxl){
      padding: 70px 25px;
    }
    &:before {
      background-image: url("../images/icons/icon-flower shape.png");
      content: "";
      position: absolute;
      width: 120px;
      height: 120px;
      left: calc(50% - 48px);
      top: 32px;
      z-index: -1;
      pointer-events: none;
    }
    .name {
      color: var(--theme-color1);
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 15px;
      position: relative;
      &:hover {
        color: var(--theme-color1);
      }
    }
    .price {
      color: var(--headings-color);
      display: block;
      font-size: 64px;
      font-weight: 600;
      font-family: var(--title-font);
      margin-bottom: 40px;
      @include transition(all 300ms ease);
    }
    .text {
      margin-bottom: 30px;
    }
  }
  &.home7-style{
    .inner-box{
      background-color: var(--theme-color-light);
    }
  }
}