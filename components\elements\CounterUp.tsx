'use client'

import { useState, FunctionComponent } from 'react'
import CountUp from "react-countup"
import ScrollTriggerLib from 'react-scroll-trigger'

const ScrollTrigger = ScrollTriggerLib as unknown as FunctionComponent<any>;

interface CounterUpProps {
  count: number
  time: number
}

export default function CounterUp({ count, time }: CounterUpProps) {
  const [counterOn, setCounterOn] = useState(false)

  return (
    <ScrollTrigger onEnter={() => setCounterOn(true)} onExit={() => setCounterOn(false)} component="span">
      <CountUp end={counterOn ? count : 0} duration={time} redraw={true}>
        {({ countUpRef }) => (
          <span ref={countUpRef}></span>
        )}
      </CountUp>
    </ScrollTrigger>
  )
}
