// "use client"

import Link from "next/link";
import React from 'react';
import Image from "next/image";

const Banner8 = () => {

  return (
    <>
      <section className="banner-section-three">
        <div className="shape-image-curve"></div>
        <div className="shape-image-leaf bounce-y"></div>
        <div className="shape-image8-1 bounce-y"></div>
        <div className="bg-image" style={{ backgroundImage: 'url(/images/main-slider/bg-slider3-1.png)' }}></div>

        <div className="banner-carousel-one owl-theme">
          <div className="slide-item">
            <div className="auto-container">
              <div className="float-text animate-5">LAVISH SHAPE</div>
              <div className="shape-image8-2 animate-6"></div>
              <div className="row">
                <div className="content-box col-xl-4 col-lg-4 col-md-12 col-sm-12">
                  <div className="text animate-3">
                    Chez <PERSON>, votre beauté est sublimée. <br />
                    Notre atmosphère apaisante rendra votre expérience inoubliable.
                  </div>
                </div>
                <div className="image-column col-xl-5 col-lg-4 col-md-12 col-sm-12">
                  <div className="image-box">
                    <figure className="image animate-3">
                      <Image
                        src="/images/main-slider/slide8-1.jpg"
                        alt="Lavish Shape"
                        width={500}
                        height={500}
                        priority
                        quality={90}
                        className=""
                      />
                      {/* <img src="images/main-slider/slide8-1.jpg" alt="Lavish Shape" /> */}
                    </figure>
                    <div className="text-rotate animate-5 bounce-">
                      <div className="icon-text-2"></div>
                      <Link href={'/booking'} className="play-btn-two">
                        <i className="icon fa-light fa-arrow-right-long" aria-hidden="true"></i>
                      </Link>
                    </div>
                  </div>
                </div>
                <div className="content-box col-xl-3 col-lg-4 col-md-12 col-sm-12">
                  <div className="inner-content">
                    <div className="text animate-3">
                      Votre bien-être est notre priorité. Vous allez adorer votre passage chez Lavish Shape.
                    </div>
                    <div className="btn-box animate-4">
                      <Link href="/booking" className="theme-btn btn-style-transparent">
                        <span className="btn-title">PRENDRE RENDEZ-VOUS</span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Banner8;
