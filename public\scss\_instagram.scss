/***

==================================================================
    Instagram Section
==================================================================

***/

.instagram-section {
    padding: 100px 0;
    position: relative;
    @include media-breakpoint-down(md){
        padding: 80px 0;
    }
    .icon-instagram1-6 {
        background-image: url(../images/resource/instagram1-6.png);
        position: absolute;
        width: 201px;
        height: 360px;
        right: 0;
        bottom: 51px;
        @include media-breakpoint-down(xxl){
            display: none;
        }
    }
    .icon-instagram1-7 {
        background-image: url(../images/resource/instagram1-7.png);
        position: absolute;
        width: 201px;
        height: 360px;
        left: 0;
        bottom: 51px;
        @include media-breakpoint-down(xxl){
            display: none;
        }
    }
    .sec-title {
        h4 {
            font-weight: 500;
            position: relative;
            &:before {
            border: 1px solid #1C1A1D;
            content: "";
            position: absolute;
            left: calc(50% + 170px);
            width: 36.84%;
            opacity: 10%;
            top: 18px;
            }
            &:after {
                border: 1px solid #1C1A1D;
                content: "";
                position: absolute;
                right: calc(50% + 170px);
                width: 36.84%;
                opacity: 10%;
                top: 18px;
            }
        }
    }
    .instagram-block {
        .inner-box {
            .image-box {
                align-items: center;
                display: flex;
                position: relative;
                justify-content: center;
                @include media-breakpoint-down(lg){
                    margin-bottom: 25px;
                }
                &:hover {
                    .image {
                        &:before {
                            transform: scale(1);
                        }
                        a {
                            img {
                                transform: scale(1.15);
                            }
                        }
                    }
                    .icon {
                        transform: scale(1);
                    }
                }
                .image {
                    border-radius: 11px;
                    margin-bottom: 0;
                    overflow: hidden;
                    width: 100%;
                    &:before {
                        border-radius: 11px;
                        background-color: rgba(194, 167, 78, .80);
                        content: "";
                        position: absolute;
                        left: 0;
                        top: 0;
                        height: 100%;
                        width: 100%;
                        transform: scale(0);
                        pointer-events: none;
                        z-index: 1;
                        @include transition(all 300ms ease);
                    }
                    a {
                        img {
                            width: 100%;
                            border-radius: 5px;
                            @include transition(all 300ms ease);
                        }
                    }
                }
                .icon {
                    color: var(--theme-color-light);
                    position: absolute;
                    pointer-events: none;
                    font-size: 68px;
                    z-index: 2;
                    transform: scale(0);
                    @include transition(all 300ms ease);
                }
            }
        }
    }
}


