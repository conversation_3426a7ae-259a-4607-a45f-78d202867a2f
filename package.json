{"name": "beauty-spa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@calcom/embed-react": "^1.5.3", "@tanstack/react-query": "^5.83.0", "@types/swiper": "^5.4.3", "axios": "^1.10.0", "lucide-react": "^0.525.0", "next": "15.3.5", "posthog-js": "^1.257.0", "prisma": "^6.11.1", "react": "^19.0.0", "react-circular-progressbar": "^2.1.0", "react-countup": "^6.5.3", "react-dom": "^19.0.0", "react-modal-video": "^2.0.2", "react-scroll-trigger": "^0.6.14", "react-slider": "^2.0.6", "swiper": "^11.2.10", "zustand": "^5.0.6"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal-video": "^1.2.3", "typescript": "^5"}}