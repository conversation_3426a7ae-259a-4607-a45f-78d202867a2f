import axios, { InternalAxiosRequestConfig } from 'axios';

// Create an Axios instance with default settings
const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_BASE_URL, // Replace with your API's base URL
});

// Add a request interceptor to include API key and token
apiClient.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        const apiKey = process.env.NEXT_PUBLIC_API_KEY; // Ensure you have this in your .env.local
        // const token = localStorage.getItem('token'); // Or wherever you're storing the token

        if (apiKey) {
            config.headers['api-key'] = apiKey;
        }

        // if (token) {
        //     config.headers['access-token'] = `${token}`;
        // }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Function to make GET requests
export const getData = async (endpoint: string, queryParams?: Record<string, any>) => {
    try {
        const response = await apiClient.get(endpoint, {
            withCredentials: true,
            params: queryParams, // Pass query parameters here
        });
        
        return response; // Return the data from the response
    } catch (error) {
        // Improved error logging
        if (axios.isAxiosError(error)) {
            console.error('GET request error:', {
                message: error.message,
                status: error.response?.status,
                data: error.response?.data,
                endpoint
            });
        } else {
            console.error('GET request error:', error);
        }
        throw error; // Rethrow the error to handle it in the component
    }
};


// Function to make POST requests
export const postData = async (endpoint: string, data: any) => {
    try {
        const response = await apiClient.post(endpoint, data, { withCredentials: true });
        return response.data; // Return the data from the response
    } catch (error) {
        console.error('POST request error:', error);
        throw error; // Rethrow the error to handle it in the component
    }
};

// Function to make PUT requests
export const putData = async (endpoint: string, data: any) => {
    try {
        const response = await apiClient.put(endpoint, data);
        return response.data; // Return the data from the response
    } catch (error) {
        console.error('PUT request error:', error);
        throw error; // Rethrow the error to handle it in the component
    }
};

// Function to make DELETE requests
export const deleteData = async (endpoint: string) => {
    try {
        const response = await apiClient.delete(endpoint);
        return response.data; // Return the data from the response
    } catch (error) {
        console.error('DELETE request error:', error);
        throw error; // Rethrow the error to handle it in the component
    }
};

