"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import Image from 'next/image'
const testimonials = [
  {
    rating: 5,
    text: "“ Cliente fidèle depuis l'ouverture, je suis pleinement satisfaite du professionnalisme et de la qualité de service de cet institut. Les résultats de mes séances d'épilation au laser m'ont véritablement impressionnée. Il est à noter que cet établissement est pionnier, voire le meilleur, dans le domaine de l'épilation au laser au Cameroun. ”",
    name: "<PERSON>",
  },
  {
    rating: 5,
    text: "“ Je suis ravie de mon expérience d'épilation au laser dans cet institut. L'équipe a été très professionnelle et attentionnée tout au long du processus. Les résultats sont incroyables, ma peau est plus lisse que jamais et je n'ai plus à m'inquiéter des poils indésirables. L'ambiance de l'institut est apaisante et le personnel m'a mise à l'aise dès mon arrivée. Je recommande vivement leurs services à tous ceux qui recherchent une solution efficace et durable. Merci encore pour ce traitement de qualité ! ”",
    name: "Diamant Virginia",
  },
  {
    rating: 5,
    text: "“ I was extremely impressed and satisfied!! Service is topnotch. Everything is on point and they are so welcoming. Definitely my new spot. ”",
    name: "San",
  },
];

const swiperOptions = {
  modules: [Autoplay, Pagination, Navigation],
  slidesPerView: 1,
  pagination: true,
  autoplay: {
    delay: 8000,
    disableOnInteraction: false,
  },
  loop: true,
};

const Testimonial8 = () => {
  return (
    <section className="testimonial-section style-three">
      <div
        className="bg bg-image"
        style={{ backgroundImage: "url(/images/background/bg-testimonial1.png)" }}
      ></div>
      <div className="instagram1-7 bounce-y"></div>
      <div className="testimonial-pattrn1-2 bounce-x"></div>

      <div className="auto-container">
        <div className="sec-title text-center">
          <figure className="image">
            <img src="images/icons/icon1.png" alt="Image" />
          </figure>
          <span className="sub-title">Témoignages</span>
          <h2 className="words-slide-up text-split">Ce que disent nos clients</h2>
          <p>Découvrez les avis vérifiés de nos clients sur Google.</p>
        </div>

        <div className="carousel-outer col-lg-8 offset-lg-2">
          <Swiper
            {...swiperOptions}
            className="testimonial-carousel-three owl-carousel owl-theme default-dots"
          >
            {testimonials.map((review, index) => (
              <SwiperSlide key={index} className="slide-item">
                <div className="testimonial-block">
                  <div className="inner-box text-center">
                    <div className="rating">
                      {Array.from({ length: review.rating }).map((_, idx) => (
                        <i key={idx} className="icon fa fa-star"></i>
                      ))}
                    </div>
                    <div className="text">{review.text}</div>
                    <div className="info-box">
                      <h4 className="name">{review.name}</h4>
                      {/* <span className="designation">{review.role}</span> */}
                    </div>
                    <Image src='/images/google.webp' height={30} width={30} alt="google icon" className="mt-4" />
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </section>
  );
};

export default Testimonial8;
