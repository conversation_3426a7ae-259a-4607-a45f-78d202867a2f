import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Message {
  sender: 'user' | 'bot';
  text: string;
}

interface UserInfo {
  id: string;
  name: string;
  email: string;
  phone: string;
}

interface ChatState {
  messages: Message[];
  userInfo: UserInfo;
  showForm: boolean;
  initialized: boolean;
  showFAQs: boolean;
  answeredQuestions: number[];
  allQuestionsAnswered: boolean;
  customMessageSent: boolean;
  addMessage: (message: Message) => void;
  setUserInfo: (info: UserInfo) => void;
  setShowForm: (show: boolean) => void;
  setInitialized: (init: boolean) => void;
  setShowFAQs: (show: boolean) => void;
  setAnsweredQuestions: (questionIds: number[]) => void;
  setAllQuestionsAnswered: (answered: boolean) => void;
  setCustomMessageSent: (sent: boolean) => void;
  clearChat: () => void;
}

export const useStore = create<ChatState>()(
  persist(
    (set) => ({
      messages: [],
      userInfo: { id: '', name: '', email: '', phone: '' },
      showForm: true,
      initialized: false,
      showFAQs: false,
      answeredQuestions: [],
      allQuestionsAnswered: false,
      customMessageSent: false,
      addMessage: (message) => set((state) => ({ 
        messages: [...state.messages, message] 
      })),
      setUserInfo: (info) => set({ userInfo: info }),
      setShowForm: (show) => set({ showForm: show }),
      setInitialized: (init) => set({ initialized: init }),
      setShowFAQs: (show) => set({ showFAQs: show }),
      setAnsweredQuestions: (questionIds) => set({ answeredQuestions: questionIds }),
      setAllQuestionsAnswered: (answered) => set({ allQuestionsAnswered: answered }),
      setCustomMessageSent: (sent) => set({ customMessageSent: sent }),
      clearChat: () => set({ 
        messages: [], 
        showForm: true, 
        initialized: false,
        showFAQs: false,
        answeredQuestions: [],
        allQuestionsAnswered: false,
        customMessageSent: false
      }),
    }),
    {
      name: 'chat-storage',
    }
  )
);




