"use client";

import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination, Scrollbar } from "swiper/modules";
import Image from "next/image";

const swiperOptions = {
  modules: [Autoplay, Pagination, Navigation, Scrollbar],
  slidesPerView: 4,
  spaceBetween: 30,
  scrollbar: { draggable: true, dragSize: 24 },
  autoplay: {
    delay: 2500,
    disableOnInteraction: false,
  },
  loop: true,
  breakpoints: {
    320: { slidesPerView: 1 },
    575: { slidesPerView: 1 },
    767: { slidesPerView: 2 },
    991: { slidesPerView: 3 },
    1199: { slidesPerView: 4 },
    1350: { slidesPerView: 4 },
  },
};

// Tableau des services
const services = [
  {
    title: "Relaxation Sauna",
    icon: "/images/icons/theme-icon5.png",
    bgImage1: "/images/resource/service1-1.png",
    bgImage2: "/images/resource/service1-2.png",
    link: "/",
  },
  {
    title: "Soins du Visage",
    icon: "/images/icons/theme-icon6.png",
    bgImage1: "/images/resource/service1-1.png",
    bgImage2: "/images/resource/service1-2.png",
    link: "/",
  },
  {
    title: "Maquillage Professionnel",
    icon: "/images/icons/theme-icon7.png",
    bgImage1: "/images/resource/service1-1.png",
    bgImage2: "/images/resource/service1-2.png",
    link: "/",
  },
  {
    title: "Massage Thaï",
    icon: "/images/icons/theme-icon8.png",
    bgImage1: "/images/resource/service1-1.png",
    bgImage2: "/images/resource/service1-2.png",
    link: "/",
  },
  {
    title: "Sculpture du Corps",
    icon: "/images/icons/theme-icon5.png",
    bgImage1: "/images/resource/service1-1.png",
    bgImage2: "/images/resource/service1-2.png",
    link: "/",
  },
];

const Services1 = () => {
  return (
    <>
      <section className="services-section mt-20">
        <div className="service1-pattrn1 bounce-y"></div>
        <div className="auto-container">
          <div className="outer-box">
            <div className="sec-title">
              <div className="row">
                <div className="col-xl-6">
                  <figure className="image">
                    <Image
                      src="/images/icons/icon1.png"
                      alt="Icône"
                      width={60}
                      height={60}
                    />
                  </figure>
                  <span className="sub-title">Nos Services</span>
                  <h2 className="words-slide-up text-split">
                    Découvrez Nos Soins Exclusifs chez Lavish Shape
                  </h2>
                </div>
                <div className="col-xl-5 offset-xl-1">
                  <div className="text">
                    Chez Lavish Shape, chaque service est conçu pour révéler votre beauté naturelle
                    et améliorer votre bien-être dans une atmosphère luxueuse et relaxante.
                  </div>
                </div>
              </div>
            </div>

            <Swiper {...swiperOptions} className="service-carousel owl-carousel owl-theme default-dots pb-0">
              {services.map((service, index) => (
                <SwiperSlide key={index}>
                  <div className="swiper-slide">
                    <div className="service-block">
                      <div className="inner-box">
                        <div className="image-box position-relative">
                          <div
                            className="bg-image"
                            style={{ backgroundImage: `url(${service.bgImage1})` }}
                          ></div>
                          <div
                            className="bg-image-two"
                            style={{ backgroundImage: `url(${service.bgImage2})` }}
                          ></div>
                        </div>
                        <div className="content-box">
                          <figure className="icon mb-0">
                            <Image
                              src={service.icon}
                              alt={service.title}
                              width={40}
                              height={40}
                            />
                          </figure>
                          <h4 className="title">
                            <Link href={service.link}>{service.title}</Link>
                          </h4>
                        </div>
                      </div>
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            <div className="text-center mt-4">
              <Link href="/booking" className="theme-btn btn-style-two">
                <span className="btn-title">Réserver Maintenant</span>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Services1;