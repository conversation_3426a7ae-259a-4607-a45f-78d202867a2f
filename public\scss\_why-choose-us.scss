/*** 

====================================================================
    Why Choose Us
====================================================================

***/

.why-choose-us {
  position: relative;
  padding: 0 0 54px;
  z-index: 1;
  @include media-breakpoint-down(xl){
    padding: 0;
  }
  .bg {
    background-size: auto;
    background-position: right bottom;
    bottom: -222px;
    pointer-events: none;
    top: auto;
    z-index: -1;
  }
  .icon-sailboat-line-1 {
    pointer-events: none;
    position: absolute;
    left: 0;
    bottom: 24px;
    z-index: -1;
  }
  .icon-wheel-compass-1 {
    position: absolute;
    top: -50px;
    right: 155px;
    animation: fa-spin 70s infinite;
    z-index: -1;
    @include media-breakpoint-down(xxl){
      opacity: .1;
    }
  }
  .sec-title-two {
    text-align: center;
    h2 {
      opacity: 0.2 !important;
      color: var(--text-color);
      font-family: var(--text-font);
      font-size: 120px;
      font-weight: 400;
      line-height: 1.3em;
      margin-bottom: 0;
      -webkit-text-fill-color: white;
      -webkit-text-stroke: 1px var(--text-color);
      @include media-breakpoint-down(lg){
        font-size: 80px;
      }
      @include media-breakpoint-down(md){
        font-size: 60px;
      }
      @include media-breakpoint-down(sm){
        font-size: 50px;
      }
    }
  }
  
  .content-column {
    position: relative;
    margin-bottom: 50px;
    @include media-breakpoint-down(xl){
      margin-bottom: 0;
    }
    .inner-column {
      position: relative;
    }
  }

  .image-column {
    position: relative;
    margin-bottom: 50px;
    .inner-column{
      position: relative;
      @include media-breakpoint-down(xl){
        padding-right: 0;
      }
      .image-box {
        position: relative;
        .image{
          position: relative;
          margin-bottom: 0;
          margin-top: -202px;
          z-index: -1;
          img {
            @include media-breakpoint-down(lg){
              display: none;
            }
          }
        }
        .image-2{
          position: absolute;
          top: 17px;
          left: 100px;
          margin-bottom: 0;
          @include media-breakpoint-down(lg){
            display: none;
          }
        }
        .image-3{
          position: absolute;
          bottom: 104px;
          right: 3px;
          margin-bottom: 0;
          @include media-breakpoint-down(lg){
            display: none;
          }
          img {
            outline: 10px solid var(--theme-color-light);
            outline-offset: -10px;
          }
        }
      }
    }
  }
}

.accordion-box {
  position: relative;
  .block {
    position: relative;
    background-color: var(--theme-color-light);
    .acc-btn {
      border-bottom: 1px solid #e1e1e1;
      position: relative;
      font-family: var(--title-font);
      font-size: 18px;
      line-height: 24px;
      color: var(--theme-color-dark);
      font-weight: 400;
      cursor: pointer;
      padding: 28px 20px 20px 40px;
      letter-spacing: -.01em;
      transition: all 500ms ease;
      .icon{
        position: absolute;
        top: 27px;
        left: 0;
        line-height: 28px;
        color: var(--theme-color1);
        font-size: 20px;
        @include transition(all 300ms ease);
      }
      .arrow {
        position: absolute;
        right: 10px;
        top: 30px;
        font-size: 20px;
        line-height: 1;
        font-weight: 400;
        color: var(--theme-color-dark);
        text-align: center;
        @include transition(all 300ms ease);
      }
    }
    .acc-btn.active {
      color: var(--theme-color-dark);
      .arrow {
        font-size: 20px;
        color: var(--theme-color-dark);
        transform: rotate(180deg);
        &:before {
          content: "\f068";
        }
      }
      .icon{
        transform: scale(-1) rotate(180deg);
      }
    }
    .acc-content {
      font-size: 14px;
      position: relative;
      display: none;
      .content {
        position: relative;
        padding: 11px 30px 3px 0;
        .text {
          display: block;
          font-size: 14px;
          margin-bottom: 0;
        }
      }
    }
    .acc-content.current {
      display: block;
    }
  }
  &.style-two{
    .block {
      position: relative;
      border: none;
      width: 100%;
      padding: 7px 17px 10px;
      margin-bottom: 12px;
      box-shadow: 0  0 43px rgba(0, 0, 0, 0.06);
      background-color: var(--theme-color-light);
      @include media-breakpoint-down(lg){
        padding: 7px 10px 10px;
      }
      .acc-btn {
        font-weight: 700;
        @include media-breakpoint-down(sm){
          padding-left: 70px;
        }
      }
      .arrow {
        right: 4px;
      }
      .content {
        padding: 10px 0 10px;
        .text {
          font-size: 16px;
          line-height: 30px;
          letter-spacing: -.01em;
          color: #7a7a7a;
          @include media-breakpoint-down(lg){
            line-height: 24px;
            margin-top: 5px;
          }
        }
      }
      .acc-btn.active {
        color: var(--theme-color1);
        .arrow,
        .icon {
          color: var(--theme-color1);
        }
        .count {
          background-color: var(--theme-color1);
          color: var(--theme-color-light);
        }
      }
    }
    .count {
      position: absolute;
      top: 0;
      left: 0;
      width: 57px;
      height: 57px;
      line-height: 57px;
      color: #a6a6a6;
      background-color: #f2f3f6;
      text-align: center;
      @include media-breakpoint-down(sm){
        top: 5px;
      }
    }
  }
}


/*** 

====================================================================
    Why Choose Us Two
====================================================================

***/

.why-choose-us-two {
  background-color: #fff;
  padding: 150px 0;
  position: relative;
  z-index: 2;
  @include media-breakpoint-down(xl){
    padding: 100px 0;
  }
  @include media-breakpoint-down(lg){
    padding: 100px 0 80px;
  }
  .sec-title {
    .text {
      margin-top: 16px;
    }
  }
  .bg-image {
    position: absolute;
    top: 0;
    right: 0;
    left: auto;
    height: 100%;
    width: 56%;
    z-index: -1;
  }
  .content-column {
    position: relative;
    &:before {
      content: "";
      position: absolute;
      max-height: 621px;
      width: 100px;
      background-color: #bfa888;
      left: -28px;
      top: -45px;
      height: 116.7%;
      @include media-breakpoint-down(xl){
        max-height: 690px;
      }
    }
    .inner-column {
      background-color: #fff;
      padding: 64px 133px 80px 61px;
      position: relative;
      z-index: 1;
      @include media-breakpoint-down(xl){
        padding: 60px 60px 60px 50px;
      }
      @include media-breakpoint-down(sm){
        padding: 60px 30px 60px 30px;
      }
    }
  }
  .inner-column {
    .text-box {
      text {
        font-size: 200px;
        font-family: Allison;
      }
    }
  }
  .icon-big-boat-2 {
    position: absolute;
    left: -45px;
    bottom: 3px;
    z-index: -1;
    @include media-breakpoint-down(xl){
      display: none;
    }
  }
  .content-column-two {
    align-items: center;
    position: relative;
    display: flex;
    justify-content: flex-end;
    @include media-breakpoint-down(lg){
      display: none;
    }
    .text {
      color: white;
      font-size: 250px;
      font-family: Allison;
      text-align: end;
      letter-spacing: 10px;
      position: relative;
      left: 100px;
      top: -100px;
      opacity: 0.5;
      @include media-breakpoint-down(xl){
        font-size: 150px;
        left: -70px;
      }
    }
  }
}

/*** 

====================================================================
    Why Choose Us Three
====================================================================

***/

.why-choose-us-three {
  position: relative;
  padding: 100px 0;
  z-index: 1;
  .icon-big-boat-5 {
    position: absolute;
    right: 80px;
    bottom: 105px;
    pointer-events: none;
    z-index: -1;
    @include media-breakpoint-down(xxl){
      display: none;
    }
  }
  .bg {
    background-size: auto;
    background-position: left bottom;
    bottom: 0;
    pointer-events: none;
    top: auto;
    z-index: -1;
    @include media-breakpoint-down(xl){
      background-size: cover;
    }
  }
  .icon-sailboat-line-1 {
    pointer-events: none;
    position: absolute;
    left: 0;
    bottom: 24px;
    z-index: -1;
  }
  .icon-wheel-compass-1 {
    position: absolute;
    top: -50px;
    right: 155px;
    animation: fa-spin 70s infinite;
    z-index: -1;
  }
  .sec-title-two {
    text-align: center;
    h2 {
      opacity: 0.2 !important;
      color: var(--text-color);
      font-family: var(--text-font);
      font-size: 120px;
      font-weight: 400;
      line-height: 1.3em;
      margin-bottom: 0;
      -webkit-text-fill-color: white;
      -webkit-text-stroke: 1px var(--text-color);
    }
  }
  
  .content-column {
    position: relative;
    .inner-column {
      position: relative;
      background-color: #f4efec;
      padding: 81px 98px 92px 98px;
      @include media-breakpoint-down(md){
        padding: 60px 35px 55px 39px;
      }
      &:before {
        border: 1px solid var(--theme-color1);
        content: "";
        position: absolute;
        left: 18px;
        top: 18px;
        right: 18px;
        bottom: 18px;
      }

      .bg {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 100%;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        z-index: 1;
      }
    }
  }
}

.accordion-box-three {
  background-color: transparent;
  position: relative;
  .block {
    background-color: transparent;
    position: relative;
    .acc-btn {
      border-bottom: 1px solid #e1e1e1;
      position: relative;
      font-family: var(--title-font);
      font-size: 18px;
      line-height: 24px;
      color: var(--theme-color-dark);
      font-weight: 400;
      cursor: pointer;
      padding: 28px 20px 20px 40px;
      letter-spacing: -.01em;
      transition: all 500ms ease;
      .icon{
        position: absolute;
        top: 27px;
        left: 0;
        line-height: 28px;
        color: var(--theme-color1);
        font-size: 20px;
        @include transition(all 300ms ease);
      }
      .arrow {
        position: absolute;
        right: 10px;
        top: 30px;
        font-size: 20px;
        line-height: 1;
        font-weight: 400;
        color: var(--theme-color-dark);
        text-align: center;
        @include transition(all 300ms ease);
      }
    }
    .acc-btn.active {
      color: var(--theme-color-dark);
      .arrow {
        font-size: 20px;
        color: var(--theme-color-dark);
        transform: rotate(180deg);
        &:before {
          content: "\f068";
        }
      }
      .icon{
        transform: scale(-1) rotate(180deg);
      }
    }
    .acc-content {
      font-size: 14px;
      position: relative;
      display: none;
      .content {
        position: relative;
        padding: 11px 30px 3px 0;
        .text {
          display: block;
          font-size: 14px;
          margin-bottom: 0;
        }
      }
    }
    .acc-content.current {
      display: block;
    }
  }
  &.style-two{
    .block {
      position: relative;
      border: none;
      width: 100%;
      padding: 7px 17px 10px;
      margin-bottom: 12px;
      box-shadow: 0  0 43px rgba(0, 0, 0, 0.06);
      background-color: var(--theme-color-light);
      .acc-btn {
        padding-left: 92px;
        font-weight: 700;
      }
      .arrow {
        right: 4px;
      }
      .content {
        padding: 10px 0 10px;
        .text {
          font-size: 16px;
          line-height: 30px;
          letter-spacing: -.01em;
          color: #7a7a7a;
        }
      }
      .acc-btn.active {
        color: var(--theme-color1);
        .arrow,
        .icon {
          color: var(--theme-color1);
        }
        .count {
          background-color: var(--theme-color1);
          color: var(--theme-color-light);
        }
      }
    }
    .count {
      position: absolute;
      top: 0;
      left: 0;
      width: 57px;
      height: 57px;
      line-height: 57px;
      color: #a6a6a6;
      background-color: #f2f3f6;
      text-align: center;
    }
  }
}