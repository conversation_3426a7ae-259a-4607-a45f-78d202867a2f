// app/layout.tsx or app/RootLayout.tsx
import Script from 'next/script';
import '../public/css/bootstrap.min.css';
import '../public/css/style.css';
import { plusJakarta, cormorant, alexBrush } from './fonts';
import Preloader from '@/components/elements/Preloader';
import { ReactQueryProvider } from '@/providers/ReactQueryProvider';

export const metadata = {
  title: "Lavish Shape & Glow Med Spa – Spa & soins esthétiques",
  description:
    "Lavish Shape & Glow Med Spa, centre de beauté haut de gamme, propose des soins esthétiques avancés et une expérience relaxante.",
  openGraph: {
    title: "Lavish Shape & Glow Med Spa",
    description:
      "Centre de beauté et spa haut de gamme proposant des soins esthétiques avancés.",
    url: "https://www.lavishshapemedspa.com",
    siteName: "Lavish Shape & Glow Med Spa",
    // images: [
    //   {
    //     url: "https://www.lavishshapemedspa.com/images/og-image.jpg",
    //     width: 1200,
    //     height: 630,
    //   },
    // ],
    locale: "en_US",
    type: "website",
  },
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon.ico",
    apple: "/favicon.ico",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${plusJakarta.variable} ${cormorant.variable} ${alexBrush.variable}`}>
      <ReactQueryProvider>
        <body>
          <Preloader>
            {children}
          </Preloader>
          <Script
            src="/assets/js/smoothscroll.js"
            strategy="afterInteractive"
          />
        </body>
      </ReactQueryProvider>
    </html>
  );
}
